import { useTranslation } from 'react-i18next';
import { Star, Globe, ShoppingCart, Users, TrendingUp } from 'lucide-react';

const Partners = () => {
  const { t } = useTranslation();

  const partners = [
    {
      id: 'amazon',
      name: 'Amazon',
      logo: '🛒',
      description: t('partner_amazon_desc'),
      region: 'Global',
      category: 'E-commerce',
      stats: {
        years: '5+',
        products: '10,000+',
        rating: '4.8/5'
      },
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50'
    },
    {
      id: 'shopee',
      name: 'Shopee',
      logo: '🛍️',
      description: t('partner_shopee_desc'),
      region: 'Southeast Asia',
      category: 'E-commerce',
      stats: {
        years: '4+',
        products: '8,000+',
        rating: '4.9/5'
      },
      color: 'bg-red-500',
      bgColor: 'bg-red-50'
    },
    {
      id: 'tiktok',
      name: 'TikTok Shop',
      logo: '🎵',
      description: t('partner_tiktok_desc'),
      region: 'Global',
      category: 'Social Commerce',
      stats: {
        years: '2+',
        products: '5,000+',
        rating: '4.7/5'
      },
      color: 'bg-black',
      bgColor: 'bg-gray-50'
    },
    {
      id: 'temu',
      name: 'Temu',
      logo: '🏪',
      description: t('partner_temu_desc'),
      region: 'Global',
      category: 'Marketplace',
      stats: {
        years: '2+',
        products: '6,000+',
        rating: '4.6/5'
      },
      color: 'bg-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      id: 'alibaba',
      name: 'Alibaba',
      logo: '🌐',
      description: t('partner_alibaba_desc'),
      region: 'Global B2B',
      category: 'B2B Platform',
      stats: {
        years: '8+',
        products: '15,000+',
        rating: '4.8/5'
      },
      color: 'bg-orange-600',
      bgColor: 'bg-orange-50'
    },
    {
      id: 'lazada',
      name: 'Lazada',
      logo: '🛒',
      description: t('partner_lazada_desc'),
      region: 'Southeast Asia',
      category: 'E-commerce',
      stats: {
        years: '3+',
        products: '4,000+',
        rating: '4.5/5'
      },
      color: 'bg-purple-600',
      bgColor: 'bg-purple-50'
    }
  ];

  const achievements = [
    {
      icon: ShoppingCart,
      number: '50,000+',
      label: t('partners_products_sold'),
      color: 'text-blue-600'
    },
    {
      icon: Users,
      number: '2M+',
      label: t('partners_customers_served'),
      color: 'text-green-600'
    },
    {
      icon: Globe,
      number: '50+',
      label: t('partners_countries_reached'),
      color: 'text-purple-600'
    },
    {
      icon: TrendingUp,
      number: '98%',
      label: t('partners_satisfaction_rate'),
      color: 'text-orange-600'
    }
  ];

  return (
    <section id="partners" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('partners_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-6">
            {t('partners_subtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('partners_intro')}
          </p>
        </div>

        {/* Partners Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {partners.map((partner) => (
            <div key={partner.id} className={`${partner.bgColor} rounded-lg p-6 hover:shadow-lg transition-all duration-300 border border-gray-200`}>
              {/* Partner Header */}
              <div className="flex items-center mb-4">
                <div className="text-4xl mr-4">{partner.logo}</div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{partner.name}</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span className={`px-2 py-1 rounded-full text-white text-xs ${partner.color}`}>
                      {partner.category}
                    </span>
                    <span>•</span>
                    <span>{partner.region}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-gray-700 mb-4 text-sm leading-relaxed">
                {partner.description}
              </p>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="font-bold text-lg text-gray-900">{partner.stats.years}</div>
                  <div className="text-xs text-gray-600">{t('partners_years_cooperation')}</div>
                </div>
                <div>
                  <div className="font-bold text-lg text-gray-900">{partner.stats.products}</div>
                  <div className="text-xs text-gray-600">{t('partners_products')}</div>
                </div>
                <div>
                  <div className="font-bold text-lg text-gray-900 flex items-center justify-center">
                    <Star className="h-4 w-4 text-yellow-500 mr-1" />
                    {partner.stats.rating}
                  </div>
                  <div className="text-xs text-gray-600">{t('partners_rating')}</div>
                </div>
              </div>

              {/* Compliance Badge */}
              <div className="mt-4 text-center">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ✓ {t('partners_compliant')}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Achievements Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white mb-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2">{t('partners_achievements_title')}</h3>
            <p className="text-blue-100">{t('partners_achievements_subtitle')}</p>
          </div>
          <div className="grid md:grid-cols-4 gap-8 text-center">
            {achievements.map((achievement, index) => {
              const IconComponent = achievement.icon;
              return (
                <div key={index} className="flex flex-col items-center">
                  <div className="bg-white bg-opacity-20 rounded-full p-4 mb-4">
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold mb-2">{achievement.number}</div>
                  <div className="text-blue-100 text-sm">{achievement.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Ethical Partnership Statement */}
        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('partners_ethics_title')}
          </h3>
          <p className="text-lg text-gray-700 mb-6 max-w-4xl mx-auto">
            {t('partners_ethics_content')}
          </p>
          
          {/* Key Commitments */}
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-green-600 text-2xl mb-3">🛡️</div>
              <h4 className="font-semibold text-gray-900 mb-2">{t('partners_commitment_quality')}</h4>
              <p className="text-sm text-gray-600">{t('partners_commitment_quality_desc')}</p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-blue-600 text-2xl mb-3">🤝</div>
              <h4 className="font-semibold text-gray-900 mb-2">{t('partners_commitment_ethics')}</h4>
              <p className="text-sm text-gray-600">{t('partners_commitment_ethics_desc')}</p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-purple-600 text-2xl mb-3">🌍</div>
              <h4 className="font-semibold text-gray-900 mb-2">{t('partners_commitment_global')}</h4>
              <p className="text-sm text-gray-600">{t('partners_commitment_global_desc')}</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            {t('partners_cta_title')}
          </h3>
          <p className="text-gray-600 mb-6">
            {t('partners_cta_content')}
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
            {t('partners_cta_button')}
          </button>
        </div>
      </div>
    </section>
  );
};

export default Partners;
