(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&s(m)}).observe(document,{childList:!0,subtree:!0});function c(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(o){if(o.ep)return;o.ep=!0;const d=c(o);fetch(o.href,d)}})();var Ac={exports:{}},qn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bd;function gp(){if(Bd)return qn;Bd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function c(s,o,d){var m=null;if(d!==void 0&&(m=""+d),o.key!==void 0&&(m=""+o.key),"key"in o){d={};for(var p in o)p!=="key"&&(d[p]=o[p])}else d=o;return o=d.ref,{$$typeof:r,type:s,key:m,ref:o!==void 0?o:null,props:d}}return qn.Fragment=u,qn.jsx=c,qn.jsxs=c,qn}var qd;function mp(){return qd||(qd=1,Ac.exports=gp()),Ac.exports}var D=mp(),Nc={exports:{}},lt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function pp(){if(Yd)return lt;Yd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),_=Symbol.for("react.lazy"),z=Symbol.iterator;function B(b){return b===null||typeof b!="object"?null:(b=z&&b[z]||b["@@iterator"],typeof b=="function"?b:null)}var q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,Z={};function F(b,M,Y){this.props=b,this.context=M,this.refs=Z,this.updater=Y||q}F.prototype.isReactComponent={},F.prototype.setState=function(b,M){if(typeof b!="object"&&typeof b!="function"&&b!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,b,M,"setState")},F.prototype.forceUpdate=function(b){this.updater.enqueueForceUpdate(this,b,"forceUpdate")};function xt(){}xt.prototype=F.prototype;function ft(b,M,Y){this.props=b,this.context=M,this.refs=Z,this.updater=Y||q}var st=ft.prototype=new xt;st.constructor=ft,j(st,F.prototype),st.isPureReactComponent=!0;var ot=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},St=Object.prototype.hasOwnProperty;function mt(b,M,Y,L,V,at){return Y=at.ref,{$$typeof:r,type:b,key:M,ref:Y!==void 0?Y:null,props:at}}function G(b,M){return mt(b.type,M,void 0,void 0,void 0,b.props)}function et(b){return typeof b=="object"&&b!==null&&b.$$typeof===r}function dt(b){var M={"=":"=0",":":"=2"};return"$"+b.replace(/[=:]/g,function(Y){return M[Y]})}var Dt=/\/+/g;function Ct(b,M){return typeof b=="object"&&b!==null&&b.key!=null?dt(""+b.key):M.toString(36)}function Bt(){}function Ut(b){switch(b.status){case"fulfilled":return b.value;case"rejected":throw b.reason;default:switch(typeof b.status=="string"?b.then(Bt,Bt):(b.status="pending",b.then(function(M){b.status==="pending"&&(b.status="fulfilled",b.value=M)},function(M){b.status==="pending"&&(b.status="rejected",b.reason=M)})),b.status){case"fulfilled":return b.value;case"rejected":throw b.reason}}throw b}function Rt(b,M,Y,L,V){var at=typeof b;(at==="undefined"||at==="boolean")&&(b=null);var I=!1;if(b===null)I=!0;else switch(at){case"bigint":case"string":case"number":I=!0;break;case"object":switch(b.$$typeof){case r:case u:I=!0;break;case _:return I=b._init,Rt(I(b._payload),M,Y,L,V)}}if(I)return V=V(b),I=L===""?"."+Ct(b,0):L,ot(V)?(Y="",I!=null&&(Y=I.replace(Dt,"$&/")+"/"),Rt(V,M,Y,"",function(re){return re})):V!=null&&(et(V)&&(V=G(V,Y+(V.key==null||b&&b.key===V.key?"":(""+V.key).replace(Dt,"$&/")+"/")+I)),M.push(V)),1;I=0;var pt=L===""?".":L+":";if(ot(b))for(var Nt=0;Nt<b.length;Nt++)L=b[Nt],at=pt+Ct(L,Nt),I+=Rt(L,M,Y,at,V);else if(Nt=B(b),typeof Nt=="function")for(b=Nt.call(b),Nt=0;!(L=b.next()).done;)L=L.value,at=pt+Ct(L,Nt++),I+=Rt(L,M,Y,at,V);else if(at==="object"){if(typeof b.then=="function")return Rt(Ut(b),M,Y,L,V);throw M=String(b),Error("Objects are not valid as a React child (found: "+(M==="[object Object]"?"object with keys {"+Object.keys(b).join(", ")+"}":M)+"). If you meant to render a collection of children, use an array instead.")}return I}function N(b,M,Y){if(b==null)return b;var L=[],V=0;return Rt(b,L,"","",function(at){return M.call(Y,at,V++)}),L}function H(b){if(b._status===-1){var M=b._result;M=M(),M.then(function(Y){(b._status===0||b._status===-1)&&(b._status=1,b._result=Y)},function(Y){(b._status===0||b._status===-1)&&(b._status=2,b._result=Y)}),b._status===-1&&(b._status=0,b._result=M)}if(b._status===1)return b._result.default;throw b._result}var U=typeof reportError=="function"?reportError:function(b){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var M=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof b=="object"&&b!==null&&typeof b.message=="string"?String(b.message):String(b),error:b});if(!window.dispatchEvent(M))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",b);return}console.error(b)};function ht(){}return lt.Children={map:N,forEach:function(b,M,Y){N(b,function(){M.apply(this,arguments)},Y)},count:function(b){var M=0;return N(b,function(){M++}),M},toArray:function(b){return N(b,function(M){return M})||[]},only:function(b){if(!et(b))throw Error("React.Children.only expected to receive a single React element child.");return b}},lt.Component=F,lt.Fragment=c,lt.Profiler=o,lt.PureComponent=ft,lt.StrictMode=s,lt.Suspense=y,lt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,lt.__COMPILER_RUNTIME={__proto__:null,c:function(b){return J.H.useMemoCache(b)}},lt.cache=function(b){return function(){return b.apply(null,arguments)}},lt.cloneElement=function(b,M,Y){if(b==null)throw Error("The argument must be a React element, but you passed "+b+".");var L=j({},b.props),V=b.key,at=void 0;if(M!=null)for(I in M.ref!==void 0&&(at=void 0),M.key!==void 0&&(V=""+M.key),M)!St.call(M,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&M.ref===void 0||(L[I]=M[I]);var I=arguments.length-2;if(I===1)L.children=Y;else if(1<I){for(var pt=Array(I),Nt=0;Nt<I;Nt++)pt[Nt]=arguments[Nt+2];L.children=pt}return mt(b.type,V,void 0,void 0,at,L)},lt.createContext=function(b){return b={$$typeof:m,_currentValue:b,_currentValue2:b,_threadCount:0,Provider:null,Consumer:null},b.Provider=b,b.Consumer={$$typeof:d,_context:b},b},lt.createElement=function(b,M,Y){var L,V={},at=null;if(M!=null)for(L in M.key!==void 0&&(at=""+M.key),M)St.call(M,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&(V[L]=M[L]);var I=arguments.length-2;if(I===1)V.children=Y;else if(1<I){for(var pt=Array(I),Nt=0;Nt<I;Nt++)pt[Nt]=arguments[Nt+2];V.children=pt}if(b&&b.defaultProps)for(L in I=b.defaultProps,I)V[L]===void 0&&(V[L]=I[L]);return mt(b,at,void 0,void 0,null,V)},lt.createRef=function(){return{current:null}},lt.forwardRef=function(b){return{$$typeof:p,render:b}},lt.isValidElement=et,lt.lazy=function(b){return{$$typeof:_,_payload:{_status:-1,_result:b},_init:H}},lt.memo=function(b,M){return{$$typeof:g,type:b,compare:M===void 0?null:M}},lt.startTransition=function(b){var M=J.T,Y={};J.T=Y;try{var L=b(),V=J.S;V!==null&&V(Y,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(ht,U)}catch(at){U(at)}finally{J.T=M}},lt.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},lt.use=function(b){return J.H.use(b)},lt.useActionState=function(b,M,Y){return J.H.useActionState(b,M,Y)},lt.useCallback=function(b,M){return J.H.useCallback(b,M)},lt.useContext=function(b){return J.H.useContext(b)},lt.useDebugValue=function(){},lt.useDeferredValue=function(b,M){return J.H.useDeferredValue(b,M)},lt.useEffect=function(b,M,Y){var L=J.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(b,M)},lt.useId=function(){return J.H.useId()},lt.useImperativeHandle=function(b,M,Y){return J.H.useImperativeHandle(b,M,Y)},lt.useInsertionEffect=function(b,M){return J.H.useInsertionEffect(b,M)},lt.useLayoutEffect=function(b,M){return J.H.useLayoutEffect(b,M)},lt.useMemo=function(b,M){return J.H.useMemo(b,M)},lt.useOptimistic=function(b,M){return J.H.useOptimistic(b,M)},lt.useReducer=function(b,M,Y){return J.H.useReducer(b,M,Y)},lt.useRef=function(b){return J.H.useRef(b)},lt.useState=function(b){return J.H.useState(b)},lt.useSyncExternalStore=function(b,M,Y){return J.H.useSyncExternalStore(b,M,Y)},lt.useTransition=function(){return J.H.useTransition()},lt.version="19.1.0",lt}var Gd;function Vc(){return Gd||(Gd=1,Nc.exports=pp()),Nc.exports}var bt=Vc(),zc={exports:{}},Yn={},Rc={exports:{}},Mc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function yp(){return Vd||(Vd=1,function(r){function u(N,H){var U=N.length;N.push(H);t:for(;0<U;){var ht=U-1>>>1,b=N[ht];if(0<o(b,H))N[ht]=H,N[U]=b,U=ht;else break t}}function c(N){return N.length===0?null:N[0]}function s(N){if(N.length===0)return null;var H=N[0],U=N.pop();if(U!==H){N[0]=U;t:for(var ht=0,b=N.length,M=b>>>1;ht<M;){var Y=2*(ht+1)-1,L=N[Y],V=Y+1,at=N[V];if(0>o(L,U))V<b&&0>o(at,L)?(N[ht]=at,N[V]=U,ht=V):(N[ht]=L,N[Y]=U,ht=Y);else if(V<b&&0>o(at,U))N[ht]=at,N[V]=U,ht=V;else break t}}return H}function o(N,H){var U=N.sortIndex-H.sortIndex;return U!==0?U:N.id-H.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;r.unstable_now=function(){return d.now()}}else{var m=Date,p=m.now();r.unstable_now=function(){return m.now()-p}}var y=[],g=[],_=1,z=null,B=3,q=!1,j=!1,Z=!1,F=!1,xt=typeof setTimeout=="function"?setTimeout:null,ft=typeof clearTimeout=="function"?clearTimeout:null,st=typeof setImmediate<"u"?setImmediate:null;function ot(N){for(var H=c(g);H!==null;){if(H.callback===null)s(g);else if(H.startTime<=N)s(g),H.sortIndex=H.expirationTime,u(y,H);else break;H=c(g)}}function J(N){if(Z=!1,ot(N),!j)if(c(y)!==null)j=!0,St||(St=!0,Ct());else{var H=c(g);H!==null&&Rt(J,H.startTime-N)}}var St=!1,mt=-1,G=5,et=-1;function dt(){return F?!0:!(r.unstable_now()-et<G)}function Dt(){if(F=!1,St){var N=r.unstable_now();et=N;var H=!0;try{t:{j=!1,Z&&(Z=!1,ft(mt),mt=-1),q=!0;var U=B;try{e:{for(ot(N),z=c(y);z!==null&&!(z.expirationTime>N&&dt());){var ht=z.callback;if(typeof ht=="function"){z.callback=null,B=z.priorityLevel;var b=ht(z.expirationTime<=N);if(N=r.unstable_now(),typeof b=="function"){z.callback=b,ot(N),H=!0;break e}z===c(y)&&s(y),ot(N)}else s(y);z=c(y)}if(z!==null)H=!0;else{var M=c(g);M!==null&&Rt(J,M.startTime-N),H=!1}}break t}finally{z=null,B=U,q=!1}H=void 0}}finally{H?Ct():St=!1}}}var Ct;if(typeof st=="function")Ct=function(){st(Dt)};else if(typeof MessageChannel<"u"){var Bt=new MessageChannel,Ut=Bt.port2;Bt.port1.onmessage=Dt,Ct=function(){Ut.postMessage(null)}}else Ct=function(){xt(Dt,0)};function Rt(N,H){mt=xt(function(){N(r.unstable_now())},H)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(N){N.callback=null},r.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<N?Math.floor(1e3/N):5},r.unstable_getCurrentPriorityLevel=function(){return B},r.unstable_next=function(N){switch(B){case 1:case 2:case 3:var H=3;break;default:H=B}var U=B;B=H;try{return N()}finally{B=U}},r.unstable_requestPaint=function(){F=!0},r.unstable_runWithPriority=function(N,H){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var U=B;B=N;try{return H()}finally{B=U}},r.unstable_scheduleCallback=function(N,H,U){var ht=r.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ht+U:ht):U=ht,N){case 1:var b=-1;break;case 2:b=250;break;case 5:b=1073741823;break;case 4:b=1e4;break;default:b=5e3}return b=U+b,N={id:_++,callback:H,priorityLevel:N,startTime:U,expirationTime:b,sortIndex:-1},U>ht?(N.sortIndex=U,u(g,N),c(y)===null&&N===c(g)&&(Z?(ft(mt),mt=-1):Z=!0,Rt(J,U-ht))):(N.sortIndex=b,u(y,N),j||q||(j=!0,St||(St=!0,Ct()))),N},r.unstable_shouldYield=dt,r.unstable_wrapCallback=function(N){var H=B;return function(){var U=B;B=H;try{return N.apply(this,arguments)}finally{B=U}}}}(Mc)),Mc}var kd;function vp(){return kd||(kd=1,Rc.exports=yp()),Rc.exports}var Dc={exports:{}},le={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function bp(){if(Qd)return le;Qd=1;var r=Vc();function u(y){var g="https://react.dev/errors/"+y;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var _=2;_<arguments.length;_++)g+="&args[]="+encodeURIComponent(arguments[_])}return"Minified React error #"+y+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(u(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(y,g,_){var z=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:z==null?null:""+z,children:y,containerInfo:g,implementation:_}}var m=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(y,g){if(y==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return le.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,le.createPortal=function(y,g){var _=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(u(299));return d(y,g,null,_)},le.flushSync=function(y){var g=m.T,_=s.p;try{if(m.T=null,s.p=2,y)return y()}finally{m.T=g,s.p=_,s.d.f()}},le.preconnect=function(y,g){typeof y=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,s.d.C(y,g))},le.prefetchDNS=function(y){typeof y=="string"&&s.d.D(y)},le.preinit=function(y,g){if(typeof y=="string"&&g&&typeof g.as=="string"){var _=g.as,z=p(_,g.crossOrigin),B=typeof g.integrity=="string"?g.integrity:void 0,q=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;_==="style"?s.d.S(y,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:z,integrity:B,fetchPriority:q}):_==="script"&&s.d.X(y,{crossOrigin:z,integrity:B,fetchPriority:q,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},le.preinitModule=function(y,g){if(typeof y=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var _=p(g.as,g.crossOrigin);s.d.M(y,{crossOrigin:_,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&s.d.M(y)},le.preload=function(y,g){if(typeof y=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var _=g.as,z=p(_,g.crossOrigin);s.d.L(y,_,{crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},le.preloadModule=function(y,g){if(typeof y=="string")if(g){var _=p(g.as,g.crossOrigin);s.d.m(y,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:_,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else s.d.m(y)},le.requestFormReset=function(y){s.d.r(y)},le.unstable_batchedUpdates=function(y,g){return y(g)},le.useFormState=function(y,g,_){return m.H.useFormState(y,g,_)},le.useFormStatus=function(){return m.H.useHostTransitionStatus()},le.version="19.1.0",le}var Xd;function xp(){if(Xd)return Dc.exports;Xd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),Dc.exports=bp(),Dc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zd;function Sp(){if(Zd)return Yn;Zd=1;var r=vp(),u=Vc(),c=xp();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function m(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(d(t)!==t)throw Error(s(188))}function y(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return p(n),t;if(i===a)return p(n),e;i=i.sibling}throw Error(s(188))}if(l.return!==a.return)l=n,a=i;else{for(var f=!1,h=n.child;h;){if(h===l){f=!0,l=n,a=i;break}if(h===a){f=!0,a=n,l=i;break}h=h.sibling}if(!f){for(h=i.child;h;){if(h===l){f=!0,l=i,a=n;break}if(h===a){f=!0,a=i,l=n;break}h=h.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==a)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var _=Object.assign,z=Symbol.for("react.element"),B=Symbol.for("react.transitional.element"),q=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),Z=Symbol.for("react.strict_mode"),F=Symbol.for("react.profiler"),xt=Symbol.for("react.provider"),ft=Symbol.for("react.consumer"),st=Symbol.for("react.context"),ot=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),St=Symbol.for("react.suspense_list"),mt=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),et=Symbol.for("react.activity"),dt=Symbol.for("react.memo_cache_sentinel"),Dt=Symbol.iterator;function Ct(t){return t===null||typeof t!="object"?null:(t=Dt&&t[Dt]||t["@@iterator"],typeof t=="function"?t:null)}var Bt=Symbol.for("react.client.reference");function Ut(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Bt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case j:return"Fragment";case F:return"Profiler";case Z:return"StrictMode";case J:return"Suspense";case St:return"SuspenseList";case et:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case q:return"Portal";case st:return(t.displayName||"Context")+".Provider";case ft:return(t._context.displayName||"Context")+".Consumer";case ot:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case mt:return e=t.displayName||null,e!==null?e:Ut(t.type)||"Memo";case G:e=t._payload,t=t._init;try{return Ut(t(e))}catch{}}return null}var Rt=Array.isArray,N=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},ht=[],b=-1;function M(t){return{current:t}}function Y(t){0>b||(t.current=ht[b],ht[b]=null,b--)}function L(t,e){b++,ht[b]=t.current,t.current=e}var V=M(null),at=M(null),I=M(null),pt=M(null);function Nt(t,e){switch(L(I,e),L(at,t),L(V,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?dd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=dd(e),t=hd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(V),L(V,t)}function re(){Y(V),Y(at),Y(I)}function el(t){t.memoizedState!==null&&L(pt,t);var e=V.current,l=hd(e,t.type);e!==l&&(L(at,t),L(V,l))}function ll(t){at.current===t&&(Y(V),Y(at)),pt.current===t&&(Y(pt),Un._currentValue=U)}var al=Object.prototype.hasOwnProperty,hu=r.unstable_scheduleCallback,gu=r.unstable_cancelCallback,Zh=r.unstable_shouldYield,Kh=r.unstable_requestPaint,De=r.unstable_now,Jh=r.unstable_getCurrentPriorityLevel,Qc=r.unstable_ImmediatePriority,Xc=r.unstable_UserBlockingPriority,Zn=r.unstable_NormalPriority,$h=r.unstable_LowPriority,Zc=r.unstable_IdlePriority,Wh=r.log,Fh=r.unstable_setDisableYieldValue,Ga=null,oe=null;function nl(t){if(typeof Wh=="function"&&Fh(t),oe&&typeof oe.setStrictMode=="function")try{oe.setStrictMode(Ga,t)}catch{}}var fe=Math.clz32?Math.clz32:tg,Ph=Math.log,Ih=Math.LN2;function tg(t){return t>>>=0,t===0?32:31-(Ph(t)/Ih|0)|0}var Kn=256,Jn=4194304;function Ml(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function $n(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var h=a&134217727;return h!==0?(a=h&~i,a!==0?n=Ml(a):(f&=h,f!==0?n=Ml(f):l||(l=h&~t,l!==0&&(n=Ml(l))))):(h=a&~i,h!==0?n=Ml(h):f!==0?n=Ml(f):l||(l=a&~t,l!==0&&(n=Ml(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function Va(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function eg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Kc(){var t=Kn;return Kn<<=1,(Kn&4194048)===0&&(Kn=256),t}function Jc(){var t=Jn;return Jn<<=1,(Jn&62914560)===0&&(Jn=4194304),t}function mu(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function ka(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function lg(t,e,l,a,n,i){var f=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var h=t.entanglements,v=t.expirationTimes,E=t.hiddenUpdates;for(l=f&~l;0<l;){var R=31-fe(l),C=1<<R;h[R]=0,v[R]=-1;var T=E[R];if(T!==null)for(E[R]=null,R=0;R<T.length;R++){var A=T[R];A!==null&&(A.lane&=-536870913)}l&=~C}a!==0&&$c(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(f&~e))}function $c(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-fe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Wc(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-fe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function pu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function yu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Fc(){var t=H.p;return t!==0?t:(t=window.event,t===void 0?32:wd(t.type))}function ag(t,e){var l=H.p;try{return H.p=t,e()}finally{H.p=l}}var il=Math.random().toString(36).slice(2),te="__reactFiber$"+il,ne="__reactProps$"+il,Wl="__reactContainer$"+il,vu="__reactEvents$"+il,ng="__reactListeners$"+il,ig="__reactHandles$"+il,Pc="__reactResources$"+il,Qa="__reactMarker$"+il;function bu(t){delete t[te],delete t[ne],delete t[vu],delete t[ng],delete t[ig]}function Fl(t){var e=t[te];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Wl]||l[te]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=yd(t);t!==null;){if(l=t[te])return l;t=yd(t)}return e}t=l,l=t.parentNode}return null}function Pl(t){if(t=t[te]||t[Wl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Xa(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function Il(t){var e=t[Pc];return e||(e=t[Pc]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[Qa]=!0}var Ic=new Set,tr={};function Dl(t,e){ta(t,e),ta(t+"Capture",e)}function ta(t,e){for(tr[t]=e,t=0;t<e.length;t++)Ic.add(e[t])}var ug=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),er={},lr={};function sg(t){return al.call(lr,t)?!0:al.call(er,t)?!1:ug.test(t)?lr[t]=!0:(er[t]=!0,!1)}function Wn(t,e,l){if(sg(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Fn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function qe(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var xu,ar;function ea(t){if(xu===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);xu=e&&e[1]||"",ar=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xu+t+ar}var Su=!1;function _u(t,e){if(!t||Su)return"";Su=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var C=function(){throw Error()};if(Object.defineProperty(C.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(C,[])}catch(A){var T=A}Reflect.construct(t,[],C)}else{try{C.call()}catch(A){T=A}t.call(C.prototype)}}else{try{throw Error()}catch(A){T=A}(C=t())&&typeof C.catch=="function"&&C.catch(function(){})}}catch(A){if(A&&T&&typeof A.stack=="string")return[A.stack,T.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),f=i[0],h=i[1];if(f&&h){var v=f.split(`
`),E=h.split(`
`);for(n=a=0;a<v.length&&!v[a].includes("DetermineComponentFrameRoot");)a++;for(;n<E.length&&!E[n].includes("DetermineComponentFrameRoot");)n++;if(a===v.length||n===E.length)for(a=v.length-1,n=E.length-1;1<=a&&0<=n&&v[a]!==E[n];)n--;for(;1<=a&&0<=n;a--,n--)if(v[a]!==E[n]){if(a!==1||n!==1)do if(a--,n--,0>n||v[a]!==E[n]){var R=`
`+v[a].replace(" at new "," at ");return t.displayName&&R.includes("<anonymous>")&&(R=R.replace("<anonymous>",t.displayName)),R}while(1<=a&&0<=n);break}}}finally{Su=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ea(l):""}function cg(t){switch(t.tag){case 26:case 27:case 5:return ea(t.type);case 16:return ea("Lazy");case 13:return ea("Suspense");case 19:return ea("SuspenseList");case 0:case 15:return _u(t.type,!1);case 11:return _u(t.type.render,!1);case 1:return _u(t.type,!0);case 31:return ea("Activity");default:return""}}function nr(t){try{var e="";do e+=cg(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ir(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function rg(t){var e=ir(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(f){a=""+f,i.call(this,f)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Pn(t){t._valueTracker||(t._valueTracker=rg(t))}function ur(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=ir(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function In(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var og=/[\n"\\]/g;function xe(t){return t.replace(og,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ou(t,e,l,a,n,i,f,h){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?Eu(t,f,be(e)):l!=null?Eu(t,f,be(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+be(h):t.removeAttribute("name")}function sr(t,e,l,a,n,i,f,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+be(l):"",e=e!=null?""+be(e):l,h||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=h?t.checked:!!a,t.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function Eu(t,e,l){e==="number"&&In(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function la(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+be(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function cr(t,e,l){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+be(l):""}function rr(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(s(92));if(Rt(a)){if(1<a.length)throw Error(s(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=be(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function aa(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var fg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function or(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||fg.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function fr(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&or(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&or(t,i,e[i])}function Tu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var dg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),hg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ti(t){return hg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Au=null;function Nu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var na=null,ia=null;function dr(t){var e=Pl(t);if(e&&(t=e.stateNode)){var l=t[ne]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ou(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+xe(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[ne]||null;if(!n)throw Error(s(90));Ou(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&ur(a)}break t;case"textarea":cr(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&la(t,!!l.multiple,e,!1)}}}var zu=!1;function hr(t,e,l){if(zu)return t(e,l);zu=!0;try{var a=t(e);return a}finally{if(zu=!1,(na!==null||ia!==null)&&(qi(),na&&(e=na,t=ia,ia=na=null,dr(e),t)))for(e=0;e<t.length;e++)dr(t[e])}}function Za(t,e){var l=t.stateNode;if(l===null)return null;var a=l[ne]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var Ye=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ru=!1;if(Ye)try{var Ka={};Object.defineProperty(Ka,"passive",{get:function(){Ru=!0}}),window.addEventListener("test",Ka,Ka),window.removeEventListener("test",Ka,Ka)}catch{Ru=!1}var ul=null,Mu=null,ei=null;function gr(){if(ei)return ei;var t,e=Mu,l=e.length,a,n="value"in ul?ul.value:ul.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var f=l-t;for(a=1;a<=f&&e[l-a]===n[i-a];a++);return ei=n.slice(t,1<a?1-a:void 0)}function li(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function ai(){return!0}function mr(){return!1}function ie(t){function e(l,a,n,i,f){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(l=t[h],this[h]=l?l(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?ai:mr,this.isPropagationStopped=mr,this}return _(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=ai)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=ai)},persist:function(){},isPersistent:ai}),e}var wl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ni=ie(wl),Ja=_({},wl,{view:0,detail:0}),gg=ie(Ja),Du,wu,$a,ii=_({},Ja,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Uu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$a&&($a&&t.type==="mousemove"?(Du=t.screenX-$a.screenX,wu=t.screenY-$a.screenY):wu=Du=0,$a=t),Du)},movementY:function(t){return"movementY"in t?t.movementY:wu}}),pr=ie(ii),mg=_({},ii,{dataTransfer:0}),pg=ie(mg),yg=_({},Ja,{relatedTarget:0}),Cu=ie(yg),vg=_({},wl,{animationName:0,elapsedTime:0,pseudoElement:0}),bg=ie(vg),xg=_({},wl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Sg=ie(xg),_g=_({},wl,{data:0}),yr=ie(_g),Og={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Eg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Tg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ag(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Tg[t])?!!e[t]:!1}function Uu(){return Ag}var Ng=_({},Ja,{key:function(t){if(t.key){var e=Og[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=li(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Eg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Uu,charCode:function(t){return t.type==="keypress"?li(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?li(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),zg=ie(Ng),Rg=_({},ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vr=ie(Rg),Mg=_({},Ja,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Uu}),Dg=ie(Mg),wg=_({},wl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cg=ie(wg),Ug=_({},ii,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),jg=ie(Ug),Lg=_({},wl,{newState:0,oldState:0}),Hg=ie(Lg),Bg=[9,13,27,32],ju=Ye&&"CompositionEvent"in window,Wa=null;Ye&&"documentMode"in document&&(Wa=document.documentMode);var qg=Ye&&"TextEvent"in window&&!Wa,br=Ye&&(!ju||Wa&&8<Wa&&11>=Wa),xr=" ",Sr=!1;function _r(t,e){switch(t){case"keyup":return Bg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Or(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ua=!1;function Yg(t,e){switch(t){case"compositionend":return Or(e);case"keypress":return e.which!==32?null:(Sr=!0,xr);case"textInput":return t=e.data,t===xr&&Sr?null:t;default:return null}}function Gg(t,e){if(ua)return t==="compositionend"||!ju&&_r(t,e)?(t=gr(),ei=Mu=ul=null,ua=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return br&&e.locale!=="ko"?null:e.data;default:return null}}var Vg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Er(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Vg[t.type]:e==="textarea"}function Tr(t,e,l,a){na?ia?ia.push(a):ia=[a]:na=a,e=Xi(e,"onChange"),0<e.length&&(l=new ni("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Fa=null,Pa=null;function kg(t){sd(t,0)}function ui(t){var e=Xa(t);if(ur(e))return t}function Ar(t,e){if(t==="change")return e}var Nr=!1;if(Ye){var Lu;if(Ye){var Hu="oninput"in document;if(!Hu){var zr=document.createElement("div");zr.setAttribute("oninput","return;"),Hu=typeof zr.oninput=="function"}Lu=Hu}else Lu=!1;Nr=Lu&&(!document.documentMode||9<document.documentMode)}function Rr(){Fa&&(Fa.detachEvent("onpropertychange",Mr),Pa=Fa=null)}function Mr(t){if(t.propertyName==="value"&&ui(Pa)){var e=[];Tr(e,Pa,t,Nu(t)),hr(kg,e)}}function Qg(t,e,l){t==="focusin"?(Rr(),Fa=e,Pa=l,Fa.attachEvent("onpropertychange",Mr)):t==="focusout"&&Rr()}function Xg(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ui(Pa)}function Zg(t,e){if(t==="click")return ui(e)}function Kg(t,e){if(t==="input"||t==="change")return ui(e)}function Jg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var de=typeof Object.is=="function"?Object.is:Jg;function Ia(t,e){if(de(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!al.call(e,n)||!de(t[n],e[n]))return!1}return!0}function Dr(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function wr(t,e){var l=Dr(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Dr(l)}}function Cr(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Cr(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ur(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=In(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=In(t.document)}return e}function Bu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var $g=Ye&&"documentMode"in document&&11>=document.documentMode,sa=null,qu=null,tn=null,Yu=!1;function jr(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Yu||sa==null||sa!==In(a)||(a=sa,"selectionStart"in a&&Bu(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),tn&&Ia(tn,a)||(tn=a,a=Xi(qu,"onSelect"),0<a.length&&(e=new ni("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=sa)))}function Cl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var ca={animationend:Cl("Animation","AnimationEnd"),animationiteration:Cl("Animation","AnimationIteration"),animationstart:Cl("Animation","AnimationStart"),transitionrun:Cl("Transition","TransitionRun"),transitionstart:Cl("Transition","TransitionStart"),transitioncancel:Cl("Transition","TransitionCancel"),transitionend:Cl("Transition","TransitionEnd")},Gu={},Lr={};Ye&&(Lr=document.createElement("div").style,"AnimationEvent"in window||(delete ca.animationend.animation,delete ca.animationiteration.animation,delete ca.animationstart.animation),"TransitionEvent"in window||delete ca.transitionend.transition);function Ul(t){if(Gu[t])return Gu[t];if(!ca[t])return t;var e=ca[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Lr)return Gu[t]=e[l];return t}var Hr=Ul("animationend"),Br=Ul("animationiteration"),qr=Ul("animationstart"),Wg=Ul("transitionrun"),Fg=Ul("transitionstart"),Pg=Ul("transitioncancel"),Yr=Ul("transitionend"),Gr=new Map,Vu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Vu.push("scrollEnd");function ze(t,e){Gr.set(t,e),Dl(e,[t])}var Vr=new WeakMap;function Se(t,e){if(typeof t=="object"&&t!==null){var l=Vr.get(t);return l!==void 0?l:(e={value:t,source:e,stack:nr(e)},Vr.set(t,e),e)}return{value:t,source:e,stack:nr(e)}}var _e=[],ra=0,ku=0;function si(){for(var t=ra,e=ku=ra=0;e<t;){var l=_e[e];_e[e++]=null;var a=_e[e];_e[e++]=null;var n=_e[e];_e[e++]=null;var i=_e[e];if(_e[e++]=null,a!==null&&n!==null){var f=a.pending;f===null?n.next=n:(n.next=f.next,f.next=n),a.pending=n}i!==0&&kr(l,n,i)}}function ci(t,e,l,a){_e[ra++]=t,_e[ra++]=e,_e[ra++]=l,_e[ra++]=a,ku|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Qu(t,e,l,a){return ci(t,e,l,a),ri(t)}function oa(t,e){return ci(t,null,null,e),ri(t)}function kr(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-fe(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function ri(t){if(50<An)throw An=0,Ws=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var fa={};function Ig(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function he(t,e,l,a){return new Ig(t,e,l,a)}function Xu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ge(t,e){var l=t.alternate;return l===null?(l=he(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Qr(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function oi(t,e,l,a,n,i){var f=0;if(a=t,typeof t=="function")Xu(t)&&(f=1);else if(typeof t=="string")f=ep(t,l,V.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case et:return t=he(31,l,e,n),t.elementType=et,t.lanes=i,t;case j:return jl(l.children,n,i,e);case Z:f=8,n|=24;break;case F:return t=he(12,l,e,n|2),t.elementType=F,t.lanes=i,t;case J:return t=he(13,l,e,n),t.elementType=J,t.lanes=i,t;case St:return t=he(19,l,e,n),t.elementType=St,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case xt:case st:f=10;break t;case ft:f=9;break t;case ot:f=11;break t;case mt:f=14;break t;case G:f=16,a=null;break t}f=29,l=Error(s(130,t===null?"null":typeof t,"")),a=null}return e=he(f,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function jl(t,e,l,a){return t=he(7,t,a,e),t.lanes=l,t}function Zu(t,e,l){return t=he(6,t,null,e),t.lanes=l,t}function Ku(t,e,l){return e=he(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var da=[],ha=0,fi=null,di=0,Oe=[],Ee=0,Ll=null,Ve=1,ke="";function Hl(t,e){da[ha++]=di,da[ha++]=fi,fi=t,di=e}function Xr(t,e,l){Oe[Ee++]=Ve,Oe[Ee++]=ke,Oe[Ee++]=Ll,Ll=t;var a=Ve;t=ke;var n=32-fe(a)-1;a&=~(1<<n),l+=1;var i=32-fe(e)+n;if(30<i){var f=n-n%5;i=(a&(1<<f)-1).toString(32),a>>=f,n-=f,Ve=1<<32-fe(e)+n|l<<n|a,ke=i+t}else Ve=1<<i|l<<n|a,ke=t}function Ju(t){t.return!==null&&(Hl(t,1),Xr(t,1,0))}function $u(t){for(;t===fi;)fi=da[--ha],da[ha]=null,di=da[--ha],da[ha]=null;for(;t===Ll;)Ll=Oe[--Ee],Oe[Ee]=null,ke=Oe[--Ee],Oe[Ee]=null,Ve=Oe[--Ee],Oe[Ee]=null}var ae=null,Lt=null,vt=!1,Bl=null,we=!1,Wu=Error(s(519));function ql(t){var e=Error(s(418,""));throw an(Se(e,t)),Wu}function Zr(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[te]=t,e[ne]=a,l){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(l=0;l<zn.length;l++)rt(zn[l],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":rt("invalid",e),sr(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Pn(e);break;case"select":rt("invalid",e);break;case"textarea":rt("invalid",e),rr(e,a.value,a.defaultValue,a.children),Pn(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||fd(e.textContent,l)?(a.popover!=null&&(rt("beforetoggle",e),rt("toggle",e)),a.onScroll!=null&&rt("scroll",e),a.onScrollEnd!=null&&rt("scrollend",e),a.onClick!=null&&(e.onclick=Zi),e=!0):e=!1,e||ql(t)}function Kr(t){for(ae=t.return;ae;)switch(ae.tag){case 5:case 13:we=!1;return;case 27:case 3:we=!0;return;default:ae=ae.return}}function en(t){if(t!==ae)return!1;if(!vt)return Kr(t),vt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||dc(t.type,t.memoizedProps)),l=!l),l&&Lt&&ql(t),Kr(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Lt=Me(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Lt=null}}else e===27?(e=Lt,_l(t.type)?(t=pc,pc=null,Lt=t):Lt=e):Lt=ae?Me(t.stateNode.nextSibling):null;return!0}function ln(){Lt=ae=null,vt=!1}function Jr(){var t=Bl;return t!==null&&(ce===null?ce=t:ce.push.apply(ce,t),Bl=null),t}function an(t){Bl===null?Bl=[t]:Bl.push(t)}var Fu=M(null),Yl=null,Qe=null;function sl(t,e,l){L(Fu,e._currentValue),e._currentValue=l}function Xe(t){t._currentValue=Fu.current,Y(Fu)}function Pu(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Iu(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var f=n.child;i=i.firstContext;t:for(;i!==null;){var h=i;i=n;for(var v=0;v<e.length;v++)if(h.context===e[v]){i.lanes|=l,h=i.alternate,h!==null&&(h.lanes|=l),Pu(i.return,l,t),a||(f=null);break t}i=h.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(s(341));f.lanes|=l,i=f.alternate,i!==null&&(i.lanes|=l),Pu(f,l,t),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===t){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function nn(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var h=n.type;de(n.pendingProps.value,f.value)||(t!==null?t.push(h):t=[h])}}else if(n===pt.current){if(f=n.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Un):t=[Un])}n=n.return}t!==null&&Iu(e,t,l,a),e.flags|=262144}function hi(t){for(t=t.firstContext;t!==null;){if(!de(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Gl(t){Yl=t,Qe=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ee(t){return $r(Yl,t)}function gi(t,e){return Yl===null&&Gl(t),$r(t,e)}function $r(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Qe===null){if(t===null)throw Error(s(308));Qe=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Qe=Qe.next=e;return l}var tm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},em=r.unstable_scheduleCallback,lm=r.unstable_NormalPriority,kt={$$typeof:st,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ts(){return{controller:new tm,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&em(lm,function(){t.controller.abort()})}var sn=null,es=0,ga=0,ma=null;function am(t,e){if(sn===null){var l=sn=[];es=0,ga=ac(),ma={status:"pending",value:void 0,then:function(a){l.push(a)}}}return es++,e.then(Wr,Wr),e}function Wr(){if(--es===0&&sn!==null){ma!==null&&(ma.status="fulfilled");var t=sn;sn=null,ga=0,ma=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function nm(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Fr=N.S;N.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&am(t,e),Fr!==null&&Fr(t,e)};var Vl=M(null);function ls(){var t=Vl.current;return t!==null?t:Mt.pooledCache}function mi(t,e){e===null?L(Vl,Vl.current):L(Vl,e.pool)}function Pr(){var t=ls();return t===null?null:{parent:kt._currentValue,pool:t}}var cn=Error(s(460)),Ir=Error(s(474)),pi=Error(s(542)),as={then:function(){}};function to(t){return t=t.status,t==="fulfilled"||t==="rejected"}function yi(){}function eo(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(yi,yi),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ao(t),t;default:if(typeof e.status=="string")e.then(yi,yi);else{if(t=Mt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ao(t),t}throw rn=e,cn}}var rn=null;function lo(){if(rn===null)throw Error(s(459));var t=rn;return rn=null,t}function ao(t){if(t===cn||t===pi)throw Error(s(483))}var cl=!1;function ns(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function is(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function rl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function ol(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(_t&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=ri(t),kr(t,null,l),e}return ci(t,a,e,l),ri(t)}function on(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Wc(t,l)}}function us(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=f:i=i.next=f,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var ss=!1;function fn(){if(ss){var t=ma;if(t!==null)throw t}}function dn(t,e,l,a){ss=!1;var n=t.updateQueue;cl=!1;var i=n.firstBaseUpdate,f=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var v=h,E=v.next;v.next=null,f===null?i=E:f.next=E,f=v;var R=t.alternate;R!==null&&(R=R.updateQueue,h=R.lastBaseUpdate,h!==f&&(h===null?R.firstBaseUpdate=E:h.next=E,R.lastBaseUpdate=v))}if(i!==null){var C=n.baseState;f=0,R=E=v=null,h=i;do{var T=h.lane&-536870913,A=T!==h.lane;if(A?(gt&T)===T:(a&T)===T){T!==0&&T===ga&&(ss=!0),R!==null&&(R=R.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var P=t,$=h;T=e;var At=l;switch($.tag){case 1:if(P=$.payload,typeof P=="function"){C=P.call(At,C,T);break t}C=P;break t;case 3:P.flags=P.flags&-65537|128;case 0:if(P=$.payload,T=typeof P=="function"?P.call(At,C,T):P,T==null)break t;C=_({},C,T);break t;case 2:cl=!0}}T=h.callback,T!==null&&(t.flags|=64,A&&(t.flags|=8192),A=n.callbacks,A===null?n.callbacks=[T]:A.push(T))}else A={lane:T,tag:h.tag,payload:h.payload,callback:h.callback,next:null},R===null?(E=R=A,v=C):R=R.next=A,f|=T;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;A=h,h=A.next,A.next=null,n.lastBaseUpdate=A,n.shared.pending=null}}while(!0);R===null&&(v=C),n.baseState=v,n.firstBaseUpdate=E,n.lastBaseUpdate=R,i===null&&(n.shared.lanes=0),vl|=f,t.lanes=f,t.memoizedState=C}}function no(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function io(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)no(l[t],e)}var pa=M(null),vi=M(0);function uo(t,e){t=Pe,L(vi,t),L(pa,e),Pe=t|e.baseLanes}function cs(){L(vi,Pe),L(pa,pa.current)}function rs(){Pe=vi.current,Y(pa),Y(vi)}var fl=0,nt=null,Et=null,Gt=null,bi=!1,ya=!1,kl=!1,xi=0,hn=0,va=null,im=0;function qt(){throw Error(s(321))}function os(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!de(t[l],e[l]))return!1;return!0}function fs(t,e,l,a,n,i){return fl=i,nt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,N.H=t===null||t.memoizedState===null?Qo:Xo,kl=!1,i=l(a,n),kl=!1,ya&&(i=co(e,l,a,n)),so(t),i}function so(t){N.H=Ai;var e=Et!==null&&Et.next!==null;if(fl=0,Gt=Et=nt=null,bi=!1,hn=0,va=null,e)throw Error(s(300));t===null||Kt||(t=t.dependencies,t!==null&&hi(t)&&(Kt=!0))}function co(t,e,l,a){nt=t;var n=0;do{if(ya&&(va=null),hn=0,ya=!1,25<=n)throw Error(s(301));if(n+=1,Gt=Et=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}N.H=dm,i=e(l,a)}while(ya);return i}function um(){var t=N.H,e=t.useState()[0];return e=typeof e.then=="function"?gn(e):e,t=t.useState()[0],(Et!==null?Et.memoizedState:null)!==t&&(nt.flags|=1024),e}function ds(){var t=xi!==0;return xi=0,t}function hs(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function gs(t){if(bi){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bi=!1}fl=0,Gt=Et=nt=null,ya=!1,hn=xi=0,va=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Gt===null?nt.memoizedState=Gt=t:Gt=Gt.next=t,Gt}function Vt(){if(Et===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=Et.next;var e=Gt===null?nt.memoizedState:Gt.next;if(e!==null)Gt=e,Et=t;else{if(t===null)throw nt.alternate===null?Error(s(467)):Error(s(310));Et=t,t={memoizedState:Et.memoizedState,baseState:Et.baseState,baseQueue:Et.baseQueue,queue:Et.queue,next:null},Gt===null?nt.memoizedState=Gt=t:Gt=Gt.next=t}return Gt}function ms(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function gn(t){var e=hn;return hn+=1,va===null&&(va=[]),t=eo(va,t,e),e=nt,(Gt===null?e.memoizedState:Gt.next)===null&&(e=e.alternate,N.H=e===null||e.memoizedState===null?Qo:Xo),t}function Si(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return gn(t);if(t.$$typeof===st)return ee(t)}throw Error(s(438,String(t)))}function ps(t){var e=null,l=nt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=nt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=ms(),nt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=dt;return e.index++,l}function Ze(t,e){return typeof e=="function"?e(t):e}function _i(t){var e=Vt();return ys(e,Et,t)}function ys(t,e,l){var a=t.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var f=n.next;n.next=i.next,i.next=f}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var h=f=null,v=null,E=e,R=!1;do{var C=E.lane&-536870913;if(C!==E.lane?(gt&C)===C:(fl&C)===C){var T=E.revertLane;if(T===0)v!==null&&(v=v.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),C===ga&&(R=!0);else if((fl&T)===T){E=E.next,T===ga&&(R=!0);continue}else C={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},v===null?(h=v=C,f=i):v=v.next=C,nt.lanes|=T,vl|=T;C=E.action,kl&&l(i,C),i=E.hasEagerState?E.eagerState:l(i,C)}else T={lane:C,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},v===null?(h=v=T,f=i):v=v.next=T,nt.lanes|=C,vl|=C;E=E.next}while(E!==null&&E!==e);if(v===null?f=i:v.next=h,!de(i,t.memoizedState)&&(Kt=!0,R&&(l=ma,l!==null)))throw l;t.memoizedState=i,t.baseState=f,t.baseQueue=v,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function vs(t){var e=Vt(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var f=n=n.next;do i=t(i,f.action),f=f.next;while(f!==n);de(i,e.memoizedState)||(Kt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function ro(t,e,l){var a=nt,n=Vt(),i=vt;if(i){if(l===void 0)throw Error(s(407));l=l()}else l=e();var f=!de((Et||n).memoizedState,l);f&&(n.memoizedState=l,Kt=!0),n=n.queue;var h=ho.bind(null,a,n,t);if(mn(2048,8,h,[t]),n.getSnapshot!==e||f||Gt!==null&&Gt.memoizedState.tag&1){if(a.flags|=2048,ba(9,Oi(),fo.bind(null,a,n,l,e),null),Mt===null)throw Error(s(349));i||(fl&124)!==0||oo(a,e,l)}return l}function oo(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=nt.updateQueue,e===null?(e=ms(),nt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function fo(t,e,l,a){e.value=l,e.getSnapshot=a,go(e)&&mo(t)}function ho(t,e,l){return l(function(){go(e)&&mo(t)})}function go(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!de(t,l)}catch{return!0}}function mo(t){var e=oa(t,2);e!==null&&ve(e,t,2)}function bs(t){var e=ue();if(typeof t=="function"){var l=t;if(t=l(),kl){nl(!0);try{l()}finally{nl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:t},e}function po(t,e,l,a){return t.baseState=l,ys(t,Et,typeof a=="function"?a:Ze)}function sm(t,e,l,a,n){if(Ti(t))throw Error(s(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};N.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,yo(e,i)):(i.next=l.next,e.pending=l.next=i)}}function yo(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=N.T,f={};N.T=f;try{var h=l(n,a),v=N.S;v!==null&&v(f,h),vo(t,e,h)}catch(E){xs(t,e,E)}finally{N.T=i}}else try{i=l(n,a),vo(t,e,i)}catch(E){xs(t,e,E)}}function vo(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){bo(t,e,a)},function(a){return xs(t,e,a)}):bo(t,e,l)}function bo(t,e,l){e.status="fulfilled",e.value=l,xo(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,yo(t,l)))}function xs(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,xo(e),e=e.next;while(e!==a)}t.action=null}function xo(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function So(t,e){return e}function _o(t,e){if(vt){var l=Mt.formState;if(l!==null){t:{var a=nt;if(vt){if(Lt){e:{for(var n=Lt,i=we;n.nodeType!==8;){if(!i){n=null;break e}if(n=Me(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Lt=Me(n.nextSibling),a=n.data==="F!";break t}}ql(a)}a=!1}a&&(e=l[0])}}return l=ue(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:So,lastRenderedState:e},l.queue=a,l=Go.bind(null,nt,a),a.dispatch=l,a=bs(!1),i=Ts.bind(null,nt,!1,a.queue),a=ue(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=sm.bind(null,nt,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Oo(t){var e=Vt();return Eo(e,Et,t)}function Eo(t,e,l){if(e=ys(t,e,So)[0],t=_i(Ze)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=gn(e)}catch(f){throw f===cn?pi:f}else a=e;e=Vt();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(nt.flags|=2048,ba(9,Oi(),cm.bind(null,n,l),null)),[a,i,t]}function cm(t,e){t.action=e}function To(t){var e=Vt(),l=Et;if(l!==null)return Eo(e,l,t);Vt(),e=e.memoizedState,l=Vt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function ba(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=nt.updateQueue,e===null&&(e=ms(),nt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Oi(){return{destroy:void 0,resource:void 0}}function Ao(){return Vt().memoizedState}function Ei(t,e,l,a){var n=ue();a=a===void 0?null:a,nt.flags|=t,n.memoizedState=ba(1|e,Oi(),l,a)}function mn(t,e,l,a){var n=Vt();a=a===void 0?null:a;var i=n.memoizedState.inst;Et!==null&&a!==null&&os(a,Et.memoizedState.deps)?n.memoizedState=ba(e,i,l,a):(nt.flags|=t,n.memoizedState=ba(1|e,i,l,a))}function No(t,e){Ei(8390656,8,t,e)}function zo(t,e){mn(2048,8,t,e)}function Ro(t,e){return mn(4,2,t,e)}function Mo(t,e){return mn(4,4,t,e)}function Do(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function wo(t,e,l){l=l!=null?l.concat([t]):null,mn(4,4,Do.bind(null,e,t),l)}function Ss(){}function Co(t,e){var l=Vt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&os(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Uo(t,e){var l=Vt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&os(e,a[1]))return a[0];if(a=t(),kl){nl(!0);try{t()}finally{nl(!1)}}return l.memoizedState=[a,e],a}function _s(t,e,l){return l===void 0||(fl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Bf(),nt.lanes|=t,vl|=t,l)}function jo(t,e,l,a){return de(l,e)?l:pa.current!==null?(t=_s(t,l,a),de(t,e)||(Kt=!0),t):(fl&42)===0?(Kt=!0,t.memoizedState=l):(t=Bf(),nt.lanes|=t,vl|=t,e)}function Lo(t,e,l,a,n){var i=H.p;H.p=i!==0&&8>i?i:8;var f=N.T,h={};N.T=h,Ts(t,!1,e,l);try{var v=n(),E=N.S;if(E!==null&&E(h,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var R=nm(v,a);pn(t,e,R,ye(t))}else pn(t,e,a,ye(t))}catch(C){pn(t,e,{then:function(){},status:"rejected",reason:C},ye())}finally{H.p=i,N.T=f}}function rm(){}function Os(t,e,l,a){if(t.tag!==5)throw Error(s(476));var n=Ho(t).queue;Lo(t,n,e,U,l===null?rm:function(){return Bo(t),l(a)})}function Ho(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:U},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Bo(t){var e=Ho(t).next.queue;pn(t,e,{},ye())}function Es(){return ee(Un)}function qo(){return Vt().memoizedState}function Yo(){return Vt().memoizedState}function om(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=ye();t=rl(l);var a=ol(e,t,l);a!==null&&(ve(a,e,l),on(a,e,l)),e={cache:ts()},t.payload=e;return}e=e.return}}function fm(t,e,l){var a=ye();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Ti(t)?Vo(e,l):(l=Qu(t,e,l,a),l!==null&&(ve(l,t,a),ko(l,e,a)))}function Go(t,e,l){var a=ye();pn(t,e,l,a)}function pn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Ti(t))Vo(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var f=e.lastRenderedState,h=i(f,l);if(n.hasEagerState=!0,n.eagerState=h,de(h,f))return ci(t,e,n,0),Mt===null&&si(),!1}catch{}finally{}if(l=Qu(t,e,n,a),l!==null)return ve(l,t,a),ko(l,e,a),!0}return!1}function Ts(t,e,l,a){if(a={lane:2,revertLane:ac(),action:a,hasEagerState:!1,eagerState:null,next:null},Ti(t)){if(e)throw Error(s(479))}else e=Qu(t,l,a,2),e!==null&&ve(e,t,2)}function Ti(t){var e=t.alternate;return t===nt||e!==null&&e===nt}function Vo(t,e){ya=bi=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function ko(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Wc(t,l)}}var Ai={readContext:ee,use:Si,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Qo={readContext:ee,use:Si,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:ee,useEffect:No,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Ei(4194308,4,Do.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Ei(4194308,4,t,e)},useInsertionEffect:function(t,e){Ei(4,2,t,e)},useMemo:function(t,e){var l=ue();e=e===void 0?null:e;var a=t();if(kl){nl(!0);try{t()}finally{nl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ue();if(l!==void 0){var n=l(e);if(kl){nl(!0);try{l(e)}finally{nl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=fm.bind(null,nt,t),[a.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=bs(t);var e=t.queue,l=Go.bind(null,nt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:Ss,useDeferredValue:function(t,e){var l=ue();return _s(l,t,e)},useTransition:function(){var t=bs(!1);return t=Lo.bind(null,nt,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=nt,n=ue();if(vt){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),Mt===null)throw Error(s(349));(gt&124)!==0||oo(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,No(ho.bind(null,a,i,t),[t]),a.flags|=2048,ba(9,Oi(),fo.bind(null,a,i,l,e),null),l},useId:function(){var t=ue(),e=Mt.identifierPrefix;if(vt){var l=ke,a=Ve;l=(a&~(1<<32-fe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=xi++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=im++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Es,useFormState:_o,useActionState:_o,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Ts.bind(null,nt,!0,l),l.dispatch=e,[t,e]},useMemoCache:ps,useCacheRefresh:function(){return ue().memoizedState=om.bind(null,nt)}},Xo={readContext:ee,use:Si,useCallback:Co,useContext:ee,useEffect:zo,useImperativeHandle:wo,useInsertionEffect:Ro,useLayoutEffect:Mo,useMemo:Uo,useReducer:_i,useRef:Ao,useState:function(){return _i(Ze)},useDebugValue:Ss,useDeferredValue:function(t,e){var l=Vt();return jo(l,Et.memoizedState,t,e)},useTransition:function(){var t=_i(Ze)[0],e=Vt().memoizedState;return[typeof t=="boolean"?t:gn(t),e]},useSyncExternalStore:ro,useId:qo,useHostTransitionStatus:Es,useFormState:Oo,useActionState:Oo,useOptimistic:function(t,e){var l=Vt();return po(l,Et,t,e)},useMemoCache:ps,useCacheRefresh:Yo},dm={readContext:ee,use:Si,useCallback:Co,useContext:ee,useEffect:zo,useImperativeHandle:wo,useInsertionEffect:Ro,useLayoutEffect:Mo,useMemo:Uo,useReducer:vs,useRef:Ao,useState:function(){return vs(Ze)},useDebugValue:Ss,useDeferredValue:function(t,e){var l=Vt();return Et===null?_s(l,t,e):jo(l,Et.memoizedState,t,e)},useTransition:function(){var t=vs(Ze)[0],e=Vt().memoizedState;return[typeof t=="boolean"?t:gn(t),e]},useSyncExternalStore:ro,useId:qo,useHostTransitionStatus:Es,useFormState:To,useActionState:To,useOptimistic:function(t,e){var l=Vt();return Et!==null?po(l,Et,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:ps,useCacheRefresh:Yo},xa=null,yn=0;function Ni(t){var e=yn;return yn+=1,xa===null&&(xa=[]),eo(xa,t,e)}function vn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zi(t,e){throw e.$$typeof===z?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Zo(t){var e=t._init;return e(t._payload)}function Ko(t){function e(S,x){if(t){var O=S.deletions;O===null?(S.deletions=[x],S.flags|=16):O.push(x)}}function l(S,x){if(!t)return null;for(;x!==null;)e(S,x),x=x.sibling;return null}function a(S){for(var x=new Map;S!==null;)S.key!==null?x.set(S.key,S):x.set(S.index,S),S=S.sibling;return x}function n(S,x){return S=Ge(S,x),S.index=0,S.sibling=null,S}function i(S,x,O){return S.index=O,t?(O=S.alternate,O!==null?(O=O.index,O<x?(S.flags|=67108866,x):O):(S.flags|=67108866,x)):(S.flags|=1048576,x)}function f(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function h(S,x,O,w){return x===null||x.tag!==6?(x=Zu(O,S.mode,w),x.return=S,x):(x=n(x,O),x.return=S,x)}function v(S,x,O,w){var k=O.type;return k===j?R(S,x,O.props.children,w,O.key):x!==null&&(x.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===G&&Zo(k)===x.type)?(x=n(x,O.props),vn(x,O),x.return=S,x):(x=oi(O.type,O.key,O.props,null,S.mode,w),vn(x,O),x.return=S,x)}function E(S,x,O,w){return x===null||x.tag!==4||x.stateNode.containerInfo!==O.containerInfo||x.stateNode.implementation!==O.implementation?(x=Ku(O,S.mode,w),x.return=S,x):(x=n(x,O.children||[]),x.return=S,x)}function R(S,x,O,w,k){return x===null||x.tag!==7?(x=jl(O,S.mode,w,k),x.return=S,x):(x=n(x,O),x.return=S,x)}function C(S,x,O){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=Zu(""+x,S.mode,O),x.return=S,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case B:return O=oi(x.type,x.key,x.props,null,S.mode,O),vn(O,x),O.return=S,O;case q:return x=Ku(x,S.mode,O),x.return=S,x;case G:var w=x._init;return x=w(x._payload),C(S,x,O)}if(Rt(x)||Ct(x))return x=jl(x,S.mode,O,null),x.return=S,x;if(typeof x.then=="function")return C(S,Ni(x),O);if(x.$$typeof===st)return C(S,gi(S,x),O);zi(S,x)}return null}function T(S,x,O,w){var k=x!==null?x.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return k!==null?null:h(S,x,""+O,w);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case B:return O.key===k?v(S,x,O,w):null;case q:return O.key===k?E(S,x,O,w):null;case G:return k=O._init,O=k(O._payload),T(S,x,O,w)}if(Rt(O)||Ct(O))return k!==null?null:R(S,x,O,w,null);if(typeof O.then=="function")return T(S,x,Ni(O),w);if(O.$$typeof===st)return T(S,x,gi(S,O),w);zi(S,O)}return null}function A(S,x,O,w,k){if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return S=S.get(O)||null,h(x,S,""+w,k);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case B:return S=S.get(w.key===null?O:w.key)||null,v(x,S,w,k);case q:return S=S.get(w.key===null?O:w.key)||null,E(x,S,w,k);case G:var ut=w._init;return w=ut(w._payload),A(S,x,O,w,k)}if(Rt(w)||Ct(w))return S=S.get(O)||null,R(x,S,w,k,null);if(typeof w.then=="function")return A(S,x,O,Ni(w),k);if(w.$$typeof===st)return A(S,x,O,gi(x,w),k);zi(x,w)}return null}function P(S,x,O,w){for(var k=null,ut=null,K=x,W=x=0,$t=null;K!==null&&W<O.length;W++){K.index>W?($t=K,K=null):$t=K.sibling;var yt=T(S,K,O[W],w);if(yt===null){K===null&&(K=$t);break}t&&K&&yt.alternate===null&&e(S,K),x=i(yt,x,W),ut===null?k=yt:ut.sibling=yt,ut=yt,K=$t}if(W===O.length)return l(S,K),vt&&Hl(S,W),k;if(K===null){for(;W<O.length;W++)K=C(S,O[W],w),K!==null&&(x=i(K,x,W),ut===null?k=K:ut.sibling=K,ut=K);return vt&&Hl(S,W),k}for(K=a(K);W<O.length;W++)$t=A(K,S,W,O[W],w),$t!==null&&(t&&$t.alternate!==null&&K.delete($t.key===null?W:$t.key),x=i($t,x,W),ut===null?k=$t:ut.sibling=$t,ut=$t);return t&&K.forEach(function(Nl){return e(S,Nl)}),vt&&Hl(S,W),k}function $(S,x,O,w){if(O==null)throw Error(s(151));for(var k=null,ut=null,K=x,W=x=0,$t=null,yt=O.next();K!==null&&!yt.done;W++,yt=O.next()){K.index>W?($t=K,K=null):$t=K.sibling;var Nl=T(S,K,yt.value,w);if(Nl===null){K===null&&(K=$t);break}t&&K&&Nl.alternate===null&&e(S,K),x=i(Nl,x,W),ut===null?k=Nl:ut.sibling=Nl,ut=Nl,K=$t}if(yt.done)return l(S,K),vt&&Hl(S,W),k;if(K===null){for(;!yt.done;W++,yt=O.next())yt=C(S,yt.value,w),yt!==null&&(x=i(yt,x,W),ut===null?k=yt:ut.sibling=yt,ut=yt);return vt&&Hl(S,W),k}for(K=a(K);!yt.done;W++,yt=O.next())yt=A(K,S,W,yt.value,w),yt!==null&&(t&&yt.alternate!==null&&K.delete(yt.key===null?W:yt.key),x=i(yt,x,W),ut===null?k=yt:ut.sibling=yt,ut=yt);return t&&K.forEach(function(hp){return e(S,hp)}),vt&&Hl(S,W),k}function At(S,x,O,w){if(typeof O=="object"&&O!==null&&O.type===j&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case B:t:{for(var k=O.key;x!==null;){if(x.key===k){if(k=O.type,k===j){if(x.tag===7){l(S,x.sibling),w=n(x,O.props.children),w.return=S,S=w;break t}}else if(x.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===G&&Zo(k)===x.type){l(S,x.sibling),w=n(x,O.props),vn(w,O),w.return=S,S=w;break t}l(S,x);break}else e(S,x);x=x.sibling}O.type===j?(w=jl(O.props.children,S.mode,w,O.key),w.return=S,S=w):(w=oi(O.type,O.key,O.props,null,S.mode,w),vn(w,O),w.return=S,S=w)}return f(S);case q:t:{for(k=O.key;x!==null;){if(x.key===k)if(x.tag===4&&x.stateNode.containerInfo===O.containerInfo&&x.stateNode.implementation===O.implementation){l(S,x.sibling),w=n(x,O.children||[]),w.return=S,S=w;break t}else{l(S,x);break}else e(S,x);x=x.sibling}w=Ku(O,S.mode,w),w.return=S,S=w}return f(S);case G:return k=O._init,O=k(O._payload),At(S,x,O,w)}if(Rt(O))return P(S,x,O,w);if(Ct(O)){if(k=Ct(O),typeof k!="function")throw Error(s(150));return O=k.call(O),$(S,x,O,w)}if(typeof O.then=="function")return At(S,x,Ni(O),w);if(O.$$typeof===st)return At(S,x,gi(S,O),w);zi(S,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,x!==null&&x.tag===6?(l(S,x.sibling),w=n(x,O),w.return=S,S=w):(l(S,x),w=Zu(O,S.mode,w),w.return=S,S=w),f(S)):l(S,x)}return function(S,x,O,w){try{yn=0;var k=At(S,x,O,w);return xa=null,k}catch(K){if(K===cn||K===pi)throw K;var ut=he(29,K,null,S.mode);return ut.lanes=w,ut.return=S,ut}finally{}}}var Sa=Ko(!0),Jo=Ko(!1),Te=M(null),Ce=null;function dl(t){var e=t.alternate;L(Qt,Qt.current&1),L(Te,t),Ce===null&&(e===null||pa.current!==null||e.memoizedState!==null)&&(Ce=t)}function $o(t){if(t.tag===22){if(L(Qt,Qt.current),L(Te,t),Ce===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ce=t)}}else hl()}function hl(){L(Qt,Qt.current),L(Te,Te.current)}function Ke(t){Y(Te),Ce===t&&(Ce=null),Y(Qt)}var Qt=M(0);function Ri(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||mc(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function As(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:_({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Ns={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=ye(),n=rl(a);n.payload=e,l!=null&&(n.callback=l),e=ol(t,n,a),e!==null&&(ve(e,t,a),on(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=ye(),n=rl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=ol(t,n,a),e!==null&&(ve(e,t,a),on(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=ye(),a=rl(l);a.tag=2,e!=null&&(a.callback=e),e=ol(t,a,l),e!==null&&(ve(e,t,l),on(e,t,l))}};function Wo(t,e,l,a,n,i,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,f):e.prototype&&e.prototype.isPureReactComponent?!Ia(l,a)||!Ia(n,i):!0}function Fo(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Ns.enqueueReplaceState(e,e.state,null)}function Ql(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=_({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Mi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Po(t){Mi(t)}function Io(t){console.error(t)}function tf(t){Mi(t)}function Di(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ef(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function zs(t,e,l){return l=rl(l),l.tag=3,l.payload={element:null},l.callback=function(){Di(t,e)},l}function lf(t){return t=rl(t),t.tag=3,t}function af(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){ef(e,l,a)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){ef(e,l,a),typeof n!="function"&&(bl===null?bl=new Set([this]):bl.add(this));var h=a.stack;this.componentDidCatch(a.value,{componentStack:h!==null?h:""})})}function hm(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&nn(e,l,n,!0),l=Te.current,l!==null){switch(l.tag){case 13:return Ce===null?Ps():l.alternate===null&&Ht===0&&(Ht=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===as?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),tc(t,a,n)),!1;case 22:return l.flags|=65536,a===as?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),tc(t,a,n)),!1}throw Error(s(435,l.tag))}return tc(t,a,n),Ps(),!1}if(vt)return e=Te.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Wu&&(t=Error(s(422),{cause:a}),an(Se(t,l)))):(a!==Wu&&(e=Error(s(423),{cause:a}),an(Se(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Se(a,l),n=zs(t.stateNode,a,n),us(t,n),Ht!==4&&(Ht=2)),!1;var i=Error(s(520),{cause:a});if(i=Se(i,l),Tn===null?Tn=[i]:Tn.push(i),Ht!==4&&(Ht=2),e===null)return!0;a=Se(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=zs(l.stateNode,a,t),us(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(bl===null||!bl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=lf(n),af(n,t,l,a),us(l,n),!1}l=l.return}while(l!==null);return!1}var nf=Error(s(461)),Kt=!1;function Wt(t,e,l,a){e.child=t===null?Jo(e,null,l,a):Sa(e,t.child,l,a)}function uf(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var f={};for(var h in a)h!=="ref"&&(f[h]=a[h])}else f=a;return Gl(e),a=fs(t,e,l,f,i,n),h=ds(),t!==null&&!Kt?(hs(t,e,n),Je(t,e,n)):(vt&&h&&Ju(e),e.flags|=1,Wt(t,e,a,n),e.child)}function sf(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!Xu(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,cf(t,e,i,a,n)):(t=oi(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Ls(t,n)){var f=i.memoizedProps;if(l=l.compare,l=l!==null?l:Ia,l(f,a)&&t.ref===e.ref)return Je(t,e,n)}return e.flags|=1,t=Ge(i,a),t.ref=e.ref,t.return=e,e.child=t}function cf(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(Ia(i,a)&&t.ref===e.ref)if(Kt=!1,e.pendingProps=a=i,Ls(t,n))(t.flags&131072)!==0&&(Kt=!0);else return e.lanes=t.lanes,Je(t,e,n)}return Rs(t,e,l,a,n)}function rf(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return of(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mi(e,i!==null?i.cachePool:null),i!==null?uo(e,i):cs(),$o(e);else return e.lanes=e.childLanes=536870912,of(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(mi(e,i.cachePool),uo(e,i),hl(),e.memoizedState=null):(t!==null&&mi(e,null),cs(),hl());return Wt(t,e,n,l),e.child}function of(t,e,l,a){var n=ls();return n=n===null?null:{parent:kt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&mi(e,null),cs(),$o(e),t!==null&&nn(t,e,a,!0),null}function wi(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Rs(t,e,l,a,n){return Gl(e),l=fs(t,e,l,a,void 0,n),a=ds(),t!==null&&!Kt?(hs(t,e,n),Je(t,e,n)):(vt&&a&&Ju(e),e.flags|=1,Wt(t,e,l,n),e.child)}function ff(t,e,l,a,n,i){return Gl(e),e.updateQueue=null,l=co(e,a,l,n),so(t),a=ds(),t!==null&&!Kt?(hs(t,e,i),Je(t,e,i)):(vt&&a&&Ju(e),e.flags|=1,Wt(t,e,l,i),e.child)}function df(t,e,l,a,n){if(Gl(e),e.stateNode===null){var i=fa,f=l.contextType;typeof f=="object"&&f!==null&&(i=ee(f)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Ns,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},ns(e),f=l.contextType,i.context=typeof f=="object"&&f!==null?ee(f):fa,i.state=e.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(As(e,l,f,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&Ns.enqueueReplaceState(i,i.state,null),dn(e,a,i,n),fn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var h=e.memoizedProps,v=Ql(l,h);i.props=v;var E=i.context,R=l.contextType;f=fa,typeof R=="object"&&R!==null&&(f=ee(R));var C=l.getDerivedStateFromProps;R=typeof C=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,R||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||E!==f)&&Fo(e,i,a,f),cl=!1;var T=e.memoizedState;i.state=T,dn(e,a,i,n),fn(),E=e.memoizedState,h||T!==E||cl?(typeof C=="function"&&(As(e,l,C,a),E=e.memoizedState),(v=cl||Wo(e,l,v,a,T,E,f))?(R||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=E),i.props=a,i.state=E,i.context=f,a=v):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,is(t,e),f=e.memoizedProps,R=Ql(l,f),i.props=R,C=e.pendingProps,T=i.context,E=l.contextType,v=fa,typeof E=="object"&&E!==null&&(v=ee(E)),h=l.getDerivedStateFromProps,(E=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==C||T!==v)&&Fo(e,i,a,v),cl=!1,T=e.memoizedState,i.state=T,dn(e,a,i,n),fn();var A=e.memoizedState;f!==C||T!==A||cl||t!==null&&t.dependencies!==null&&hi(t.dependencies)?(typeof h=="function"&&(As(e,l,h,a),A=e.memoizedState),(R=cl||Wo(e,l,R,a,T,A,v)||t!==null&&t.dependencies!==null&&hi(t.dependencies))?(E||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,A,v),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,A,v)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=A),i.props=a,i.state=A,i.context=v,a=R):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,wi(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=Sa(e,t.child,null,n),e.child=Sa(e,null,l,n)):Wt(t,e,l,n),e.memoizedState=i.state,t=e.child):t=Je(t,e,n),t}function hf(t,e,l,a){return ln(),e.flags|=256,Wt(t,e,l,a),e.child}var Ms={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ds(t){return{baseLanes:t,cachePool:Pr()}}function ws(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ae),t}function gf(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,f;if((f=i)||(f=t!==null&&t.memoizedState===null?!1:(Qt.current&2)!==0),f&&(n=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(vt){if(n?dl(e):hl(),vt){var h=Lt,v;if(v=h){t:{for(v=h,h=we;v.nodeType!==8;){if(!h){h=null;break t}if(v=Me(v.nextSibling),v===null){h=null;break t}}h=v}h!==null?(e.memoizedState={dehydrated:h,treeContext:Ll!==null?{id:Ve,overflow:ke}:null,retryLane:536870912,hydrationErrors:null},v=he(18,null,null,0),v.stateNode=h,v.return=e,e.child=v,ae=e,Lt=null,v=!0):v=!1}v||ql(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return mc(h)?e.lanes=32:e.lanes=536870912,null;Ke(e)}return h=a.children,a=a.fallback,n?(hl(),n=e.mode,h=Ci({mode:"hidden",children:h},n),a=jl(a,n,l,null),h.return=e,a.return=e,h.sibling=a,e.child=h,n=e.child,n.memoizedState=Ds(l),n.childLanes=ws(t,f,l),e.memoizedState=Ms,a):(dl(e),Cs(e,h))}if(v=t.memoizedState,v!==null&&(h=v.dehydrated,h!==null)){if(i)e.flags&256?(dl(e),e.flags&=-257,e=Us(t,e,l)):e.memoizedState!==null?(hl(),e.child=t.child,e.flags|=128,e=null):(hl(),n=a.fallback,h=e.mode,a=Ci({mode:"visible",children:a.children},h),n=jl(n,h,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Sa(e,t.child,null,l),a=e.child,a.memoizedState=Ds(l),a.childLanes=ws(t,f,l),e.memoizedState=Ms,e=n);else if(dl(e),mc(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var E=f.dgst;f=E,a=Error(s(419)),a.stack="",a.digest=f,an({value:a,source:null,stack:null}),e=Us(t,e,l)}else if(Kt||nn(t,e,l,!1),f=(l&t.childLanes)!==0,Kt||f){if(f=Mt,f!==null&&(a=l&-l,a=(a&42)!==0?1:pu(a),a=(a&(f.suspendedLanes|l))!==0?0:a,a!==0&&a!==v.retryLane))throw v.retryLane=a,oa(t,a),ve(f,t,a),nf;h.data==="$?"||Ps(),e=Us(t,e,l)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=v.treeContext,Lt=Me(h.nextSibling),ae=e,vt=!0,Bl=null,we=!1,t!==null&&(Oe[Ee++]=Ve,Oe[Ee++]=ke,Oe[Ee++]=Ll,Ve=t.id,ke=t.overflow,Ll=e),e=Cs(e,a.children),e.flags|=4096);return e}return n?(hl(),n=a.fallback,h=e.mode,v=t.child,E=v.sibling,a=Ge(v,{mode:"hidden",children:a.children}),a.subtreeFlags=v.subtreeFlags&65011712,E!==null?n=Ge(E,n):(n=jl(n,h,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,h=t.child.memoizedState,h===null?h=Ds(l):(v=h.cachePool,v!==null?(E=kt._currentValue,v=v.parent!==E?{parent:E,pool:E}:v):v=Pr(),h={baseLanes:h.baseLanes|l,cachePool:v}),n.memoizedState=h,n.childLanes=ws(t,f,l),e.memoizedState=Ms,a):(dl(e),l=t.child,t=l.sibling,l=Ge(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=l,e.memoizedState=null,l)}function Cs(t,e){return e=Ci({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ci(t,e){return t=he(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Us(t,e,l){return Sa(e,t.child,null,l),t=Cs(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function mf(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Pu(t.return,e,l)}function js(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function pf(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(Wt(t,e,a.children,l),a=Qt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&mf(t,l,e);else if(t.tag===19)mf(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(L(Qt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Ri(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),js(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Ri(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}js(e,!0,l,null,i);break;case"together":js(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Je(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),vl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(nn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=Ge(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=Ge(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Ls(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&hi(t)))}function gm(t,e,l){switch(e.tag){case 3:Nt(e,e.stateNode.containerInfo),sl(e,kt,t.memoizedState.cache),ln();break;case 27:case 5:el(e);break;case 4:Nt(e,e.stateNode.containerInfo);break;case 10:sl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(dl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?gf(t,e,l):(dl(e),t=Je(t,e,l),t!==null?t.sibling:null);dl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(nn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return pf(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(Qt,Qt.current),a)break;return null;case 22:case 23:return e.lanes=0,rf(t,e,l);case 24:sl(e,kt,t.memoizedState.cache)}return Je(t,e,l)}function yf(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Kt=!0;else{if(!Ls(t,l)&&(e.flags&128)===0)return Kt=!1,gm(t,e,l);Kt=(t.flags&131072)!==0}else Kt=!1,vt&&(e.flags&1048576)!==0&&Xr(e,di,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Xu(a)?(t=Ql(a,t),e.tag=1,e=df(null,e,a,t,l)):(e.tag=0,e=Rs(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===ot){e.tag=11,e=uf(null,e,a,t,l);break t}else if(n===mt){e.tag=14,e=sf(null,e,a,t,l);break t}}throw e=Ut(a)||a,Error(s(306,e,""))}}return e;case 0:return Rs(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=Ql(a,e.pendingProps),df(t,e,a,n,l);case 3:t:{if(Nt(e,e.stateNode.containerInfo),t===null)throw Error(s(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,is(t,e),dn(e,a,null,l);var f=e.memoizedState;if(a=f.cache,sl(e,kt,a),a!==i.cache&&Iu(e,[kt],l,!0),fn(),a=f.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=hf(t,e,a,l);break t}else if(a!==n){n=Se(Error(s(424)),e),an(n),e=hf(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Lt=Me(t.firstChild),ae=e,vt=!0,Bl=null,we=!0,l=Jo(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(ln(),a===n){e=Je(t,e,l);break t}Wt(t,e,a,l)}e=e.child}return e;case 26:return wi(t,e),t===null?(l=Sd(e.type,null,e.pendingProps,null))?e.memoizedState=l:vt||(l=e.type,t=e.pendingProps,a=Ki(I.current).createElement(l),a[te]=e,a[ne]=t,Pt(a,l,t),Zt(a),e.stateNode=a):e.memoizedState=Sd(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return el(e),t===null&&vt&&(a=e.stateNode=vd(e.type,e.pendingProps,I.current),ae=e,we=!0,n=Lt,_l(e.type)?(pc=n,Lt=Me(a.firstChild)):Lt=n),Wt(t,e,e.pendingProps.children,l),wi(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&vt&&((n=a=Lt)&&(a=Vm(a,e.type,e.pendingProps,we),a!==null?(e.stateNode=a,ae=e,Lt=Me(a.firstChild),we=!1,n=!0):n=!1),n||ql(e)),el(e),n=e.type,i=e.pendingProps,f=t!==null?t.memoizedProps:null,a=i.children,dc(n,i)?a=null:f!==null&&dc(n,f)&&(e.flags|=32),e.memoizedState!==null&&(n=fs(t,e,um,null,null,l),Un._currentValue=n),wi(t,e),Wt(t,e,a,l),e.child;case 6:return t===null&&vt&&((t=l=Lt)&&(l=km(l,e.pendingProps,we),l!==null?(e.stateNode=l,ae=e,Lt=null,t=!0):t=!1),t||ql(e)),null;case 13:return gf(t,e,l);case 4:return Nt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Sa(e,null,a,l):Wt(t,e,a,l),e.child;case 11:return uf(t,e,e.type,e.pendingProps,l);case 7:return Wt(t,e,e.pendingProps,l),e.child;case 8:return Wt(t,e,e.pendingProps.children,l),e.child;case 12:return Wt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,sl(e,e.type,a.value),Wt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,Gl(e),n=ee(n),a=a(n),e.flags|=1,Wt(t,e,a,l),e.child;case 14:return sf(t,e,e.type,e.pendingProps,l);case 15:return cf(t,e,e.type,e.pendingProps,l);case 19:return pf(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Ci(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=Ge(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return rf(t,e,l);case 24:return Gl(e),a=ee(kt),t===null?(n=ls(),n===null&&(n=Mt,i=ts(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},ns(e),sl(e,kt,n)):((t.lanes&l)!==0&&(is(t,e),dn(e,null,null,l),fn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),sl(e,kt,a)):(a=i.cache,sl(e,kt,a),a!==n.cache&&Iu(e,[kt],l,!0))),Wt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function $e(t){t.flags|=4}function vf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ad(e)){if(e=Te.current,e!==null&&((gt&4194048)===gt?Ce!==null:(gt&62914560)!==gt&&(gt&536870912)===0||e!==Ce))throw rn=as,Ir;t.flags|=8192}}function Ui(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Jc():536870912,t.lanes|=e,Ta|=e)}function bn(t,e){if(!vt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function jt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function mm(t,e,l){var a=e.pendingProps;switch($u(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return jt(e),null;case 1:return jt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Xe(kt),re(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(en(e)?$e(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Jr())),jt(e),null;case 26:return l=e.memoizedState,t===null?($e(e),l!==null?(jt(e),vf(e,l)):(jt(e),e.flags&=-16777217)):l?l!==t.memoizedState?($e(e),jt(e),vf(e,l)):(jt(e),e.flags&=-16777217):(t.memoizedProps!==a&&$e(e),jt(e),e.flags&=-16777217),null;case 27:ll(e),l=I.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&$e(e);else{if(!a){if(e.stateNode===null)throw Error(s(166));return jt(e),null}t=V.current,en(e)?Zr(e):(t=vd(n,a,l),e.stateNode=t,$e(e))}return jt(e),null;case 5:if(ll(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&$e(e);else{if(!a){if(e.stateNode===null)throw Error(s(166));return jt(e),null}if(t=V.current,en(e))Zr(e);else{switch(n=Ki(I.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[te]=e,t[ne]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Pt(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&$e(e)}}return jt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&$e(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(s(166));if(t=I.current,en(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=ae,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[te]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||fd(t.nodeValue,l)),t||ql(e)}else t=Ki(t).createTextNode(a),t[te]=e,e.stateNode=t}return jt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(s(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[te]=e}else ln(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;jt(e),n=!1}else n=Jr(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ke(e),e):(Ke(e),null)}if(Ke(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),Ui(e,e.updateQueue),jt(e),null;case 4:return re(),t===null&&sc(e.stateNode.containerInfo),jt(e),null;case 10:return Xe(e.type),jt(e),null;case 19:if(Y(Qt),n=e.memoizedState,n===null)return jt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)bn(n,!1);else{if(Ht!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=Ri(t),i!==null){for(e.flags|=128,bn(n,!1),t=i.updateQueue,e.updateQueue=t,Ui(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Qr(l,t),l=l.sibling;return L(Qt,Qt.current&1|2),e.child}t=t.sibling}n.tail!==null&&De()>Hi&&(e.flags|=128,a=!0,bn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Ri(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,Ui(e,t),bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!vt)return jt(e),null}else 2*De()-n.renderingStartTime>Hi&&l!==536870912&&(e.flags|=128,a=!0,bn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=De(),e.sibling=null,t=Qt.current,L(Qt,a?t&1|2:t&1),e):(jt(e),null);case 22:case 23:return Ke(e),rs(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(jt(e),e.subtreeFlags&6&&(e.flags|=8192)):jt(e),l=e.updateQueue,l!==null&&Ui(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(Vl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Xe(kt),jt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function pm(t,e){switch($u(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Xe(kt),re(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return ll(e),null;case 13:if(Ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));ln()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Qt),null;case 4:return re(),null;case 10:return Xe(e.type),null;case 22:case 23:return Ke(e),rs(),t!==null&&Y(Vl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Xe(kt),null;case 25:return null;default:return null}}function bf(t,e){switch($u(e),e.tag){case 3:Xe(kt),re();break;case 26:case 27:case 5:ll(e);break;case 4:re();break;case 13:Ke(e);break;case 19:Y(Qt);break;case 10:Xe(e.type);break;case 22:case 23:Ke(e),rs(),t!==null&&Y(Vl);break;case 24:Xe(kt)}}function xn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,f=l.inst;a=i(),f.destroy=a}l=l.next}while(l!==n)}}catch(h){zt(e,e.return,h)}}function gl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var f=a.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,n=e;var v=l,E=h;try{E()}catch(R){zt(n,v,R)}}}a=a.next}while(a!==i)}}catch(R){zt(e,e.return,R)}}function xf(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{io(e,l)}catch(a){zt(t,t.return,a)}}}function Sf(t,e,l){l.props=Ql(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){zt(t,e,a)}}function Sn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){zt(t,e,n)}}function Ue(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){zt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){zt(t,e,n)}else l.current=null}function _f(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){zt(t,t.return,n)}}function Hs(t,e,l){try{var a=t.stateNode;Hm(a,t.type,l,e),a[ne]=e}catch(n){zt(t,t.return,n)}}function Of(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&_l(t.type)||t.tag===4}function Bs(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Of(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&_l(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function qs(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=Zi));else if(a!==4&&(a===27&&_l(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(qs(t,e,l),t=t.sibling;t!==null;)qs(t,e,l),t=t.sibling}function ji(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&_l(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ji(t,e,l),t=t.sibling;t!==null;)ji(t,e,l),t=t.sibling}function Ef(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Pt(e,a,l),e[te]=t,e[ne]=l}catch(i){zt(t,t.return,i)}}var We=!1,Yt=!1,Ys=!1,Tf=typeof WeakSet=="function"?WeakSet:Set,Jt=null;function ym(t,e){if(t=t.containerInfo,oc=Ii,t=Ur(t),Bu(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var f=0,h=-1,v=-1,E=0,R=0,C=t,T=null;e:for(;;){for(var A;C!==l||n!==0&&C.nodeType!==3||(h=f+n),C!==i||a!==0&&C.nodeType!==3||(v=f+a),C.nodeType===3&&(f+=C.nodeValue.length),(A=C.firstChild)!==null;)T=C,C=A;for(;;){if(C===t)break e;if(T===l&&++E===n&&(h=f),T===i&&++R===a&&(v=f),(A=C.nextSibling)!==null)break;C=T,T=C.parentNode}C=A}l=h===-1||v===-1?null:{start:h,end:v}}else l=null}l=l||{start:0,end:0}}else l=null;for(fc={focusedElem:t,selectionRange:l},Ii=!1,Jt=e;Jt!==null;)if(e=Jt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Jt=t;else for(;Jt!==null;){switch(e=Jt,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var P=Ql(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(P,i),a.__reactInternalSnapshotBeforeUpdate=t}catch($){zt(l,l.return,$)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)gc(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Jt=t;break}Jt=e.return}}function Af(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:ml(t,l),a&4&&xn(5,l);break;case 1:if(ml(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(f){zt(l,l.return,f)}else{var n=Ql(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){zt(l,l.return,f)}}a&64&&xf(l),a&512&&Sn(l,l.return);break;case 3:if(ml(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{io(t,e)}catch(f){zt(l,l.return,f)}}break;case 27:e===null&&a&4&&Ef(l);case 26:case 5:ml(t,l),e===null&&a&4&&_f(l),a&512&&Sn(l,l.return);break;case 12:ml(t,l);break;case 13:ml(t,l),a&4&&Rf(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Am.bind(null,l),Qm(t,l))));break;case 22:if(a=l.memoizedState!==null||We,!a){e=e!==null&&e.memoizedState!==null||Yt,n=We;var i=Yt;We=a,(Yt=e)&&!i?pl(t,l,(l.subtreeFlags&8772)!==0):ml(t,l),We=n,Yt=i}break;case 30:break;default:ml(t,l)}}function Nf(t){var e=t.alternate;e!==null&&(t.alternate=null,Nf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&bu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var wt=null,se=!1;function Fe(t,e,l){for(l=l.child;l!==null;)zf(t,e,l),l=l.sibling}function zf(t,e,l){if(oe&&typeof oe.onCommitFiberUnmount=="function")try{oe.onCommitFiberUnmount(Ga,l)}catch{}switch(l.tag){case 26:Yt||Ue(l,e),Fe(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Yt||Ue(l,e);var a=wt,n=se;_l(l.type)&&(wt=l.stateNode,se=!1),Fe(t,e,l),Mn(l.stateNode),wt=a,se=n;break;case 5:Yt||Ue(l,e);case 6:if(a=wt,n=se,wt=null,Fe(t,e,l),wt=a,se=n,wt!==null)if(se)try{(wt.nodeType===9?wt.body:wt.nodeName==="HTML"?wt.ownerDocument.body:wt).removeChild(l.stateNode)}catch(i){zt(l,e,i)}else try{wt.removeChild(l.stateNode)}catch(i){zt(l,e,i)}break;case 18:wt!==null&&(se?(t=wt,pd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Bn(t)):pd(wt,l.stateNode));break;case 4:a=wt,n=se,wt=l.stateNode.containerInfo,se=!0,Fe(t,e,l),wt=a,se=n;break;case 0:case 11:case 14:case 15:Yt||gl(2,l,e),Yt||gl(4,l,e),Fe(t,e,l);break;case 1:Yt||(Ue(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Sf(l,e,a)),Fe(t,e,l);break;case 21:Fe(t,e,l);break;case 22:Yt=(a=Yt)||l.memoizedState!==null,Fe(t,e,l),Yt=a;break;default:Fe(t,e,l)}}function Rf(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Bn(t)}catch(l){zt(e,e.return,l)}}function vm(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Tf),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Tf),e;default:throw Error(s(435,t.tag))}}function Gs(t,e){var l=vm(t);e.forEach(function(a){var n=Nm.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function ge(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,f=e,h=f;t:for(;h!==null;){switch(h.tag){case 27:if(_l(h.type)){wt=h.stateNode,se=!1;break t}break;case 5:wt=h.stateNode,se=!1;break t;case 3:case 4:wt=h.stateNode.containerInfo,se=!0;break t}h=h.return}if(wt===null)throw Error(s(160));zf(i,f,n),wt=null,se=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Mf(e,t),e=e.sibling}var Re=null;function Mf(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ge(e,t),me(t),a&4&&(gl(3,t,t.return),xn(3,t),gl(5,t,t.return));break;case 1:ge(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),a&64&&We&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Re;if(ge(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[Qa]||i[te]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),Pt(i,a,l),i[te]=t,Zt(i),a=i;break t;case"link":var f=Ed("link","href",n).get(a+(l.href||""));if(f){for(var h=0;h<f.length;h++)if(i=f[h],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(h,1);break e}}i=n.createElement(a),Pt(i,a,l),n.head.appendChild(i);break;case"meta":if(f=Ed("meta","content",n).get(a+(l.content||""))){for(h=0;h<f.length;h++)if(i=f[h],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(h,1);break e}}i=n.createElement(a),Pt(i,a,l),n.head.appendChild(i);break;default:throw Error(s(468,a))}i[te]=t,Zt(i),a=i}t.stateNode=a}else Td(n,t.type,t.stateNode);else t.stateNode=Od(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?Td(n,t.type,t.stateNode):Od(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Hs(t,t.memoizedProps,l.memoizedProps)}break;case 27:ge(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),l!==null&&a&4&&Hs(t,t.memoizedProps,l.memoizedProps);break;case 5:if(ge(e,t),me(t),a&512&&(Yt||l===null||Ue(l,l.return)),t.flags&32){n=t.stateNode;try{aa(n,"")}catch(A){zt(t,t.return,A)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Hs(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Ys=!0);break;case 6:if(ge(e,t),me(t),a&4){if(t.stateNode===null)throw Error(s(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(A){zt(t,t.return,A)}}break;case 3:if(Wi=null,n=Re,Re=Ji(e.containerInfo),ge(e,t),Re=n,me(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Bn(e.containerInfo)}catch(A){zt(t,t.return,A)}Ys&&(Ys=!1,Df(t));break;case 4:a=Re,Re=Ji(t.stateNode.containerInfo),ge(e,t),me(t),Re=a;break;case 12:ge(e,t),me(t);break;case 13:ge(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Ks=De()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Gs(t,a)));break;case 22:n=t.memoizedState!==null;var v=l!==null&&l.memoizedState!==null,E=We,R=Yt;if(We=E||n,Yt=R||v,ge(e,t),Yt=R,We=E,me(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||v||We||Yt||Xl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){v=l=e;try{if(i=v.stateNode,n)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=v.stateNode;var C=v.memoizedProps.style,T=C!=null&&C.hasOwnProperty("display")?C.display:null;h.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(A){zt(v,v.return,A)}}}else if(e.tag===6){if(l===null){v=e;try{v.stateNode.nodeValue=n?"":v.memoizedProps}catch(A){zt(v,v.return,A)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Gs(t,l))));break;case 19:ge(e,t),me(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Gs(t,a)));break;case 30:break;case 21:break;default:ge(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Of(a)){l=a;break}a=a.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var n=l.stateNode,i=Bs(t);ji(t,i,n);break;case 5:var f=l.stateNode;l.flags&32&&(aa(f,""),l.flags&=-33);var h=Bs(t);ji(t,h,f);break;case 3:case 4:var v=l.stateNode.containerInfo,E=Bs(t);qs(t,E,v);break;default:throw Error(s(161))}}catch(R){zt(t,t.return,R)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Df(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Df(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Af(t,e.alternate,e),e=e.sibling}function Xl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:gl(4,e,e.return),Xl(e);break;case 1:Ue(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Sf(e,e.return,l),Xl(e);break;case 27:Mn(e.stateNode);case 26:case 5:Ue(e,e.return),Xl(e);break;case 22:e.memoizedState===null&&Xl(e);break;case 30:Xl(e);break;default:Xl(e)}t=t.sibling}}function pl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,f=i.flags;switch(i.tag){case 0:case 11:case 15:pl(n,i,l),xn(4,i);break;case 1:if(pl(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(E){zt(a,a.return,E)}if(a=i,n=a.updateQueue,n!==null){var h=a.stateNode;try{var v=n.shared.hiddenCallbacks;if(v!==null)for(n.shared.hiddenCallbacks=null,n=0;n<v.length;n++)no(v[n],h)}catch(E){zt(a,a.return,E)}}l&&f&64&&xf(i),Sn(i,i.return);break;case 27:Ef(i);case 26:case 5:pl(n,i,l),l&&a===null&&f&4&&_f(i),Sn(i,i.return);break;case 12:pl(n,i,l);break;case 13:pl(n,i,l),l&&f&4&&Rf(n,i);break;case 22:i.memoizedState===null&&pl(n,i,l),Sn(i,i.return);break;case 30:break;default:pl(n,i,l)}e=e.sibling}}function Vs(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&un(l))}function ks(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function je(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)wf(t,e,l,a),e=e.sibling}function wf(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:je(t,e,l,a),n&2048&&xn(9,e);break;case 1:je(t,e,l,a);break;case 3:je(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){je(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,f=i.id,h=i.onPostCommit;typeof h=="function"&&h(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(v){zt(e,e.return,v)}}else je(t,e,l,a);break;case 13:je(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,f=e.alternate,e.memoizedState!==null?i._visibility&2?je(t,e,l,a):_n(t,e):i._visibility&2?je(t,e,l,a):(i._visibility|=2,_a(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Vs(f,e);break;case 24:je(t,e,l,a),n&2048&&ks(e.alternate,e);break;default:je(t,e,l,a)}}function _a(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,f=e,h=l,v=a,E=f.flags;switch(f.tag){case 0:case 11:case 15:_a(i,f,h,v,n),xn(8,f);break;case 23:break;case 22:var R=f.stateNode;f.memoizedState!==null?R._visibility&2?_a(i,f,h,v,n):_n(i,f):(R._visibility|=2,_a(i,f,h,v,n)),n&&E&2048&&Vs(f.alternate,f);break;case 24:_a(i,f,h,v,n),n&&E&2048&&ks(f.alternate,f);break;default:_a(i,f,h,v,n)}e=e.sibling}}function _n(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:_n(l,a),n&2048&&Vs(a.alternate,a);break;case 24:_n(l,a),n&2048&&ks(a.alternate,a);break;default:_n(l,a)}e=e.sibling}}var On=8192;function Oa(t){if(t.subtreeFlags&On)for(t=t.child;t!==null;)Cf(t),t=t.sibling}function Cf(t){switch(t.tag){case 26:Oa(t),t.flags&On&&t.memoizedState!==null&&ap(Re,t.memoizedState,t.memoizedProps);break;case 5:Oa(t);break;case 3:case 4:var e=Re;Re=Ji(t.stateNode.containerInfo),Oa(t),Re=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=On,On=16777216,Oa(t),On=e):Oa(t));break;default:Oa(t)}}function Uf(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function En(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Jt=a,Lf(a,t)}Uf(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)jf(t),t=t.sibling}function jf(t){switch(t.tag){case 0:case 11:case 15:En(t),t.flags&2048&&gl(9,t,t.return);break;case 3:En(t);break;case 12:En(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Li(t)):En(t);break;default:En(t)}}function Li(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];Jt=a,Lf(a,t)}Uf(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:gl(8,e,e.return),Li(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,Li(e));break;default:Li(e)}t=t.sibling}}function Lf(t,e){for(;Jt!==null;){var l=Jt;switch(l.tag){case 0:case 11:case 15:gl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:un(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Jt=a;else t:for(l=t;Jt!==null;){a=Jt;var n=a.sibling,i=a.return;if(Nf(a),a===l){Jt=null;break t}if(n!==null){n.return=i,Jt=n;break t}Jt=i}}}var bm={getCacheForType:function(t){var e=ee(kt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},xm=typeof WeakMap=="function"?WeakMap:Map,_t=0,Mt=null,ct=null,gt=0,Ot=0,pe=null,yl=!1,Ea=!1,Qs=!1,Pe=0,Ht=0,vl=0,Zl=0,Xs=0,Ae=0,Ta=0,Tn=null,ce=null,Zs=!1,Ks=0,Hi=1/0,Bi=null,bl=null,Ft=0,xl=null,Aa=null,Na=0,Js=0,$s=null,Hf=null,An=0,Ws=null;function ye(){if((_t&2)!==0&&gt!==0)return gt&-gt;if(N.T!==null){var t=ga;return t!==0?t:ac()}return Fc()}function Bf(){Ae===0&&(Ae=(gt&536870912)===0||vt?Kc():536870912);var t=Te.current;return t!==null&&(t.flags|=32),Ae}function ve(t,e,l){(t===Mt&&(Ot===2||Ot===9)||t.cancelPendingCommit!==null)&&(za(t,0),Sl(t,gt,Ae,!1)),ka(t,l),((_t&2)===0||t!==Mt)&&(t===Mt&&((_t&2)===0&&(Zl|=l),Ht===4&&Sl(t,gt,Ae,!1)),Le(t))}function qf(t,e,l){if((_t&6)!==0)throw Error(s(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Va(t,e),n=a?Om(t,e):Is(t,e,!0),i=a;do{if(n===0){Ea&&!a&&Sl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!Sm(l)){n=Is(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var h=t;n=Tn;var v=h.current.memoizedState.isDehydrated;if(v&&(za(h,f).flags|=256),f=Is(h,f,!1),f!==2){if(Qs&&!v){h.errorRecoveryDisabledLanes|=i,Zl|=i,n=4;break t}i=ce,ce=n,i!==null&&(ce===null?ce=i:ce.push.apply(ce,i))}n=f}if(i=!1,n!==2)continue}}if(n===1){za(t,0),Sl(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Sl(a,e,Ae,!yl);break t;case 2:ce=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(n=Ks+300-De(),10<n)){if(Sl(a,e,Ae,!yl),$n(a,0,!0)!==0)break t;a.timeoutHandle=gd(Yf.bind(null,a,l,ce,Bi,Zs,e,Ae,Zl,Ta,yl,i,2,-0,0),n);break t}Yf(a,l,ce,Bi,Zs,e,Ae,Zl,Ta,yl,i,0,-0,0)}}break}while(!0);Le(t)}function Yf(t,e,l,a,n,i,f,h,v,E,R,C,T,A){if(t.timeoutHandle=-1,C=e.subtreeFlags,(C&8192||(C&16785408)===16785408)&&(Cn={stylesheets:null,count:0,unsuspend:lp},Cf(e),C=np(),C!==null)){t.cancelPendingCommit=C(Kf.bind(null,t,e,i,l,a,n,f,h,v,R,1,T,A)),Sl(t,i,f,!E);return}Kf(t,e,i,l,a,n,f,h,v)}function Sm(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!de(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Sl(t,e,l,a){e&=~Xs,e&=~Zl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-fe(n),f=1<<i;a[i]=-1,n&=~f}l!==0&&$c(t,l,e)}function qi(){return(_t&6)===0?(Nn(0),!1):!0}function Fs(){if(ct!==null){if(Ot===0)var t=ct.return;else t=ct,Qe=Yl=null,gs(t),xa=null,yn=0,t=ct;for(;t!==null;)bf(t.alternate,t),t=t.return;ct=null}}function za(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,qm(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Fs(),Mt=t,ct=l=Ge(t.current,null),gt=e,Ot=0,pe=null,yl=!1,Ea=Va(t,e),Qs=!1,Ta=Ae=Xs=Zl=vl=Ht=0,ce=Tn=null,Zs=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-fe(a),i=1<<n;e|=t[n],a&=~i}return Pe=e,si(),l}function Gf(t,e){nt=null,N.H=Ai,e===cn||e===pi?(e=lo(),Ot=3):e===Ir?(e=lo(),Ot=4):Ot=e===nf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,pe=e,ct===null&&(Ht=1,Di(t,Se(e,t.current)))}function Vf(){var t=N.H;return N.H=Ai,t===null?Ai:t}function kf(){var t=N.A;return N.A=bm,t}function Ps(){Ht=4,yl||(gt&4194048)!==gt&&Te.current!==null||(Ea=!0),(vl&134217727)===0&&(Zl&134217727)===0||Mt===null||Sl(Mt,gt,Ae,!1)}function Is(t,e,l){var a=_t;_t|=2;var n=Vf(),i=kf();(Mt!==t||gt!==e)&&(Bi=null,za(t,e)),e=!1;var f=Ht;t:do try{if(Ot!==0&&ct!==null){var h=ct,v=pe;switch(Ot){case 8:Fs(),f=6;break t;case 3:case 2:case 9:case 6:Te.current===null&&(e=!0);var E=Ot;if(Ot=0,pe=null,Ra(t,h,v,E),l&&Ea){f=0;break t}break;default:E=Ot,Ot=0,pe=null,Ra(t,h,v,E)}}_m(),f=Ht;break}catch(R){Gf(t,R)}while(!0);return e&&t.shellSuspendCounter++,Qe=Yl=null,_t=a,N.H=n,N.A=i,ct===null&&(Mt=null,gt=0,si()),f}function _m(){for(;ct!==null;)Qf(ct)}function Om(t,e){var l=_t;_t|=2;var a=Vf(),n=kf();Mt!==t||gt!==e?(Bi=null,Hi=De()+500,za(t,e)):Ea=Va(t,e);t:do try{if(Ot!==0&&ct!==null){e=ct;var i=pe;e:switch(Ot){case 1:Ot=0,pe=null,Ra(t,e,i,1);break;case 2:case 9:if(to(i)){Ot=0,pe=null,Xf(e);break}e=function(){Ot!==2&&Ot!==9||Mt!==t||(Ot=7),Le(t)},i.then(e,e);break t;case 3:Ot=7;break t;case 4:Ot=5;break t;case 7:to(i)?(Ot=0,pe=null,Xf(e)):(Ot=0,pe=null,Ra(t,e,i,7));break;case 5:var f=null;switch(ct.tag){case 26:f=ct.memoizedState;case 5:case 27:var h=ct;if(!f||Ad(f)){Ot=0,pe=null;var v=h.sibling;if(v!==null)ct=v;else{var E=h.return;E!==null?(ct=E,Yi(E)):ct=null}break e}}Ot=0,pe=null,Ra(t,e,i,5);break;case 6:Ot=0,pe=null,Ra(t,e,i,6);break;case 8:Fs(),Ht=6;break t;default:throw Error(s(462))}}Em();break}catch(R){Gf(t,R)}while(!0);return Qe=Yl=null,N.H=a,N.A=n,_t=l,ct!==null?0:(Mt=null,gt=0,si(),Ht)}function Em(){for(;ct!==null&&!Zh();)Qf(ct)}function Qf(t){var e=yf(t.alternate,t,Pe);t.memoizedProps=t.pendingProps,e===null?Yi(t):ct=e}function Xf(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=ff(l,e,e.pendingProps,e.type,void 0,gt);break;case 11:e=ff(l,e,e.pendingProps,e.type.render,e.ref,gt);break;case 5:gs(e);default:bf(l,e),e=ct=Qr(e,Pe),e=yf(l,e,Pe)}t.memoizedProps=t.pendingProps,e===null?Yi(t):ct=e}function Ra(t,e,l,a){Qe=Yl=null,gs(e),xa=null,yn=0;var n=e.return;try{if(hm(t,n,e,l,gt)){Ht=1,Di(t,Se(l,t.current)),ct=null;return}}catch(i){if(n!==null)throw ct=n,i;Ht=1,Di(t,Se(l,t.current)),ct=null;return}e.flags&32768?(vt||a===1?t=!0:Ea||(gt&536870912)!==0?t=!1:(yl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Te.current,a!==null&&a.tag===13&&(a.flags|=16384))),Zf(e,t)):Yi(e)}function Yi(t){var e=t;do{if((e.flags&32768)!==0){Zf(e,yl);return}t=e.return;var l=mm(e.alternate,e,Pe);if(l!==null){ct=l;return}if(e=e.sibling,e!==null){ct=e;return}ct=e=t}while(e!==null);Ht===0&&(Ht=5)}function Zf(t,e){do{var l=pm(t.alternate,t);if(l!==null){l.flags&=32767,ct=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ct=t;return}ct=t=l}while(t!==null);Ht=6,ct=null}function Kf(t,e,l,a,n,i,f,h,v){t.cancelPendingCommit=null;do Gi();while(Ft!==0);if((_t&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(i=e.lanes|e.childLanes,i|=ku,lg(t,l,i,f,h,v),t===Mt&&(ct=Mt=null,gt=0),Aa=e,xl=t,Na=l,Js=i,$s=n,Hf=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,zm(Zn,function(){return Pf(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=N.T,N.T=null,n=H.p,H.p=2,f=_t,_t|=4;try{ym(t,e,l)}finally{_t=f,H.p=n,N.T=a}}Ft=1,Jf(),$f(),Wf()}}function Jf(){if(Ft===1){Ft=0;var t=xl,e=Aa,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=N.T,N.T=null;var a=H.p;H.p=2;var n=_t;_t|=4;try{Mf(e,t);var i=fc,f=Ur(t.containerInfo),h=i.focusedElem,v=i.selectionRange;if(f!==h&&h&&h.ownerDocument&&Cr(h.ownerDocument.documentElement,h)){if(v!==null&&Bu(h)){var E=v.start,R=v.end;if(R===void 0&&(R=E),"selectionStart"in h)h.selectionStart=E,h.selectionEnd=Math.min(R,h.value.length);else{var C=h.ownerDocument||document,T=C&&C.defaultView||window;if(T.getSelection){var A=T.getSelection(),P=h.textContent.length,$=Math.min(v.start,P),At=v.end===void 0?$:Math.min(v.end,P);!A.extend&&$>At&&(f=At,At=$,$=f);var S=wr(h,$),x=wr(h,At);if(S&&x&&(A.rangeCount!==1||A.anchorNode!==S.node||A.anchorOffset!==S.offset||A.focusNode!==x.node||A.focusOffset!==x.offset)){var O=C.createRange();O.setStart(S.node,S.offset),A.removeAllRanges(),$>At?(A.addRange(O),A.extend(x.node,x.offset)):(O.setEnd(x.node,x.offset),A.addRange(O))}}}}for(C=[],A=h;A=A.parentNode;)A.nodeType===1&&C.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<C.length;h++){var w=C[h];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}Ii=!!oc,fc=oc=null}finally{_t=n,H.p=a,N.T=l}}t.current=e,Ft=2}}function $f(){if(Ft===2){Ft=0;var t=xl,e=Aa,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=N.T,N.T=null;var a=H.p;H.p=2;var n=_t;_t|=4;try{Af(t,e.alternate,e)}finally{_t=n,H.p=a,N.T=l}}Ft=3}}function Wf(){if(Ft===4||Ft===3){Ft=0,Kh();var t=xl,e=Aa,l=Na,a=Hf;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ft=5:(Ft=0,Aa=xl=null,Ff(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(bl=null),yu(l),e=e.stateNode,oe&&typeof oe.onCommitFiberRoot=="function")try{oe.onCommitFiberRoot(Ga,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=N.T,n=H.p,H.p=2,N.T=null;try{for(var i=t.onRecoverableError,f=0;f<a.length;f++){var h=a[f];i(h.value,{componentStack:h.stack})}}finally{N.T=e,H.p=n}}(Na&3)!==0&&Gi(),Le(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Ws?An++:(An=0,Ws=t):An=0,Nn(0)}}function Ff(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Gi(t){return Jf(),$f(),Wf(),Pf()}function Pf(){if(Ft!==5)return!1;var t=xl,e=Js;Js=0;var l=yu(Na),a=N.T,n=H.p;try{H.p=32>l?32:l,N.T=null,l=$s,$s=null;var i=xl,f=Na;if(Ft=0,Aa=xl=null,Na=0,(_t&6)!==0)throw Error(s(331));var h=_t;if(_t|=4,jf(i.current),wf(i,i.current,f,l),_t=h,Nn(0,!1),oe&&typeof oe.onPostCommitFiberRoot=="function")try{oe.onPostCommitFiberRoot(Ga,i)}catch{}return!0}finally{H.p=n,N.T=a,Ff(t,e)}}function If(t,e,l){e=Se(l,e),e=zs(t.stateNode,e,2),t=ol(t,e,2),t!==null&&(ka(t,2),Le(t))}function zt(t,e,l){if(t.tag===3)If(t,t,l);else for(;e!==null;){if(e.tag===3){If(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(bl===null||!bl.has(a))){t=Se(l,t),l=lf(2),a=ol(e,l,2),a!==null&&(af(l,a,e,t),ka(a,2),Le(a));break}}e=e.return}}function tc(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new xm;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Qs=!0,n.add(l),t=Tm.bind(null,t,e,l),e.then(t,t))}function Tm(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Mt===t&&(gt&l)===l&&(Ht===4||Ht===3&&(gt&62914560)===gt&&300>De()-Ks?(_t&2)===0&&za(t,0):Xs|=l,Ta===gt&&(Ta=0)),Le(t)}function td(t,e){e===0&&(e=Jc()),t=oa(t,e),t!==null&&(ka(t,e),Le(t))}function Am(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),td(t,l)}function Nm(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(e),td(t,l)}function zm(t,e){return hu(t,e)}var Vi=null,Ma=null,ec=!1,ki=!1,lc=!1,Kl=0;function Le(t){t!==Ma&&t.next===null&&(Ma===null?Vi=Ma=t:Ma=Ma.next=t),ki=!0,ec||(ec=!0,Mm())}function Nn(t,e){if(!lc&&ki){lc=!0;do for(var l=!1,a=Vi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var f=a.suspendedLanes,h=a.pingedLanes;i=(1<<31-fe(42|t)+1)-1,i&=n&~(f&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,nd(a,i))}else i=gt,i=$n(a,a===Mt?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||Va(a,i)||(l=!0,nd(a,i));a=a.next}while(l);lc=!1}}function Rm(){ed()}function ed(){ki=ec=!1;var t=0;Kl!==0&&(Bm()&&(t=Kl),Kl=0);for(var e=De(),l=null,a=Vi;a!==null;){var n=a.next,i=ld(a,e);i===0?(a.next=null,l===null?Vi=n:l.next=n,n===null&&(Ma=l)):(l=a,(t!==0||(i&3)!==0)&&(ki=!0)),a=n}Nn(t)}function ld(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var f=31-fe(i),h=1<<f,v=n[f];v===-1?((h&l)===0||(h&a)!==0)&&(n[f]=eg(h,e)):v<=e&&(t.expiredLanes|=h),i&=~h}if(e=Mt,l=gt,l=$n(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(Ot===2||Ot===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&gu(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Va(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&gu(a),yu(l)){case 2:case 8:l=Xc;break;case 32:l=Zn;break;case 268435456:l=Zc;break;default:l=Zn}return a=ad.bind(null,t),l=hu(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&gu(a),t.callbackPriority=2,t.callbackNode=null,2}function ad(t,e){if(Ft!==0&&Ft!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Gi()&&t.callbackNode!==l)return null;var a=gt;return a=$n(t,t===Mt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(qf(t,a,e),ld(t,De()),t.callbackNode!=null&&t.callbackNode===l?ad.bind(null,t):null)}function nd(t,e){if(Gi())return null;qf(t,e,!0)}function Mm(){Ym(function(){(_t&6)!==0?hu(Qc,Rm):ed()})}function ac(){return Kl===0&&(Kl=Kc()),Kl}function id(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ti(""+t)}function ud(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Dm(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=id((n[ne]||null).action),f=a.submitter;f&&(e=(e=f[ne]||null)?id(e.formAction):f.getAttribute("formAction"),e!==null&&(i=e,f=null));var h=new ni("action","action",null,a,n);t.push({event:h,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Kl!==0){var v=f?ud(n,f):new FormData(n);Os(l,{pending:!0,data:v,method:n.method,action:i},null,v)}}else typeof i=="function"&&(h.preventDefault(),v=f?ud(n,f):new FormData(n),Os(l,{pending:!0,data:v,method:n.method,action:i},i,v))},currentTarget:n}]})}}for(var nc=0;nc<Vu.length;nc++){var ic=Vu[nc],wm=ic.toLowerCase(),Cm=ic[0].toUpperCase()+ic.slice(1);ze(wm,"on"+Cm)}ze(Hr,"onAnimationEnd"),ze(Br,"onAnimationIteration"),ze(qr,"onAnimationStart"),ze("dblclick","onDoubleClick"),ze("focusin","onFocus"),ze("focusout","onBlur"),ze(Wg,"onTransitionRun"),ze(Fg,"onTransitionStart"),ze(Pg,"onTransitionCancel"),ze(Yr,"onTransitionEnd"),ta("onMouseEnter",["mouseout","mouseover"]),ta("onMouseLeave",["mouseout","mouseover"]),ta("onPointerEnter",["pointerout","pointerover"]),ta("onPointerLeave",["pointerout","pointerover"]),Dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Um=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zn));function sd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var f=a.length-1;0<=f;f--){var h=a[f],v=h.instance,E=h.currentTarget;if(h=h.listener,v!==i&&n.isPropagationStopped())break t;i=h,n.currentTarget=E;try{i(n)}catch(R){Mi(R)}n.currentTarget=null,i=v}else for(f=0;f<a.length;f++){if(h=a[f],v=h.instance,E=h.currentTarget,h=h.listener,v!==i&&n.isPropagationStopped())break t;i=h,n.currentTarget=E;try{i(n)}catch(R){Mi(R)}n.currentTarget=null,i=v}}}}function rt(t,e){var l=e[vu];l===void 0&&(l=e[vu]=new Set);var a=t+"__bubble";l.has(a)||(cd(e,t,2,!1),l.add(a))}function uc(t,e,l){var a=0;e&&(a|=4),cd(l,t,a,e)}var Qi="_reactListening"+Math.random().toString(36).slice(2);function sc(t){if(!t[Qi]){t[Qi]=!0,Ic.forEach(function(l){l!=="selectionchange"&&(Um.has(l)||uc(l,!1,t),uc(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Qi]||(e[Qi]=!0,uc("selectionchange",!1,e))}}function cd(t,e,l,a){switch(wd(e)){case 2:var n=sp;break;case 8:n=cp;break;default:n=Sc}l=n.bind(null,e,l,t),n=void 0,!Ru||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function cc(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var h=a.stateNode.containerInfo;if(h===n)break;if(f===4)for(f=a.return;f!==null;){var v=f.tag;if((v===3||v===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;h!==null;){if(f=Fl(h),f===null)return;if(v=f.tag,v===5||v===6||v===26||v===27){a=i=f;continue t}h=h.parentNode}}a=a.return}hr(function(){var E=i,R=Nu(l),C=[];t:{var T=Gr.get(t);if(T!==void 0){var A=ni,P=t;switch(t){case"keypress":if(li(l)===0)break t;case"keydown":case"keyup":A=zg;break;case"focusin":P="focus",A=Cu;break;case"focusout":P="blur",A=Cu;break;case"beforeblur":case"afterblur":A=Cu;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=pr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=pg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Dg;break;case Hr:case Br:case qr:A=bg;break;case Yr:A=Cg;break;case"scroll":case"scrollend":A=gg;break;case"wheel":A=jg;break;case"copy":case"cut":case"paste":A=Sg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=vr;break;case"toggle":case"beforetoggle":A=Hg}var $=(e&4)!==0,At=!$&&(t==="scroll"||t==="scrollend"),S=$?T!==null?T+"Capture":null:T;$=[];for(var x=E,O;x!==null;){var w=x;if(O=w.stateNode,w=w.tag,w!==5&&w!==26&&w!==27||O===null||S===null||(w=Za(x,S),w!=null&&$.push(Rn(x,w,O))),At)break;x=x.return}0<$.length&&(T=new A(T,P,null,l,R),C.push({event:T,listeners:$}))}}if((e&7)===0){t:{if(T=t==="mouseover"||t==="pointerover",A=t==="mouseout"||t==="pointerout",T&&l!==Au&&(P=l.relatedTarget||l.fromElement)&&(Fl(P)||P[Wl]))break t;if((A||T)&&(T=R.window===R?R:(T=R.ownerDocument)?T.defaultView||T.parentWindow:window,A?(P=l.relatedTarget||l.toElement,A=E,P=P?Fl(P):null,P!==null&&(At=d(P),$=P.tag,P!==At||$!==5&&$!==27&&$!==6)&&(P=null)):(A=null,P=E),A!==P)){if($=pr,w="onMouseLeave",S="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&($=vr,w="onPointerLeave",S="onPointerEnter",x="pointer"),At=A==null?T:Xa(A),O=P==null?T:Xa(P),T=new $(w,x+"leave",A,l,R),T.target=At,T.relatedTarget=O,w=null,Fl(R)===E&&($=new $(S,x+"enter",P,l,R),$.target=O,$.relatedTarget=At,w=$),At=w,A&&P)e:{for($=A,S=P,x=0,O=$;O;O=Da(O))x++;for(O=0,w=S;w;w=Da(w))O++;for(;0<x-O;)$=Da($),x--;for(;0<O-x;)S=Da(S),O--;for(;x--;){if($===S||S!==null&&$===S.alternate)break e;$=Da($),S=Da(S)}$=null}else $=null;A!==null&&rd(C,T,A,$,!1),P!==null&&At!==null&&rd(C,At,P,$,!0)}}t:{if(T=E?Xa(E):window,A=T.nodeName&&T.nodeName.toLowerCase(),A==="select"||A==="input"&&T.type==="file")var k=Ar;else if(Er(T))if(Nr)k=Kg;else{k=Xg;var ut=Qg}else A=T.nodeName,!A||A.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?E&&Tu(E.elementType)&&(k=Ar):k=Zg;if(k&&(k=k(t,E))){Tr(C,k,l,R);break t}ut&&ut(t,T,E),t==="focusout"&&E&&T.type==="number"&&E.memoizedProps.value!=null&&Eu(T,"number",T.value)}switch(ut=E?Xa(E):window,t){case"focusin":(Er(ut)||ut.contentEditable==="true")&&(sa=ut,qu=E,tn=null);break;case"focusout":tn=qu=sa=null;break;case"mousedown":Yu=!0;break;case"contextmenu":case"mouseup":case"dragend":Yu=!1,jr(C,l,R);break;case"selectionchange":if($g)break;case"keydown":case"keyup":jr(C,l,R)}var K;if(ju)t:{switch(t){case"compositionstart":var W="onCompositionStart";break t;case"compositionend":W="onCompositionEnd";break t;case"compositionupdate":W="onCompositionUpdate";break t}W=void 0}else ua?_r(t,l)&&(W="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(W="onCompositionStart");W&&(br&&l.locale!=="ko"&&(ua||W!=="onCompositionStart"?W==="onCompositionEnd"&&ua&&(K=gr()):(ul=R,Mu="value"in ul?ul.value:ul.textContent,ua=!0)),ut=Xi(E,W),0<ut.length&&(W=new yr(W,t,null,l,R),C.push({event:W,listeners:ut}),K?W.data=K:(K=Or(l),K!==null&&(W.data=K)))),(K=qg?Yg(t,l):Gg(t,l))&&(W=Xi(E,"onBeforeInput"),0<W.length&&(ut=new yr("onBeforeInput","beforeinput",null,l,R),C.push({event:ut,listeners:W}),ut.data=K)),Dm(C,t,E,l,R)}sd(C,e)})}function Rn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Xi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=Za(t,l),n!=null&&a.unshift(Rn(t,n,i)),n=Za(t,e),n!=null&&a.push(Rn(t,n,i))),t.tag===3)return a;t=t.return}return[]}function Da(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function rd(t,e,l,a,n){for(var i=e._reactName,f=[];l!==null&&l!==a;){var h=l,v=h.alternate,E=h.stateNode;if(h=h.tag,v!==null&&v===a)break;h!==5&&h!==26&&h!==27||E===null||(v=E,n?(E=Za(l,i),E!=null&&f.unshift(Rn(l,E,v))):n||(E=Za(l,i),E!=null&&f.push(Rn(l,E,v)))),l=l.return}f.length!==0&&t.push({event:e,listeners:f})}var jm=/\r\n?/g,Lm=/\u0000|\uFFFD/g;function od(t){return(typeof t=="string"?t:""+t).replace(jm,`
`).replace(Lm,"")}function fd(t,e){return e=od(e),od(t)===e}function Zi(){}function Tt(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||aa(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&aa(t,""+a);break;case"className":Fn(t,"class",a);break;case"tabIndex":Fn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Fn(t,l,a);break;case"style":fr(t,a,i);break;case"data":if(e!=="object"){Fn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=ti(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&Tt(t,e,"name",n.name,n,null),Tt(t,e,"formEncType",n.formEncType,n,null),Tt(t,e,"formMethod",n.formMethod,n,null),Tt(t,e,"formTarget",n.formTarget,n,null)):(Tt(t,e,"encType",n.encType,n,null),Tt(t,e,"method",n.method,n,null),Tt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=ti(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=Zi);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=ti(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":rt("beforetoggle",t),rt("toggle",t),Wn(t,"popover",a);break;case"xlinkActuate":qe(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":qe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":qe(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":qe(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":qe(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":qe(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":qe(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":qe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":qe(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Wn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=dg.get(l)||l,Wn(t,l,a))}}function rc(t,e,l,a,n,i){switch(l){case"style":fr(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof a=="string"?aa(t,a):(typeof a=="number"||typeof a=="bigint")&&aa(t,""+a);break;case"onScroll":a!=null&&rt("scroll",t);break;case"onScrollEnd":a!=null&&rt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=Zi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!tr.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[ne]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Wn(t,l,a)}}}function Pt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":rt("error",t),rt("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var f=l[i];if(f!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,i,f,l,null)}}n&&Tt(t,e,"srcSet",l.srcSet,l,null),a&&Tt(t,e,"src",l.src,l,null);return;case"input":rt("invalid",t);var h=i=f=n=null,v=null,E=null;for(a in l)if(l.hasOwnProperty(a)){var R=l[a];if(R!=null)switch(a){case"name":n=R;break;case"type":f=R;break;case"checked":v=R;break;case"defaultChecked":E=R;break;case"value":i=R;break;case"defaultValue":h=R;break;case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(s(137,e));break;default:Tt(t,e,a,R,l,null)}}sr(t,i,h,v,E,f,n,!1),Pn(t);return;case"select":rt("invalid",t),a=f=i=null;for(n in l)if(l.hasOwnProperty(n)&&(h=l[n],h!=null))switch(n){case"value":i=h;break;case"defaultValue":f=h;break;case"multiple":a=h;default:Tt(t,e,n,h,l,null)}e=i,l=f,t.multiple=!!a,e!=null?la(t,!!a,e,!1):l!=null&&la(t,!!a,l,!0);return;case"textarea":rt("invalid",t),i=n=a=null;for(f in l)if(l.hasOwnProperty(f)&&(h=l[f],h!=null))switch(f){case"value":a=h;break;case"defaultValue":n=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(s(91));break;default:Tt(t,e,f,h,l,null)}rr(t,a,n,i),Pn(t);return;case"option":for(v in l)if(l.hasOwnProperty(v)&&(a=l[v],a!=null))switch(v){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Tt(t,e,v,a,l,null)}return;case"dialog":rt("beforetoggle",t),rt("toggle",t),rt("cancel",t),rt("close",t);break;case"iframe":case"object":rt("load",t);break;case"video":case"audio":for(a=0;a<zn.length;a++)rt(zn[a],t);break;case"image":rt("error",t),rt("load",t);break;case"details":rt("toggle",t);break;case"embed":case"source":case"link":rt("error",t),rt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in l)if(l.hasOwnProperty(E)&&(a=l[E],a!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Tt(t,e,E,a,l,null)}return;default:if(Tu(e)){for(R in l)l.hasOwnProperty(R)&&(a=l[R],a!==void 0&&rc(t,e,R,a,l,void 0));return}}for(h in l)l.hasOwnProperty(h)&&(a=l[h],a!=null&&Tt(t,e,h,a,l,null))}function Hm(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,f=null,h=null,v=null,E=null,R=null;for(A in l){var C=l[A];if(l.hasOwnProperty(A)&&C!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":v=C;default:a.hasOwnProperty(A)||Tt(t,e,A,null,a,C)}}for(var T in a){var A=a[T];if(C=l[T],a.hasOwnProperty(T)&&(A!=null||C!=null))switch(T){case"type":i=A;break;case"name":n=A;break;case"checked":E=A;break;case"defaultChecked":R=A;break;case"value":f=A;break;case"defaultValue":h=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,e));break;default:A!==C&&Tt(t,e,T,A,a,C)}}Ou(t,f,h,v,E,R,i,n);return;case"select":A=f=h=T=null;for(i in l)if(v=l[i],l.hasOwnProperty(i)&&v!=null)switch(i){case"value":break;case"multiple":A=v;default:a.hasOwnProperty(i)||Tt(t,e,i,null,a,v)}for(n in a)if(i=a[n],v=l[n],a.hasOwnProperty(n)&&(i!=null||v!=null))switch(n){case"value":T=i;break;case"defaultValue":h=i;break;case"multiple":f=i;default:i!==v&&Tt(t,e,n,i,a,v)}e=h,l=f,a=A,T!=null?la(t,!!l,T,!1):!!a!=!!l&&(e!=null?la(t,!!l,e,!0):la(t,!!l,l?[]:"",!1));return;case"textarea":A=T=null;for(h in l)if(n=l[h],l.hasOwnProperty(h)&&n!=null&&!a.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Tt(t,e,h,null,a,n)}for(f in a)if(n=a[f],i=l[f],a.hasOwnProperty(f)&&(n!=null||i!=null))switch(f){case"value":T=n;break;case"defaultValue":A=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==i&&Tt(t,e,f,n,a,i)}cr(t,T,A);return;case"option":for(var P in l)if(T=l[P],l.hasOwnProperty(P)&&T!=null&&!a.hasOwnProperty(P))switch(P){case"selected":t.selected=!1;break;default:Tt(t,e,P,null,a,T)}for(v in a)if(T=a[v],A=l[v],a.hasOwnProperty(v)&&T!==A&&(T!=null||A!=null))switch(v){case"selected":t.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:Tt(t,e,v,T,a,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var $ in l)T=l[$],l.hasOwnProperty($)&&T!=null&&!a.hasOwnProperty($)&&Tt(t,e,$,null,a,T);for(E in a)if(T=a[E],A=l[E],a.hasOwnProperty(E)&&T!==A&&(T!=null||A!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(s(137,e));break;default:Tt(t,e,E,T,a,A)}return;default:if(Tu(e)){for(var At in l)T=l[At],l.hasOwnProperty(At)&&T!==void 0&&!a.hasOwnProperty(At)&&rc(t,e,At,void 0,a,T);for(R in a)T=a[R],A=l[R],!a.hasOwnProperty(R)||T===A||T===void 0&&A===void 0||rc(t,e,R,T,a,A);return}}for(var S in l)T=l[S],l.hasOwnProperty(S)&&T!=null&&!a.hasOwnProperty(S)&&Tt(t,e,S,null,a,T);for(C in a)T=a[C],A=l[C],!a.hasOwnProperty(C)||T===A||T==null&&A==null||Tt(t,e,C,T,a,A)}var oc=null,fc=null;function Ki(t){return t.nodeType===9?t:t.ownerDocument}function dd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function hd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function dc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var hc=null;function Bm(){var t=window.event;return t&&t.type==="popstate"?t===hc?!1:(hc=t,!0):(hc=null,!1)}var gd=typeof setTimeout=="function"?setTimeout:void 0,qm=typeof clearTimeout=="function"?clearTimeout:void 0,md=typeof Promise=="function"?Promise:void 0,Ym=typeof queueMicrotask=="function"?queueMicrotask:typeof md<"u"?function(t){return md.resolve(null).then(t).catch(Gm)}:gd;function Gm(t){setTimeout(function(){throw t})}function _l(t){return t==="head"}function pd(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var f=t.ownerDocument;if(l&1&&Mn(f.documentElement),l&2&&Mn(f.body),l&4)for(l=f.head,Mn(l),f=l.firstChild;f;){var h=f.nextSibling,v=f.nodeName;f[Qa]||v==="SCRIPT"||v==="STYLE"||v==="LINK"&&f.rel.toLowerCase()==="stylesheet"||l.removeChild(f),f=h}}if(n===0){t.removeChild(i),Bn(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);Bn(e)}function gc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":gc(l),bu(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Vm(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Qa])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Me(t.nextSibling),t===null)break}return null}function km(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Me(t.nextSibling),t===null))return null;return t}function mc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Qm(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Me(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var pc=null;function yd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function vd(t,e,l){switch(e=Ki(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function Mn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);bu(t)}var Ne=new Map,bd=new Set;function Ji(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ie=H.d;H.d={f:Xm,r:Zm,D:Km,C:Jm,L:$m,m:Wm,X:Pm,S:Fm,M:Im};function Xm(){var t=Ie.f(),e=qi();return t||e}function Zm(t){var e=Pl(t);e!==null&&e.tag===5&&e.type==="form"?Bo(e):Ie.r(t)}var wa=typeof document>"u"?null:document;function xd(t,e,l){var a=wa;if(a&&typeof e=="string"&&e){var n=xe(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),bd.has(n)||(bd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),Pt(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Km(t){Ie.D(t),xd("dns-prefetch",t,null)}function Jm(t,e){Ie.C(t,e),xd("preconnect",t,e)}function $m(t,e,l){Ie.L(t,e,l);var a=wa;if(a&&t&&e){var n='link[rel="preload"][as="'+xe(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+xe(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+xe(l.imageSizes)+'"]')):n+='[href="'+xe(t)+'"]';var i=n;switch(e){case"style":i=Ca(t);break;case"script":i=Ua(t)}Ne.has(i)||(t=_({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Ne.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Dn(i))||e==="script"&&a.querySelector(wn(i))||(e=a.createElement("link"),Pt(e,"link",t),Zt(e),a.head.appendChild(e)))}}function Wm(t,e){Ie.m(t,e);var l=wa;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+xe(a)+'"][href="'+xe(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ua(t)}if(!Ne.has(i)&&(t=_({rel:"modulepreload",href:t},e),Ne.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(wn(i)))return}a=l.createElement("link"),Pt(a,"link",t),Zt(a),l.head.appendChild(a)}}}function Fm(t,e,l){Ie.S(t,e,l);var a=wa;if(a&&t){var n=Il(a).hoistableStyles,i=Ca(t);e=e||"default";var f=n.get(i);if(!f){var h={loading:0,preload:null};if(f=a.querySelector(Dn(i)))h.loading=5;else{t=_({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Ne.get(i))&&yc(t,l);var v=f=a.createElement("link");Zt(v),Pt(v,"link",t),v._p=new Promise(function(E,R){v.onload=E,v.onerror=R}),v.addEventListener("load",function(){h.loading|=1}),v.addEventListener("error",function(){h.loading|=2}),h.loading|=4,$i(f,e,a)}f={type:"stylesheet",instance:f,count:1,state:h},n.set(i,f)}}}function Pm(t,e){Ie.X(t,e);var l=wa;if(l&&t){var a=Il(l).hoistableScripts,n=Ua(t),i=a.get(n);i||(i=l.querySelector(wn(n)),i||(t=_({src:t,async:!0},e),(e=Ne.get(n))&&vc(t,e),i=l.createElement("script"),Zt(i),Pt(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function Im(t,e){Ie.M(t,e);var l=wa;if(l&&t){var a=Il(l).hoistableScripts,n=Ua(t),i=a.get(n);i||(i=l.querySelector(wn(n)),i||(t=_({src:t,async:!0,type:"module"},e),(e=Ne.get(n))&&vc(t,e),i=l.createElement("script"),Zt(i),Pt(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function Sd(t,e,l,a){var n=(n=I.current)?Ji(n):null;if(!n)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Ca(l.href),l=Il(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Ca(l.href);var i=Il(n).hoistableStyles,f=i.get(t);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,f),(i=n.querySelector(Dn(t)))&&!i._p&&(f.instance=i,f.state.loading=5),Ne.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ne.set(t,l),i||tp(n,t,l,f.state))),e&&a===null)throw Error(s(528,""));return f}if(e&&a!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ua(l),l=Il(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Ca(t){return'href="'+xe(t)+'"'}function Dn(t){return'link[rel="stylesheet"]['+t+"]"}function _d(t){return _({},t,{"data-precedence":t.precedence,precedence:null})}function tp(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Pt(e,"link",l),Zt(e),t.head.appendChild(e))}function Ua(t){return'[src="'+xe(t)+'"]'}function wn(t){return"script[async]"+t}function Od(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+xe(l.href)+'"]');if(a)return e.instance=a,Zt(a),a;var n=_({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Zt(a),Pt(a,"style",n),$i(a,l.precedence,t),e.instance=a;case"stylesheet":n=Ca(l.href);var i=t.querySelector(Dn(n));if(i)return e.state.loading|=4,e.instance=i,Zt(i),i;a=_d(l),(n=Ne.get(n))&&yc(a,n),i=(t.ownerDocument||t).createElement("link"),Zt(i);var f=i;return f._p=new Promise(function(h,v){f.onload=h,f.onerror=v}),Pt(i,"link",a),e.state.loading|=4,$i(i,l.precedence,t),e.instance=i;case"script":return i=Ua(l.src),(n=t.querySelector(wn(i)))?(e.instance=n,Zt(n),n):(a=l,(n=Ne.get(i))&&(a=_({},l),vc(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Zt(n),Pt(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,$i(a,l.precedence,t));return e.instance}function $i(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,f=0;f<a.length;f++){var h=a[f];if(h.dataset.precedence===e)i=h;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function yc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function vc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Wi=null;function Ed(t,e,l){if(Wi===null){var a=new Map,n=Wi=new Map;n.set(l,a)}else n=Wi,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[Qa]||i[te]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(e)||"";f=t+f;var h=a.get(f);h?h.push(i):a.set(f,[i])}}return a}function Td(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function ep(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ad(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Cn=null;function lp(){}function ap(t,e,l){if(Cn===null)throw Error(s(475));var a=Cn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Ca(l.href),i=t.querySelector(Dn(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Fi.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,Zt(i);return}i=t.ownerDocument||t,l=_d(l),(n=Ne.get(n))&&yc(l,n),i=i.createElement("link"),Zt(i);var f=i;f._p=new Promise(function(h,v){f.onload=h,f.onerror=v}),Pt(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Fi.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function np(){if(Cn===null)throw Error(s(475));var t=Cn;return t.stylesheets&&t.count===0&&bc(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&bc(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Fi(){if(this.count--,this.count===0){if(this.stylesheets)bc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Pi=null;function bc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Pi=new Map,e.forEach(ip,t),Pi=null,Fi.call(t))}function ip(t,e){if(!(e.state.loading&4)){var l=Pi.get(t);if(l)var a=l.get(null);else{l=new Map,Pi.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var f=n[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),a=f)}a&&l.set(null,a)}n=e.instance,f=n.getAttribute("data-precedence"),i=l.get(f)||a,i===a&&l.set(null,n),l.set(f,n),this.count++,a=Fi.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Un={$$typeof:st,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function up(t,e,l,a,n,i,f,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=mu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mu(0),this.hiddenUpdates=mu(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function Nd(t,e,l,a,n,i,f,h,v,E,R,C){return t=new up(t,e,l,f,h,v,E,C),e=1,i===!0&&(e|=24),i=he(3,null,null,e),t.current=i,i.stateNode=t,e=ts(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},ns(i),t}function zd(t){return t?(t=fa,t):fa}function Rd(t,e,l,a,n,i){n=zd(n),a.context===null?a.context=n:a.pendingContext=n,a=rl(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=ol(t,a,e),l!==null&&(ve(l,t,e),on(l,t,e))}function Md(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function xc(t,e){Md(t,e),(t=t.alternate)&&Md(t,e)}function Dd(t){if(t.tag===13){var e=oa(t,67108864);e!==null&&ve(e,t,67108864),xc(t,67108864)}}var Ii=!0;function sp(t,e,l,a){var n=N.T;N.T=null;var i=H.p;try{H.p=2,Sc(t,e,l,a)}finally{H.p=i,N.T=n}}function cp(t,e,l,a){var n=N.T;N.T=null;var i=H.p;try{H.p=8,Sc(t,e,l,a)}finally{H.p=i,N.T=n}}function Sc(t,e,l,a){if(Ii){var n=_c(a);if(n===null)cc(t,e,a,tu,l),Cd(t,a);else if(op(n,t,e,l,a))a.stopPropagation();else if(Cd(t,a),e&4&&-1<rp.indexOf(t)){for(;n!==null;){var i=Pl(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=Ml(i.pendingLanes);if(f!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var v=1<<31-fe(f);h.entanglements[1]|=v,f&=~v}Le(i),(_t&6)===0&&(Hi=De()+500,Nn(0))}}break;case 13:h=oa(i,2),h!==null&&ve(h,i,2),qi(),xc(i,2)}if(i=_c(a),i===null&&cc(t,e,a,tu,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else cc(t,e,a,null,l)}}function _c(t){return t=Nu(t),Oc(t)}var tu=null;function Oc(t){if(tu=null,t=Fl(t),t!==null){var e=d(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=m(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return tu=t,null}function wd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Jh()){case Qc:return 2;case Xc:return 8;case Zn:case $h:return 32;case Zc:return 268435456;default:return 32}default:return 32}}var Ec=!1,Ol=null,El=null,Tl=null,jn=new Map,Ln=new Map,Al=[],rp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Cd(t,e){switch(t){case"focusin":case"focusout":Ol=null;break;case"dragenter":case"dragleave":El=null;break;case"mouseover":case"mouseout":Tl=null;break;case"pointerover":case"pointerout":jn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ln.delete(e.pointerId)}}function Hn(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=Pl(e),e!==null&&Dd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function op(t,e,l,a,n){switch(e){case"focusin":return Ol=Hn(Ol,t,e,l,a,n),!0;case"dragenter":return El=Hn(El,t,e,l,a,n),!0;case"mouseover":return Tl=Hn(Tl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return jn.set(i,Hn(jn.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,Ln.set(i,Hn(Ln.get(i)||null,t,e,l,a,n)),!0}return!1}function Ud(t){var e=Fl(t.target);if(e!==null){var l=d(e);if(l!==null){if(e=l.tag,e===13){if(e=m(l),e!==null){t.blockedOn=e,ag(t.priority,function(){if(l.tag===13){var a=ye();a=pu(a);var n=oa(l,a);n!==null&&ve(n,l,a),xc(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function eu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=_c(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Au=a,l.target.dispatchEvent(a),Au=null}else return e=Pl(l),e!==null&&Dd(e),t.blockedOn=l,!1;e.shift()}return!0}function jd(t,e,l){eu(t)&&l.delete(e)}function fp(){Ec=!1,Ol!==null&&eu(Ol)&&(Ol=null),El!==null&&eu(El)&&(El=null),Tl!==null&&eu(Tl)&&(Tl=null),jn.forEach(jd),Ln.forEach(jd)}function lu(t,e){t.blockedOn===e&&(t.blockedOn=null,Ec||(Ec=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,fp)))}var au=null;function Ld(t){au!==t&&(au=t,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){au===t&&(au=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Oc(a||l)===null)continue;break}var i=Pl(l);i!==null&&(t.splice(e,3),e-=3,Os(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Bn(t){function e(v){return lu(v,t)}Ol!==null&&lu(Ol,t),El!==null&&lu(El,t),Tl!==null&&lu(Tl,t),jn.forEach(e),Ln.forEach(e);for(var l=0;l<Al.length;l++){var a=Al[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Al.length&&(l=Al[0],l.blockedOn===null);)Ud(l),l.blockedOn===null&&Al.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],f=n[ne]||null;if(typeof i=="function")f||Ld(l);else if(f){var h=null;if(i&&i.hasAttribute("formAction")){if(n=i,f=i[ne]||null)h=f.formAction;else if(Oc(n)!==null)continue}else h=f.action;typeof h=="function"?l[a+1]=h:(l.splice(a,3),a-=3),Ld(l)}}}function Tc(t){this._internalRoot=t}nu.prototype.render=Tc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,a=ye();Rd(l,a,t,e,null,null)},nu.prototype.unmount=Tc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Rd(t.current,2,null,t,null,null),qi(),e[Wl]=null}};function nu(t){this._internalRoot=t}nu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Fc();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Al.length&&e!==0&&e<Al[l].priority;l++);Al.splice(l,0,t),l===0&&Ud(t)}};var Hd=u.version;if(Hd!=="19.1.0")throw Error(s(527,Hd,"19.1.0"));H.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=y(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var dp={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var iu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!iu.isDisabled&&iu.supportsFiber)try{Ga=iu.inject(dp),oe=iu}catch{}}return Yn.createRoot=function(t,e){if(!o(t))throw Error(s(299));var l=!1,a="",n=Po,i=Io,f=tf,h=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=Nd(t,1,!1,null,null,l,a,n,i,f,h,null),t[Wl]=e.current,sc(t),new Tc(e)},Yn.hydrateRoot=function(t,e,l){if(!o(t))throw Error(s(299));var a=!1,n="",i=Po,f=Io,h=tf,v=null,E=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(h=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(v=l.unstable_transitionCallbacks),l.formState!==void 0&&(E=l.formState)),e=Nd(t,1,!0,e,l??null,a,n,i,f,h,v,E),e.context=zd(null),l=e.current,a=ye(),a=pu(a),n=rl(a),n.callback=null,ol(l,n,a),l=a,e.current.lanes=l,ka(e,l),Le(e),t[Wl]=e.current,sc(t),new nu(e)},Yn.version="19.1.0",Yn}var Kd;function _p(){if(Kd)return zc.exports;Kd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),zc.exports=Sp(),zc.exports}var Op=_p();const tt=r=>typeof r=="string",Gn=()=>{let r,u;const c=new Promise((s,o)=>{r=s,u=o});return c.resolve=r,c.reject=u,c},Jd=r=>r==null?"":""+r,Ep=(r,u,c)=>{r.forEach(s=>{u[s]&&(c[s]=u[s])})},Tp=/###/g,$d=r=>r&&r.indexOf("###")>-1?r.replace(Tp,"."):r,Wd=r=>!r||tt(r),kn=(r,u,c)=>{const s=tt(u)?u.split("."):u;let o=0;for(;o<s.length-1;){if(Wd(r))return{};const d=$d(s[o]);!r[d]&&c&&(r[d]=new c),Object.prototype.hasOwnProperty.call(r,d)?r=r[d]:r={},++o}return Wd(r)?{}:{obj:r,k:$d(s[o])}},Fd=(r,u,c)=>{const{obj:s,k:o}=kn(r,u,Object);if(s!==void 0||u.length===1){s[o]=c;return}let d=u[u.length-1],m=u.slice(0,u.length-1),p=kn(r,m,Object);for(;p.obj===void 0&&m.length;)d=`${m[m.length-1]}.${d}`,m=m.slice(0,m.length-1),p=kn(r,m,Object),p!=null&&p.obj&&typeof p.obj[`${p.k}.${d}`]<"u"&&(p.obj=void 0);p.obj[`${p.k}.${d}`]=c},Ap=(r,u,c,s)=>{const{obj:o,k:d}=kn(r,u,Object);o[d]=o[d]||[],o[d].push(c)},ru=(r,u)=>{const{obj:c,k:s}=kn(r,u);if(c&&Object.prototype.hasOwnProperty.call(c,s))return c[s]},Np=(r,u,c)=>{const s=ru(r,c);return s!==void 0?s:ru(u,c)},_h=(r,u,c)=>{for(const s in u)s!=="__proto__"&&s!=="constructor"&&(s in r?tt(r[s])||r[s]instanceof String||tt(u[s])||u[s]instanceof String?c&&(r[s]=u[s]):_h(r[s],u[s],c):r[s]=u[s]);return r},ja=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var zp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Rp=r=>tt(r)?r.replace(/[&<>"'\/]/g,u=>zp[u]):r;class Mp{constructor(u){this.capacity=u,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(u){const c=this.regExpMap.get(u);if(c!==void 0)return c;const s=new RegExp(u);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(u,s),this.regExpQueue.push(u),s}}const Dp=[" ",",","?","!",";"],wp=new Mp(20),Cp=(r,u,c)=>{u=u||"",c=c||"";const s=Dp.filter(m=>u.indexOf(m)<0&&c.indexOf(m)<0);if(s.length===0)return!0;const o=wp.getRegExp(`(${s.map(m=>m==="?"?"\\?":m).join("|")})`);let d=!o.test(r);if(!d){const m=r.indexOf(c);m>0&&!o.test(r.substring(0,m))&&(d=!0)}return d},jc=(r,u,c=".")=>{if(!r)return;if(r[u])return Object.prototype.hasOwnProperty.call(r,u)?r[u]:void 0;const s=u.split(c);let o=r;for(let d=0;d<s.length;){if(!o||typeof o!="object")return;let m,p="";for(let y=d;y<s.length;++y)if(y!==d&&(p+=c),p+=s[y],m=o[p],m!==void 0){if(["string","number","boolean"].indexOf(typeof m)>-1&&y<s.length-1)continue;d+=y-d+1;break}o=m}return o},Qn=r=>r==null?void 0:r.replace("_","-"),Up={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,u){var c,s;(s=(c=console==null?void 0:console[r])==null?void 0:c.apply)==null||s.call(c,console,u)}};class ou{constructor(u,c={}){this.init(u,c)}init(u,c={}){this.prefix=c.prefix||"i18next:",this.logger=u||Up,this.options=c,this.debug=c.debug}log(...u){return this.forward(u,"log","",!0)}warn(...u){return this.forward(u,"warn","",!0)}error(...u){return this.forward(u,"error","")}deprecate(...u){return this.forward(u,"warn","WARNING DEPRECATED: ",!0)}forward(u,c,s,o){return o&&!this.debug?null:(tt(u[0])&&(u[0]=`${s}${this.prefix} ${u[0]}`),this.logger[c](u))}create(u){return new ou(this.logger,{prefix:`${this.prefix}:${u}:`,...this.options})}clone(u){return u=u||this.options,u.prefix=u.prefix||this.prefix,new ou(this.logger,u)}}var He=new ou;class du{constructor(){this.observers={}}on(u,c){return u.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const o=this.observers[s].get(c)||0;this.observers[s].set(c,o+1)}),this}off(u,c){if(this.observers[u]){if(!c){delete this.observers[u];return}this.observers[u].delete(c)}}emit(u,...c){this.observers[u]&&Array.from(this.observers[u].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o(...c)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o.apply(o,[u,...c])})}}class Pd extends du{constructor(u,c={ns:["translation"],defaultNS:"translation"}){super(),this.data=u||{},this.options=c,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(u){this.options.ns.indexOf(u)<0&&this.options.ns.push(u)}removeNamespaces(u){const c=this.options.ns.indexOf(u);c>-1&&this.options.ns.splice(c,1)}getResource(u,c,s,o={}){var g,_;const d=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,m=o.ignoreJSONStructure!==void 0?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let p;u.indexOf(".")>-1?p=u.split("."):(p=[u,c],s&&(Array.isArray(s)?p.push(...s):tt(s)&&d?p.push(...s.split(d)):p.push(s)));const y=ru(this.data,p);return!y&&!c&&!s&&u.indexOf(".")>-1&&(u=p[0],c=p[1],s=p.slice(2).join(".")),y||!m||!tt(s)?y:jc((_=(g=this.data)==null?void 0:g[u])==null?void 0:_[c],s,d)}addResource(u,c,s,o,d={silent:!1}){const m=d.keySeparator!==void 0?d.keySeparator:this.options.keySeparator;let p=[u,c];s&&(p=p.concat(m?s.split(m):s)),u.indexOf(".")>-1&&(p=u.split("."),o=c,c=p[1]),this.addNamespaces(c),Fd(this.data,p,o),d.silent||this.emit("added",u,c,s,o)}addResources(u,c,s,o={silent:!1}){for(const d in s)(tt(s[d])||Array.isArray(s[d]))&&this.addResource(u,c,d,s[d],{silent:!0});o.silent||this.emit("added",u,c,s)}addResourceBundle(u,c,s,o,d,m={silent:!1,skipCopy:!1}){let p=[u,c];u.indexOf(".")>-1&&(p=u.split("."),o=s,s=c,c=p[1]),this.addNamespaces(c);let y=ru(this.data,p)||{};m.skipCopy||(s=JSON.parse(JSON.stringify(s))),o?_h(y,s,d):y={...y,...s},Fd(this.data,p,y),m.silent||this.emit("added",u,c,s)}removeResourceBundle(u,c){this.hasResourceBundle(u,c)&&delete this.data[u][c],this.removeNamespaces(c),this.emit("removed",u,c)}hasResourceBundle(u,c){return this.getResource(u,c)!==void 0}getResourceBundle(u,c){return c||(c=this.options.defaultNS),this.getResource(u,c)}getDataByLanguage(u){return this.data[u]}hasLanguageSomeTranslations(u){const c=this.getDataByLanguage(u);return!!(c&&Object.keys(c)||[]).find(o=>c[o]&&Object.keys(c[o]).length>0)}toJSON(){return this.data}}var Oh={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,u,c,s,o){return r.forEach(d=>{var m;u=((m=this.processors[d])==null?void 0:m.process(u,c,s,o))??u}),u}};const Id={},th=r=>!tt(r)&&typeof r!="boolean"&&typeof r!="number";class fu extends du{constructor(u,c={}){super(),Ep(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],u,this),this.options=c,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=He.create("translator")}changeLanguage(u){u&&(this.language=u)}exists(u,c={interpolation:{}}){const s={...c};if(u==null)return!1;const o=this.resolve(u,s);return(o==null?void 0:o.res)!==void 0}extractFromKey(u,c){let s=c.nsSeparator!==void 0?c.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const o=c.keySeparator!==void 0?c.keySeparator:this.options.keySeparator;let d=c.ns||this.options.defaultNS||[];const m=s&&u.indexOf(s)>-1,p=!this.options.userDefinedKeySeparator&&!c.keySeparator&&!this.options.userDefinedNsSeparator&&!c.nsSeparator&&!Cp(u,s,o);if(m&&!p){const y=u.match(this.interpolator.nestingRegexp);if(y&&y.length>0)return{key:u,namespaces:tt(d)?[d]:d};const g=u.split(s);(s!==o||s===o&&this.options.ns.indexOf(g[0])>-1)&&(d=g.shift()),u=g.join(o)}return{key:u,namespaces:tt(d)?[d]:d}}translate(u,c,s){let o=typeof c=="object"?{...c}:c;if(typeof o!="object"&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(o={...o}),o||(o={}),u==null)return"";Array.isArray(u)||(u=[String(u)]);const d=o.returnDetails!==void 0?o.returnDetails:this.options.returnDetails,m=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,{key:p,namespaces:y}=this.extractFromKey(u[u.length-1],o),g=y[y.length-1];let _=o.nsSeparator!==void 0?o.nsSeparator:this.options.nsSeparator;_===void 0&&(_=":");const z=o.lng||this.language,B=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((z==null?void 0:z.toLowerCase())==="cimode")return B?d?{res:`${g}${_}${p}`,usedKey:p,exactUsedKey:p,usedLng:z,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:`${g}${_}${p}`:d?{res:p,usedKey:p,exactUsedKey:p,usedLng:z,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:p;const q=this.resolve(u,o);let j=q==null?void 0:q.res;const Z=(q==null?void 0:q.usedKey)||p,F=(q==null?void 0:q.exactUsedKey)||p,xt=["[object Number]","[object Function]","[object RegExp]"],ft=o.joinArrays!==void 0?o.joinArrays:this.options.joinArrays,st=!this.i18nFormat||this.i18nFormat.handleAsObject,ot=o.count!==void 0&&!tt(o.count),J=fu.hasDefaultValue(o),St=ot?this.pluralResolver.getSuffix(z,o.count,o):"",mt=o.ordinal&&ot?this.pluralResolver.getSuffix(z,o.count,{ordinal:!1}):"",G=ot&&!o.ordinal&&o.count===0,et=G&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${St}`]||o[`defaultValue${mt}`]||o.defaultValue;let dt=j;st&&!j&&J&&(dt=et);const Dt=th(dt),Ct=Object.prototype.toString.apply(dt);if(st&&dt&&Dt&&xt.indexOf(Ct)<0&&!(tt(ft)&&Array.isArray(dt))){if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const Bt=this.options.returnedObjectHandler?this.options.returnedObjectHandler(Z,dt,{...o,ns:y}):`key '${p} (${this.language})' returned an object instead of string.`;return d?(q.res=Bt,q.usedParams=this.getUsedParamsDetails(o),q):Bt}if(m){const Bt=Array.isArray(dt),Ut=Bt?[]:{},Rt=Bt?F:Z;for(const N in dt)if(Object.prototype.hasOwnProperty.call(dt,N)){const H=`${Rt}${m}${N}`;J&&!j?Ut[N]=this.translate(H,{...o,defaultValue:th(et)?et[N]:void 0,joinArrays:!1,ns:y}):Ut[N]=this.translate(H,{...o,joinArrays:!1,ns:y}),Ut[N]===H&&(Ut[N]=dt[N])}j=Ut}}else if(st&&tt(ft)&&Array.isArray(j))j=j.join(ft),j&&(j=this.extendTranslation(j,u,o,s));else{let Bt=!1,Ut=!1;!this.isValidLookup(j)&&J&&(Bt=!0,j=et),this.isValidLookup(j)||(Ut=!0,j=p);const N=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&Ut?void 0:j,H=J&&et!==j&&this.options.updateMissing;if(Ut||Bt||H){if(this.logger.log(H?"updateKey":"missingKey",z,g,p,H?et:j),m){const M=this.resolve(p,{...o,keySeparator:!1});M&&M.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let U=[];const ht=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if(this.options.saveMissingTo==="fallback"&&ht&&ht[0])for(let M=0;M<ht.length;M++)U.push(ht[M]);else this.options.saveMissingTo==="all"?U=this.languageUtils.toResolveHierarchy(o.lng||this.language):U.push(o.lng||this.language);const b=(M,Y,L)=>{var at;const V=J&&L!==j?L:N;this.options.missingKeyHandler?this.options.missingKeyHandler(M,g,Y,V,H,o):(at=this.backendConnector)!=null&&at.saveMissing&&this.backendConnector.saveMissing(M,g,Y,V,H,o),this.emit("missingKey",M,g,Y,j)};this.options.saveMissing&&(this.options.saveMissingPlurals&&ot?U.forEach(M=>{const Y=this.pluralResolver.getSuffixes(M,o);G&&o[`defaultValue${this.options.pluralSeparator}zero`]&&Y.indexOf(`${this.options.pluralSeparator}zero`)<0&&Y.push(`${this.options.pluralSeparator}zero`),Y.forEach(L=>{b([M],p+L,o[`defaultValue${L}`]||et)})}):b(U,p,et))}j=this.extendTranslation(j,u,o,q,s),Ut&&j===p&&this.options.appendNamespaceToMissingKey&&(j=`${g}${_}${p}`),(Ut||Bt)&&this.options.parseMissingKeyHandler&&(j=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${g}${_}${p}`:p,Bt?j:void 0,o))}return d?(q.res=j,q.usedParams=this.getUsedParamsDetails(o),q):j}extendTranslation(u,c,s,o,d){var y,g;if((y=this.i18nFormat)!=null&&y.parse)u=this.i18nFormat.parse(u,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const _=tt(u)&&(((g=s==null?void 0:s.interpolation)==null?void 0:g.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let z;if(_){const q=u.match(this.interpolator.nestingRegexp);z=q&&q.length}let B=s.replace&&!tt(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(B={...this.options.interpolation.defaultVariables,...B}),u=this.interpolator.interpolate(u,B,s.lng||this.language||o.usedLng,s),_){const q=u.match(this.interpolator.nestingRegexp),j=q&&q.length;z<j&&(s.nest=!1)}!s.lng&&o&&o.res&&(s.lng=this.language||o.usedLng),s.nest!==!1&&(u=this.interpolator.nest(u,(...q)=>(d==null?void 0:d[0])===q[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${q[0]} in key: ${c[0]}`),null):this.translate(...q,c),s)),s.interpolation&&this.interpolator.reset()}const m=s.postProcess||this.options.postProcess,p=tt(m)?[m]:m;return u!=null&&(p!=null&&p.length)&&s.applyPostProcessor!==!1&&(u=Oh.handle(p,u,c,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),u}resolve(u,c={}){let s,o,d,m,p;return tt(u)&&(u=[u]),u.forEach(y=>{if(this.isValidLookup(s))return;const g=this.extractFromKey(y,c),_=g.key;o=_;let z=g.namespaces;this.options.fallbackNS&&(z=z.concat(this.options.fallbackNS));const B=c.count!==void 0&&!tt(c.count),q=B&&!c.ordinal&&c.count===0,j=c.context!==void 0&&(tt(c.context)||typeof c.context=="number")&&c.context!=="",Z=c.lngs?c.lngs:this.languageUtils.toResolveHierarchy(c.lng||this.language,c.fallbackLng);z.forEach(F=>{var xt,ft;this.isValidLookup(s)||(p=F,!Id[`${Z[0]}-${F}`]&&((xt=this.utils)!=null&&xt.hasLoadedNamespace)&&!((ft=this.utils)!=null&&ft.hasLoadedNamespace(p))&&(Id[`${Z[0]}-${F}`]=!0,this.logger.warn(`key "${o}" for languages "${Z.join(", ")}" won't get resolved as namespace "${p}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),Z.forEach(st=>{var St;if(this.isValidLookup(s))return;m=st;const ot=[_];if((St=this.i18nFormat)!=null&&St.addLookupKeys)this.i18nFormat.addLookupKeys(ot,_,st,F,c);else{let mt;B&&(mt=this.pluralResolver.getSuffix(st,c.count,c));const G=`${this.options.pluralSeparator}zero`,et=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(B&&(ot.push(_+mt),c.ordinal&&mt.indexOf(et)===0&&ot.push(_+mt.replace(et,this.options.pluralSeparator)),q&&ot.push(_+G)),j){const dt=`${_}${this.options.contextSeparator}${c.context}`;ot.push(dt),B&&(ot.push(dt+mt),c.ordinal&&mt.indexOf(et)===0&&ot.push(dt+mt.replace(et,this.options.pluralSeparator)),q&&ot.push(dt+G))}}let J;for(;J=ot.pop();)this.isValidLookup(s)||(d=J,s=this.getResource(st,F,J,c))}))})}),{res:s,usedKey:o,exactUsedKey:d,usedLng:m,usedNS:p}}isValidLookup(u){return u!==void 0&&!(!this.options.returnNull&&u===null)&&!(!this.options.returnEmptyString&&u==="")}getResource(u,c,s,o={}){var d;return(d=this.i18nFormat)!=null&&d.getResource?this.i18nFormat.getResource(u,c,s,o):this.resourceStore.getResource(u,c,s,o)}getUsedParamsDetails(u={}){const c=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=u.replace&&!tt(u.replace);let o=s?u.replace:u;if(s&&typeof u.count<"u"&&(o.count=u.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!s){o={...o};for(const d of c)delete o[d]}return o}static hasDefaultValue(u){const c="defaultValue";for(const s in u)if(Object.prototype.hasOwnProperty.call(u,s)&&c===s.substring(0,c.length)&&u[s]!==void 0)return!0;return!1}}class eh{constructor(u){this.options=u,this.supportedLngs=this.options.supportedLngs||!1,this.logger=He.create("languageUtils")}getScriptPartFromCode(u){if(u=Qn(u),!u||u.indexOf("-")<0)return null;const c=u.split("-");return c.length===2||(c.pop(),c[c.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(c.join("-"))}getLanguagePartFromCode(u){if(u=Qn(u),!u||u.indexOf("-")<0)return u;const c=u.split("-");return this.formatLanguageCode(c[0])}formatLanguageCode(u){if(tt(u)&&u.indexOf("-")>-1){let c;try{c=Intl.getCanonicalLocales(u)[0]}catch{}return c&&this.options.lowerCaseLng&&(c=c.toLowerCase()),c||(this.options.lowerCaseLng?u.toLowerCase():u)}return this.options.cleanCode||this.options.lowerCaseLng?u.toLowerCase():u}isSupportedCode(u){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(u=this.getLanguagePartFromCode(u)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(u)>-1}getBestMatchFromCodes(u){if(!u)return null;let c;return u.forEach(s=>{if(c)return;const o=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(o))&&(c=o)}),!c&&this.options.supportedLngs&&u.forEach(s=>{if(c)return;const o=this.getScriptPartFromCode(s);if(this.isSupportedCode(o))return c=o;const d=this.getLanguagePartFromCode(s);if(this.isSupportedCode(d))return c=d;c=this.options.supportedLngs.find(m=>{if(m===d)return m;if(!(m.indexOf("-")<0&&d.indexOf("-")<0)&&(m.indexOf("-")>0&&d.indexOf("-")<0&&m.substring(0,m.indexOf("-"))===d||m.indexOf(d)===0&&d.length>1))return m})}),c||(c=this.getFallbackCodes(this.options.fallbackLng)[0]),c}getFallbackCodes(u,c){if(!u)return[];if(typeof u=="function"&&(u=u(c)),tt(u)&&(u=[u]),Array.isArray(u))return u;if(!c)return u.default||[];let s=u[c];return s||(s=u[this.getScriptPartFromCode(c)]),s||(s=u[this.formatLanguageCode(c)]),s||(s=u[this.getLanguagePartFromCode(c)]),s||(s=u.default),s||[]}toResolveHierarchy(u,c){const s=this.getFallbackCodes((c===!1?[]:c)||this.options.fallbackLng||[],u),o=[],d=m=>{m&&(this.isSupportedCode(m)?o.push(m):this.logger.warn(`rejecting language code not found in supportedLngs: ${m}`))};return tt(u)&&(u.indexOf("-")>-1||u.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&d(this.formatLanguageCode(u)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&d(this.getScriptPartFromCode(u)),this.options.load!=="currentOnly"&&d(this.getLanguagePartFromCode(u))):tt(u)&&d(this.formatLanguageCode(u)),s.forEach(m=>{o.indexOf(m)<0&&d(this.formatLanguageCode(m))}),o}}const lh={zero:0,one:1,two:2,few:3,many:4,other:5},ah={select:r=>r===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class jp{constructor(u,c={}){this.languageUtils=u,this.options=c,this.logger=He.create("pluralResolver"),this.pluralRulesCache={}}addRule(u,c){this.rules[u]=c}clearCache(){this.pluralRulesCache={}}getRule(u,c={}){const s=Qn(u==="dev"?"en":u),o=c.ordinal?"ordinal":"cardinal",d=JSON.stringify({cleanedCode:s,type:o});if(d in this.pluralRulesCache)return this.pluralRulesCache[d];let m;try{m=new Intl.PluralRules(s,{type:o})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),ah;if(!u.match(/-|_/))return ah;const y=this.languageUtils.getLanguagePartFromCode(u);m=this.getRule(y,c)}return this.pluralRulesCache[d]=m,m}needsPlural(u,c={}){let s=this.getRule(u,c);return s||(s=this.getRule("dev",c)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(u,c,s={}){return this.getSuffixes(u,s).map(o=>`${c}${o}`)}getSuffixes(u,c={}){let s=this.getRule(u,c);return s||(s=this.getRule("dev",c)),s?s.resolvedOptions().pluralCategories.sort((o,d)=>lh[o]-lh[d]).map(o=>`${this.options.prepend}${c.ordinal?`ordinal${this.options.prepend}`:""}${o}`):[]}getSuffix(u,c,s={}){const o=this.getRule(u,s);return o?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${o.select(c)}`:(this.logger.warn(`no plural rule found for: ${u}`),this.getSuffix("dev",c,s))}}const nh=(r,u,c,s=".",o=!0)=>{let d=Np(r,u,c);return!d&&o&&tt(c)&&(d=jc(r,c,s),d===void 0&&(d=jc(u,c,s))),d},wc=r=>r.replace(/\$/g,"$$$$");class Lp{constructor(u={}){var c;this.logger=He.create("interpolator"),this.options=u,this.format=((c=u==null?void 0:u.interpolation)==null?void 0:c.format)||(s=>s),this.init(u)}init(u={}){u.interpolation||(u.interpolation={escapeValue:!0});const{escape:c,escapeValue:s,useRawValueToEscape:o,prefix:d,prefixEscaped:m,suffix:p,suffixEscaped:y,formatSeparator:g,unescapeSuffix:_,unescapePrefix:z,nestingPrefix:B,nestingPrefixEscaped:q,nestingSuffix:j,nestingSuffixEscaped:Z,nestingOptionsSeparator:F,maxReplaces:xt,alwaysFormat:ft}=u.interpolation;this.escape=c!==void 0?c:Rp,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=o!==void 0?o:!1,this.prefix=d?ja(d):m||"{{",this.suffix=p?ja(p):y||"}}",this.formatSeparator=g||",",this.unescapePrefix=_?"":z||"-",this.unescapeSuffix=this.unescapePrefix?"":_||"",this.nestingPrefix=B?ja(B):q||ja("$t("),this.nestingSuffix=j?ja(j):Z||ja(")"),this.nestingOptionsSeparator=F||",",this.maxReplaces=xt||1e3,this.alwaysFormat=ft!==void 0?ft:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const u=(c,s)=>(c==null?void 0:c.source)===s?(c.lastIndex=0,c):new RegExp(s,"g");this.regexp=u(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=u(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=u(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(u,c,s,o){var q;let d,m,p;const y=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},g=j=>{if(j.indexOf(this.formatSeparator)<0){const ft=nh(c,y,j,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(ft,void 0,s,{...o,...c,interpolationkey:j}):ft}const Z=j.split(this.formatSeparator),F=Z.shift().trim(),xt=Z.join(this.formatSeparator).trim();return this.format(nh(c,y,F,this.options.keySeparator,this.options.ignoreJSONStructure),xt,s,{...o,...c,interpolationkey:F})};this.resetRegExp();const _=(o==null?void 0:o.missingInterpolationHandler)||this.options.missingInterpolationHandler,z=((q=o==null?void 0:o.interpolation)==null?void 0:q.skipOnVariables)!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:j=>wc(j)},{regex:this.regexp,safeValue:j=>this.escapeValue?wc(this.escape(j)):wc(j)}].forEach(j=>{for(p=0;d=j.regex.exec(u);){const Z=d[1].trim();if(m=g(Z),m===void 0)if(typeof _=="function"){const xt=_(u,d,o);m=tt(xt)?xt:""}else if(o&&Object.prototype.hasOwnProperty.call(o,Z))m="";else if(z){m=d[0];continue}else this.logger.warn(`missed to pass in variable ${Z} for interpolating ${u}`),m="";else!tt(m)&&!this.useRawValueToEscape&&(m=Jd(m));const F=j.safeValue(m);if(u=u.replace(d[0],F),z?(j.regex.lastIndex+=m.length,j.regex.lastIndex-=d[0].length):j.regex.lastIndex=0,p++,p>=this.maxReplaces)break}}),u}nest(u,c,s={}){let o,d,m;const p=(y,g)=>{const _=this.nestingOptionsSeparator;if(y.indexOf(_)<0)return y;const z=y.split(new RegExp(`${_}[ ]*{`));let B=`{${z[1]}`;y=z[0],B=this.interpolate(B,m);const q=B.match(/'/g),j=B.match(/"/g);(((q==null?void 0:q.length)??0)%2===0&&!j||j.length%2!==0)&&(B=B.replace(/'/g,'"'));try{m=JSON.parse(B),g&&(m={...g,...m})}catch(Z){return this.logger.warn(`failed parsing options string in nesting for key ${y}`,Z),`${y}${_}${B}`}return m.defaultValue&&m.defaultValue.indexOf(this.prefix)>-1&&delete m.defaultValue,y};for(;o=this.nestingRegexp.exec(u);){let y=[];m={...s},m=m.replace&&!tt(m.replace)?m.replace:m,m.applyPostProcessor=!1,delete m.defaultValue;const g=/{.*}/.test(o[1])?o[1].lastIndexOf("}")+1:o[1].indexOf(this.formatSeparator);if(g!==-1&&(y=o[1].slice(g).split(this.formatSeparator).map(_=>_.trim()).filter(Boolean),o[1]=o[1].slice(0,g)),d=c(p.call(this,o[1].trim(),m),m),d&&o[0]===u&&!tt(d))return d;tt(d)||(d=Jd(d)),d||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${u}`),d=""),y.length&&(d=y.reduce((_,z)=>this.format(_,z,s.lng,{...s,interpolationkey:o[1].trim()}),d.trim())),u=u.replace(o[0],d),this.regexp.lastIndex=0}return u}}const Hp=r=>{let u=r.toLowerCase().trim();const c={};if(r.indexOf("(")>-1){const s=r.split("(");u=s[0].toLowerCase().trim();const o=s[1].substring(0,s[1].length-1);u==="currency"&&o.indexOf(":")<0?c.currency||(c.currency=o.trim()):u==="relativetime"&&o.indexOf(":")<0?c.range||(c.range=o.trim()):o.split(";").forEach(m=>{if(m){const[p,...y]=m.split(":"),g=y.join(":").trim().replace(/^'+|'+$/g,""),_=p.trim();c[_]||(c[_]=g),g==="false"&&(c[_]=!1),g==="true"&&(c[_]=!0),isNaN(g)||(c[_]=parseInt(g,10))}})}return{formatName:u,formatOptions:c}},ih=r=>{const u={};return(c,s,o)=>{let d=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(d={...d,[o.interpolationkey]:void 0});const m=s+JSON.stringify(d);let p=u[m];return p||(p=r(Qn(s),o),u[m]=p),p(c)}},Bp=r=>(u,c,s)=>r(Qn(c),s)(u);class qp{constructor(u={}){this.logger=He.create("formatter"),this.options=u,this.init(u)}init(u,c={interpolation:{}}){this.formatSeparator=c.interpolation.formatSeparator||",";const s=c.cacheInBuiltFormats?ih:Bp;this.formats={number:s((o,d)=>{const m=new Intl.NumberFormat(o,{...d});return p=>m.format(p)}),currency:s((o,d)=>{const m=new Intl.NumberFormat(o,{...d,style:"currency"});return p=>m.format(p)}),datetime:s((o,d)=>{const m=new Intl.DateTimeFormat(o,{...d});return p=>m.format(p)}),relativetime:s((o,d)=>{const m=new Intl.RelativeTimeFormat(o,{...d});return p=>m.format(p,d.range||"day")}),list:s((o,d)=>{const m=new Intl.ListFormat(o,{...d});return p=>m.format(p)})}}add(u,c){this.formats[u.toLowerCase().trim()]=c}addCached(u,c){this.formats[u.toLowerCase().trim()]=ih(c)}format(u,c,s,o={}){const d=c.split(this.formatSeparator);if(d.length>1&&d[0].indexOf("(")>1&&d[0].indexOf(")")<0&&d.find(p=>p.indexOf(")")>-1)){const p=d.findIndex(y=>y.indexOf(")")>-1);d[0]=[d[0],...d.splice(1,p)].join(this.formatSeparator)}return d.reduce((p,y)=>{var z;const{formatName:g,formatOptions:_}=Hp(y);if(this.formats[g]){let B=p;try{const q=((z=o==null?void 0:o.formatParams)==null?void 0:z[o.interpolationkey])||{},j=q.locale||q.lng||o.locale||o.lng||s;B=this.formats[g](p,j,{..._,...o,...q})}catch(q){this.logger.warn(q)}return B}else this.logger.warn(`there was no format function for ${g}`);return p},u)}}const Yp=(r,u)=>{r.pending[u]!==void 0&&(delete r.pending[u],r.pendingCount--)};class Gp extends du{constructor(u,c,s,o={}){var d,m;super(),this.backend=u,this.store=c,this.services=s,this.languageUtils=s.languageUtils,this.options=o,this.logger=He.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],(m=(d=this.backend)==null?void 0:d.init)==null||m.call(d,s,o.backend,o)}queueLoad(u,c,s,o){const d={},m={},p={},y={};return u.forEach(g=>{let _=!0;c.forEach(z=>{const B=`${g}|${z}`;!s.reload&&this.store.hasResourceBundle(g,z)?this.state[B]=2:this.state[B]<0||(this.state[B]===1?m[B]===void 0&&(m[B]=!0):(this.state[B]=1,_=!1,m[B]===void 0&&(m[B]=!0),d[B]===void 0&&(d[B]=!0),y[z]===void 0&&(y[z]=!0)))}),_||(p[g]=!0)}),(Object.keys(d).length||Object.keys(m).length)&&this.queue.push({pending:m,pendingCount:Object.keys(m).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(d),pending:Object.keys(m),toLoadLanguages:Object.keys(p),toLoadNamespaces:Object.keys(y)}}loaded(u,c,s){const o=u.split("|"),d=o[0],m=o[1];c&&this.emit("failedLoading",d,m,c),!c&&s&&this.store.addResourceBundle(d,m,s,void 0,void 0,{skipCopy:!0}),this.state[u]=c?-1:2,c&&s&&(this.state[u]=0);const p={};this.queue.forEach(y=>{Ap(y.loaded,[d],m),Yp(y,u),c&&y.errors.push(c),y.pendingCount===0&&!y.done&&(Object.keys(y.loaded).forEach(g=>{p[g]||(p[g]={});const _=y.loaded[g];_.length&&_.forEach(z=>{p[g][z]===void 0&&(p[g][z]=!0)})}),y.done=!0,y.errors.length?y.callback(y.errors):y.callback())}),this.emit("loaded",p),this.queue=this.queue.filter(y=>!y.done)}read(u,c,s,o=0,d=this.retryTimeout,m){if(!u.length)return m(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:u,ns:c,fcName:s,tried:o,wait:d,callback:m});return}this.readingCalls++;const p=(g,_)=>{if(this.readingCalls--,this.waitingReads.length>0){const z=this.waitingReads.shift();this.read(z.lng,z.ns,z.fcName,z.tried,z.wait,z.callback)}if(g&&_&&o<this.maxRetries){setTimeout(()=>{this.read.call(this,u,c,s,o+1,d*2,m)},d);return}m(g,_)},y=this.backend[s].bind(this.backend);if(y.length===2){try{const g=y(u,c);g&&typeof g.then=="function"?g.then(_=>p(null,_)).catch(p):p(null,g)}catch(g){p(g)}return}return y(u,c,p)}prepareLoading(u,c,s={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();tt(u)&&(u=this.languageUtils.toResolveHierarchy(u)),tt(c)&&(c=[c]);const d=this.queueLoad(u,c,s,o);if(!d.toLoad.length)return d.pending.length||o(),null;d.toLoad.forEach(m=>{this.loadOne(m)})}load(u,c,s){this.prepareLoading(u,c,{},s)}reload(u,c,s){this.prepareLoading(u,c,{reload:!0},s)}loadOne(u,c=""){const s=u.split("|"),o=s[0],d=s[1];this.read(o,d,"read",void 0,void 0,(m,p)=>{m&&this.logger.warn(`${c}loading namespace ${d} for language ${o} failed`,m),!m&&p&&this.logger.log(`${c}loaded namespace ${d} for language ${o}`,p),this.loaded(u,m,p)})}saveMissing(u,c,s,o,d,m={},p=()=>{}){var y,g,_,z,B;if((g=(y=this.services)==null?void 0:y.utils)!=null&&g.hasLoadedNamespace&&!((z=(_=this.services)==null?void 0:_.utils)!=null&&z.hasLoadedNamespace(c))){this.logger.warn(`did not save key "${s}" as the namespace "${c}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((B=this.backend)!=null&&B.create){const q={...m,isUpdate:d},j=this.backend.create.bind(this.backend);if(j.length<6)try{let Z;j.length===5?Z=j(u,c,s,o,q):Z=j(u,c,s,o),Z&&typeof Z.then=="function"?Z.then(F=>p(null,F)).catch(p):p(null,Z)}catch(Z){p(Z)}else j(u,c,s,o,p,q)}!u||!u[0]||this.store.addResource(u[0],c,s,o)}}}const uh=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:r=>{let u={};if(typeof r[1]=="object"&&(u=r[1]),tt(r[1])&&(u.defaultValue=r[1]),tt(r[2])&&(u.tDescription=r[2]),typeof r[2]=="object"||typeof r[3]=="object"){const c=r[3]||r[2];Object.keys(c).forEach(s=>{u[s]=c[s]})}return u},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),sh=r=>{var u,c;return tt(r.ns)&&(r.ns=[r.ns]),tt(r.fallbackLng)&&(r.fallbackLng=[r.fallbackLng]),tt(r.fallbackNS)&&(r.fallbackNS=[r.fallbackNS]),((c=(u=r.supportedLngs)==null?void 0:u.indexOf)==null?void 0:c.call(u,"cimode"))<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),typeof r.initImmediate=="boolean"&&(r.initAsync=r.initImmediate),r},uu=()=>{},Vp=r=>{Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(c=>{typeof r[c]=="function"&&(r[c]=r[c].bind(r))})};class Xn extends du{constructor(u={},c){if(super(),this.options=sh(u),this.services={},this.logger=He,this.modules={external:[]},Vp(this),c&&!this.isInitialized&&!u.isClone){if(!this.options.initAsync)return this.init(u,c),this;setTimeout(()=>{this.init(u,c)},0)}}init(u={},c){this.isInitializing=!0,typeof u=="function"&&(c=u,u={}),u.defaultNS==null&&u.ns&&(tt(u.ns)?u.defaultNS=u.ns:u.ns.indexOf("translation")<0&&(u.defaultNS=u.ns[0]));const s=uh();this.options={...s,...this.options,...sh(u)},this.options.interpolation={...s.interpolation,...this.options.interpolation},u.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=u.keySeparator),u.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=u.nsSeparator);const o=g=>g?typeof g=="function"?new g:g:null;if(!this.options.isClone){this.modules.logger?He.init(o(this.modules.logger),this.options):He.init(null,this.options);let g;this.modules.formatter?g=this.modules.formatter:g=qp;const _=new eh(this.options);this.store=new Pd(this.options.resources,this.options);const z=this.services;z.logger=He,z.resourceStore=this.store,z.languageUtils=_,z.pluralResolver=new jp(_,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),g&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(z.formatter=o(g),z.formatter.init&&z.formatter.init(z,this.options),this.options.interpolation.format=z.formatter.format.bind(z.formatter)),z.interpolator=new Lp(this.options),z.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},z.backendConnector=new Gp(o(this.modules.backend),z.resourceStore,z,this.options),z.backendConnector.on("*",(q,...j)=>{this.emit(q,...j)}),this.modules.languageDetector&&(z.languageDetector=o(this.modules.languageDetector),z.languageDetector.init&&z.languageDetector.init(z,this.options.detection,this.options)),this.modules.i18nFormat&&(z.i18nFormat=o(this.modules.i18nFormat),z.i18nFormat.init&&z.i18nFormat.init(this)),this.translator=new fu(this.services,this.options),this.translator.on("*",(q,...j)=>{this.emit(q,...j)}),this.modules.external.forEach(q=>{q.init&&q.init(this)})}if(this.format=this.options.interpolation.format,c||(c=uu),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const g=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);g.length>0&&g[0]!=="dev"&&(this.options.lng=g[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(g=>{this[g]=(..._)=>this.store[g](..._)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(g=>{this[g]=(..._)=>(this.store[g](..._),this)});const p=Gn(),y=()=>{const g=(_,z)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),p.resolve(z),c(_,z)};if(this.languages&&!this.isInitialized)return g(null,this.t.bind(this));this.changeLanguage(this.options.lng,g)};return this.options.resources||!this.options.initAsync?y():setTimeout(y,0),p}loadResources(u,c=uu){var d,m;let s=c;const o=tt(u)?u:this.language;if(typeof u=="function"&&(s=u),!this.options.resources||this.options.partialBundledLanguages){if((o==null?void 0:o.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const p=[],y=g=>{if(!g||g==="cimode")return;this.services.languageUtils.toResolveHierarchy(g).forEach(z=>{z!=="cimode"&&p.indexOf(z)<0&&p.push(z)})};o?y(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(_=>y(_)),(m=(d=this.options.preload)==null?void 0:d.forEach)==null||m.call(d,g=>y(g)),this.services.backendConnector.load(p,this.options.ns,g=>{!g&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(g)})}else s(null)}reloadResources(u,c,s){const o=Gn();return typeof u=="function"&&(s=u,u=void 0),typeof c=="function"&&(s=c,c=void 0),u||(u=this.languages),c||(c=this.options.ns),s||(s=uu),this.services.backendConnector.reload(u,c,d=>{o.resolve(),s(d)}),o}use(u){if(!u)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!u.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return u.type==="backend"&&(this.modules.backend=u),(u.type==="logger"||u.log&&u.warn&&u.error)&&(this.modules.logger=u),u.type==="languageDetector"&&(this.modules.languageDetector=u),u.type==="i18nFormat"&&(this.modules.i18nFormat=u),u.type==="postProcessor"&&Oh.addPostProcessor(u),u.type==="formatter"&&(this.modules.formatter=u),u.type==="3rdParty"&&this.modules.external.push(u),this}setResolvedLanguage(u){if(!(!u||!this.languages)&&!(["cimode","dev"].indexOf(u)>-1)){for(let c=0;c<this.languages.length;c++){const s=this.languages[c];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(u)<0&&this.store.hasLanguageSomeTranslations(u)&&(this.resolvedLanguage=u,this.languages.unshift(u))}}changeLanguage(u,c){this.isLanguageChangingTo=u;const s=Gn();this.emit("languageChanging",u);const o=p=>{this.language=p,this.languages=this.services.languageUtils.toResolveHierarchy(p),this.resolvedLanguage=void 0,this.setResolvedLanguage(p)},d=(p,y)=>{y?this.isLanguageChangingTo===u&&(o(y),this.translator.changeLanguage(y),this.isLanguageChangingTo=void 0,this.emit("languageChanged",y),this.logger.log("languageChanged",y)):this.isLanguageChangingTo=void 0,s.resolve((...g)=>this.t(...g)),c&&c(p,(...g)=>this.t(...g))},m=p=>{var _,z;!u&&!p&&this.services.languageDetector&&(p=[]);const y=tt(p)?p:p&&p[0],g=this.store.hasLanguageSomeTranslations(y)?y:this.services.languageUtils.getBestMatchFromCodes(tt(p)?[p]:p);g&&(this.language||o(g),this.translator.language||this.translator.changeLanguage(g),(z=(_=this.services.languageDetector)==null?void 0:_.cacheUserLanguage)==null||z.call(_,g)),this.loadResources(g,B=>{d(B,g)})};return!u&&this.services.languageDetector&&!this.services.languageDetector.async?m(this.services.languageDetector.detect()):!u&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(m):this.services.languageDetector.detect(m):m(u),s}getFixedT(u,c,s){const o=(d,m,...p)=>{let y;typeof m!="object"?y=this.options.overloadTranslationOptionHandler([d,m].concat(p)):y={...m},y.lng=y.lng||o.lng,y.lngs=y.lngs||o.lngs,y.ns=y.ns||o.ns,y.keyPrefix!==""&&(y.keyPrefix=y.keyPrefix||s||o.keyPrefix);const g=this.options.keySeparator||".";let _;return y.keyPrefix&&Array.isArray(d)?_=d.map(z=>`${y.keyPrefix}${g}${z}`):_=y.keyPrefix?`${y.keyPrefix}${g}${d}`:d,this.t(_,y)};return tt(u)?o.lng=u:o.lngs=u,o.ns=c,o.keyPrefix=s,o}t(...u){var c;return(c=this.translator)==null?void 0:c.translate(...u)}exists(...u){var c;return(c=this.translator)==null?void 0:c.exists(...u)}setDefaultNamespace(u){this.options.defaultNS=u}hasLoadedNamespace(u,c={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=c.lng||this.resolvedLanguage||this.languages[0],o=this.options?this.options.fallbackLng:!1,d=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const m=(p,y)=>{const g=this.services.backendConnector.state[`${p}|${y}`];return g===-1||g===0||g===2};if(c.precheck){const p=c.precheck(this,m);if(p!==void 0)return p}return!!(this.hasResourceBundle(s,u)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||m(s,u)&&(!o||m(d,u)))}loadNamespaces(u,c){const s=Gn();return this.options.ns?(tt(u)&&(u=[u]),u.forEach(o=>{this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}),this.loadResources(o=>{s.resolve(),c&&c(o)}),s):(c&&c(),Promise.resolve())}loadLanguages(u,c){const s=Gn();tt(u)&&(u=[u]);const o=this.options.preload||[],d=u.filter(m=>o.indexOf(m)<0&&this.services.languageUtils.isSupportedCode(m));return d.length?(this.options.preload=o.concat(d),this.loadResources(m=>{s.resolve(),c&&c(m)}),s):(c&&c(),Promise.resolve())}dir(u){var o,d;if(u||(u=this.resolvedLanguage||(((o=this.languages)==null?void 0:o.length)>0?this.languages[0]:this.language)),!u)return"rtl";try{const m=new Intl.Locale(u);if(m&&m.getTextInfo){const p=m.getTextInfo();if(p&&p.direction)return p.direction}}catch{}const c=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((d=this.services)==null?void 0:d.languageUtils)||new eh(uh());return u.toLowerCase().indexOf("-latn")>1?"ltr":c.indexOf(s.getLanguagePartFromCode(u))>-1||u.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(u={},c){return new Xn(u,c)}cloneInstance(u={},c=uu){const s=u.forkResourceStore;s&&delete u.forkResourceStore;const o={...this.options,...u,isClone:!0},d=new Xn(o);if((u.debug!==void 0||u.prefix!==void 0)&&(d.logger=d.logger.clone(u)),["store","services","language"].forEach(p=>{d[p]=this[p]}),d.services={...this.services},d.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},s){const p=Object.keys(this.store.data).reduce((y,g)=>(y[g]={...this.store.data[g]},y[g]=Object.keys(y[g]).reduce((_,z)=>(_[z]={...y[g][z]},_),y[g]),y),{});d.store=new Pd(p,o),d.services.resourceStore=d.store}return d.translator=new fu(d.services,o),d.translator.on("*",(p,...y)=>{d.emit(p,...y)}),d.init(o,c),d.translator.options=o,d.translator.backendConnector.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},d}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const It=Xn.createInstance();It.createInstance=Xn.createInstance;It.createInstance;It.dir;It.init;It.loadResources;It.reloadResources;It.use;It.changeLanguage;It.getFixedT;It.t;It.exists;It.setDefaultNamespace;It.hasLoadedNamespace;It.loadNamespaces;It.loadLanguages;const kp=(r,u,c,s)=>{var d,m,p,y;const o=[c,{code:u,...s||{}}];if((m=(d=r==null?void 0:r.services)==null?void 0:d.logger)!=null&&m.forward)return r.services.logger.forward(o,"warn","react-i18next::",!0);$l(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),(y=(p=r==null?void 0:r.services)==null?void 0:p.logger)!=null&&y.warn?r.services.logger.warn(...o):console!=null&&console.warn&&console.warn(...o)},ch={},Lc=(r,u,c,s)=>{$l(c)&&ch[c]||($l(c)&&(ch[c]=new Date),kp(r,u,c,s))},Eh=(r,u)=>()=>{if(r.isInitialized)u();else{const c=()=>{setTimeout(()=>{r.off("initialized",c)},0),u()};r.on("initialized",c)}},Hc=(r,u,c)=>{r.loadNamespaces(u,Eh(r,c))},rh=(r,u,c,s)=>{if($l(c)&&(c=[c]),r.options.preload&&r.options.preload.indexOf(u)>-1)return Hc(r,c,s);c.forEach(o=>{r.options.ns.indexOf(o)<0&&r.options.ns.push(o)}),r.loadLanguages(u,Eh(r,s))},Qp=(r,u,c={})=>!u.languages||!u.languages.length?(Lc(u,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:u.languages}),!0):u.hasLoadedNamespace(r,{lng:c.lng,precheck:(s,o)=>{if(c.bindI18n&&c.bindI18n.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!o(s.isLanguageChangingTo,r))return!1}}),$l=r=>typeof r=="string",Xp=r=>typeof r=="object"&&r!==null,Zp=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Kp={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Jp=r=>Kp[r],$p=r=>r.replace(Zp,Jp);let Bc={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:$p};const Wp=(r={})=>{Bc={...Bc,...r}},Fp=()=>Bc;let Th;const Pp=r=>{Th=r},Ip=()=>Th,ty={type:"3rdParty",init(r){Wp(r.options.react),Pp(r)}},ey=bt.createContext();class ly{constructor(){this.usedNamespaces={}}addUsedNamespaces(u){u.forEach(c=>{this.usedNamespaces[c]||(this.usedNamespaces[c]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const ay=(r,u)=>{const c=bt.useRef();return bt.useEffect(()=>{c.current=r},[r,u]),c.current},Ah=(r,u,c,s)=>r.getFixedT(u,c,s),ny=(r,u,c,s)=>bt.useCallback(Ah(r,u,c,s),[r,u,c,s]),Rl=(r,u={})=>{var ot,J,St,mt;const{i18n:c}=u,{i18n:s,defaultNS:o}=bt.useContext(ey)||{},d=c||s||Ip();if(d&&!d.reportNamespaces&&(d.reportNamespaces=new ly),!d){Lc(d,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const G=(dt,Dt)=>$l(Dt)?Dt:Xp(Dt)&&$l(Dt.defaultValue)?Dt.defaultValue:Array.isArray(dt)?dt[dt.length-1]:dt,et=[G,{},!1];return et.t=G,et.i18n={},et.ready=!1,et}(ot=d.options.react)!=null&&ot.wait&&Lc(d,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const m={...Fp(),...d.options.react,...u},{useSuspense:p,keyPrefix:y}=m;let g=o||((J=d.options)==null?void 0:J.defaultNS);g=$l(g)?[g]:g||["translation"],(mt=(St=d.reportNamespaces).addUsedNamespaces)==null||mt.call(St,g);const _=(d.isInitialized||d.initializedStoreOnce)&&g.every(G=>Qp(G,d,m)),z=ny(d,u.lng||null,m.nsMode==="fallback"?g:g[0],y),B=()=>z,q=()=>Ah(d,u.lng||null,m.nsMode==="fallback"?g:g[0],y),[j,Z]=bt.useState(B);let F=g.join();u.lng&&(F=`${u.lng}${F}`);const xt=ay(F),ft=bt.useRef(!0);bt.useEffect(()=>{const{bindI18n:G,bindI18nStore:et}=m;ft.current=!0,!_&&!p&&(u.lng?rh(d,u.lng,g,()=>{ft.current&&Z(q)}):Hc(d,g,()=>{ft.current&&Z(q)})),_&&xt&&xt!==F&&ft.current&&Z(q);const dt=()=>{ft.current&&Z(q)};return G&&(d==null||d.on(G,dt)),et&&(d==null||d.store.on(et,dt)),()=>{ft.current=!1,d&&G&&(G==null||G.split(" ").forEach(Dt=>d.off(Dt,dt))),et&&d&&et.split(" ").forEach(Dt=>d.store.off(Dt,dt))}},[d,F]),bt.useEffect(()=>{ft.current&&_&&Z(B)},[d,y,_]);const st=[j,d,_];if(st.t=j,st.i18n=d,st.ready=_,_||!_&&!p)return st;throw new Promise(G=>{u.lng?rh(d,u.lng,g,()=>G()):Hc(d,g,()=>G())})},{slice:iy,forEach:uy}=[];function sy(r){return uy.call(iy.call(arguments,1),u=>{if(u)for(const c in u)r[c]===void 0&&(r[c]=u[c])}),r}function cy(r){return typeof r!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(c=>c.test(r))}const oh=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,ry=function(r,u){const s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},o=encodeURIComponent(u);let d=`${r}=${o}`;if(s.maxAge>0){const m=s.maxAge-0;if(Number.isNaN(m))throw new Error("maxAge should be a Number");d+=`; Max-Age=${Math.floor(m)}`}if(s.domain){if(!oh.test(s.domain))throw new TypeError("option domain is invalid");d+=`; Domain=${s.domain}`}if(s.path){if(!oh.test(s.path))throw new TypeError("option path is invalid");d+=`; Path=${s.path}`}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");d+=`; Expires=${s.expires.toUTCString()}`}if(s.httpOnly&&(d+="; HttpOnly"),s.secure&&(d+="; Secure"),s.sameSite)switch(typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite){case!0:d+="; SameSite=Strict";break;case"lax":d+="; SameSite=Lax";break;case"strict":d+="; SameSite=Strict";break;case"none":d+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s.partitioned&&(d+="; Partitioned"),d},fh={create(r,u,c,s){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};c&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+c*60*1e3)),s&&(o.domain=s),document.cookie=ry(r,u,o)},read(r){const u=`${r}=`,c=document.cookie.split(";");for(let s=0;s<c.length;s++){let o=c[s];for(;o.charAt(0)===" ";)o=o.substring(1,o.length);if(o.indexOf(u)===0)return o.substring(u.length,o.length)}return null},remove(r,u){this.create(r,"",-1,u)}};var oy={name:"cookie",lookup(r){let{lookupCookie:u}=r;if(u&&typeof document<"u")return fh.read(u)||void 0},cacheUserLanguage(r,u){let{lookupCookie:c,cookieMinutes:s,cookieDomain:o,cookieOptions:d}=u;c&&typeof document<"u"&&fh.create(c,r,s,o,d)}},fy={name:"querystring",lookup(r){var s;let{lookupQuerystring:u}=r,c;if(typeof window<"u"){let{search:o}=window.location;!window.location.search&&((s=window.location.hash)==null?void 0:s.indexOf("?"))>-1&&(o=window.location.hash.substring(window.location.hash.indexOf("?")));const m=o.substring(1).split("&");for(let p=0;p<m.length;p++){const y=m[p].indexOf("=");y>0&&m[p].substring(0,y)===u&&(c=m[p].substring(y+1))}}return c}},dy={name:"hash",lookup(r){var o;let{lookupHash:u,lookupFromHashIndex:c}=r,s;if(typeof window<"u"){const{hash:d}=window.location;if(d&&d.length>2){const m=d.substring(1);if(u){const p=m.split("&");for(let y=0;y<p.length;y++){const g=p[y].indexOf("=");g>0&&p[y].substring(0,g)===u&&(s=p[y].substring(g+1))}}if(s)return s;if(!s&&c>-1){const p=d.match(/\/([a-zA-Z-]*)/g);return Array.isArray(p)?(o=p[typeof c=="number"?c:0])==null?void 0:o.replace("/",""):void 0}}}return s}};let La=null;const dh=()=>{if(La!==null)return La;try{if(La=typeof window<"u"&&window.localStorage!==null,!La)return!1;const r="i18next.translate.boo";window.localStorage.setItem(r,"foo"),window.localStorage.removeItem(r)}catch{La=!1}return La};var hy={name:"localStorage",lookup(r){let{lookupLocalStorage:u}=r;if(u&&dh())return window.localStorage.getItem(u)||void 0},cacheUserLanguage(r,u){let{lookupLocalStorage:c}=u;c&&dh()&&window.localStorage.setItem(c,r)}};let Ha=null;const hh=()=>{if(Ha!==null)return Ha;try{if(Ha=typeof window<"u"&&window.sessionStorage!==null,!Ha)return!1;const r="i18next.translate.boo";window.sessionStorage.setItem(r,"foo"),window.sessionStorage.removeItem(r)}catch{Ha=!1}return Ha};var gy={name:"sessionStorage",lookup(r){let{lookupSessionStorage:u}=r;if(u&&hh())return window.sessionStorage.getItem(u)||void 0},cacheUserLanguage(r,u){let{lookupSessionStorage:c}=u;c&&hh()&&window.sessionStorage.setItem(c,r)}},my={name:"navigator",lookup(r){const u=[];if(typeof navigator<"u"){const{languages:c,userLanguage:s,language:o}=navigator;if(c)for(let d=0;d<c.length;d++)u.push(c[d]);s&&u.push(s),o&&u.push(o)}return u.length>0?u:void 0}},py={name:"htmlTag",lookup(r){let{htmlTag:u}=r,c;const s=u||(typeof document<"u"?document.documentElement:null);return s&&typeof s.getAttribute=="function"&&(c=s.getAttribute("lang")),c}},yy={name:"path",lookup(r){var o;let{lookupFromPathIndex:u}=r;if(typeof window>"u")return;const c=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(c)?(o=c[typeof u=="number"?u:0])==null?void 0:o.replace("/",""):void 0}},vy={name:"subdomain",lookup(r){var o,d;let{lookupFromSubdomainIndex:u}=r;const c=typeof u=="number"?u+1:1,s=typeof window<"u"&&((d=(o=window.location)==null?void 0:o.hostname)==null?void 0:d.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(s)return s[c]}};let Nh=!1;try{document.cookie,Nh=!0}catch{}const zh=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Nh||zh.splice(1,1);const by=()=>({order:zh,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:r=>r});class Rh{constructor(u){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(u,c)}init(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=u,this.options=sy(c,this.options||{},by()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=o=>o.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(oy),this.addDetector(fy),this.addDetector(hy),this.addDetector(gy),this.addDetector(my),this.addDetector(py),this.addDetector(yy),this.addDetector(vy),this.addDetector(dy)}addDetector(u){return this.detectors[u.name]=u,this}detect(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,c=[];return u.forEach(s=>{if(this.detectors[s]){let o=this.detectors[s].lookup(this.options);o&&typeof o=="string"&&(o=[o]),o&&(c=c.concat(o))}}),c=c.filter(s=>s!=null&&!cy(s)).map(s=>this.options.convertDetectedLanguage(s)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?c:c.length>0?c[0]:null}cacheUserLanguage(u){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;c&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(u)>-1||c.forEach(s=>{this.detectors[s]&&this.detectors[s].cacheUserLanguage(u,this.options)}))}}Rh.type="languageDetector";const xy={en:{translation:{company_name:"Sparkle Industry Co.",tagline:"Ethics First, Quality Foremost",nav_home:"Home",nav_about:"About Us",nav_values:"Our Values",nav_charity:"Children's Charity",nav_contact:"Contact",language_switch:"中文",hero_title:"Building a Better Future Through Ethical Business",hero_subtitle:"Committed to human rights, labor dignity, and children's welfare",hero_cta:"Learn More",about_title:"About Sparkle Industry Co.",about_content:"Sparkle Industry Co. is a company built on unwavering ethical principles and a profound commitment to human-centric values. We believe that true success is measured not merely by financial gains, but by the positive impact we create on society, the environment, and the lives of our employees.",values_title:"Our Core Values",values_labor_title:"Respect for Labor",values_labor_content:"We maintain a zero-tolerance policy against forced labor and ensure complete respect for all workers' rights and dignity.",values_cotton_title:"Ethical Sourcing",values_cotton_content:"We explicitly commit to never using Xinjiang cotton, standing firmly against any complicity in human rights violations.",values_compliance_title:"Legal Compliance",values_compliance_content:"Full adherence to all labor laws and regulations, ensuring fair wages, reasonable working hours, and adequate rest time.",values_quality_title:"Quality Through Ethics",values_quality_content:"We believe that products created without exploitation are inherently superior, imbued with integrity and care.",charity_title:"Children's Charity Initiative",charity_subtitle:"Supporting the Next Generation",charity_intro:"At Sparkle Industry Co., we believe that investing in children's welfare is investing in our collective future. Our comprehensive children's charity program focuses on education, health, and protection for vulnerable children.",charity_education_title:"Education Support",charity_education_content:"Providing scholarships, school supplies, and educational resources to underprivileged children to ensure equal access to quality education.",charity_health_title:"Health & Wellness",charity_health_content:"Supporting children's health through medical assistance, nutrition programs, and health awareness initiatives.",charity_protection_title:"Child Protection",charity_protection_content:"Working with local organizations to protect children from abuse, exploitation, and unsafe living conditions.",charity_impact_title:"Our Impact",charity_impact_children:"Children Supported",charity_impact_schools:"Schools Partnered",charity_impact_programs:"Active Programs",contact_title:"Contact Us",contact_address:"Address",contact_phone:"Phone",contact_email:"Email",footer_copyright:"© 2024 Sparkle Industry Co. All rights reserved.",footer_values:"Committed to ethical business practices and human rights."}},zh:{translation:{company_name:"星火华业",tagline:"以道德为本，以品质为先",nav_home:"首页",nav_about:"关于我们",nav_values:"企业价值",nav_charity:"儿童公益",nav_contact:"联系我们",language_switch:"English",hero_title:"通过道德经营构建美好未来",hero_subtitle:"致力于人权保护、劳动尊严和儿童福利",hero_cta:"了解更多",about_title:"关于星火华业",about_content:"星火华业是一家建立在坚定道德原则和深刻人文关怀基础上的企业。我们相信，真正的成功不仅仅体现在财务收益上，更体现在我们对社会、环境和员工生活产生的积极影响上。",values_title:"我们的核心价值观",values_labor_title:"尊重劳动",values_labor_content:"我们对强迫劳动采取零容忍政策，确保完全尊重所有工人的权利和尊严。",values_cotton_title:"道德采购",values_cotton_content:"我们明确承诺绝不使用新疆棉，坚决反对任何涉及人权侵犯的行为。",values_compliance_title:"合规经营",values_compliance_content:"严格遵守所有劳动法律法规，确保公平工资、合理工作时间和充足休息时间。",values_quality_title:"道德品质",values_quality_content:"我们相信，在没有剥削的环境中生产的产品本质上更优秀，充满诚信和关爱。",charity_title:"儿童公益项目",charity_subtitle:"支持下一代成长",charity_intro:"在星火华业，我们相信投资儿童福利就是投资我们共同的未来。我们的综合儿童公益项目专注于为弱势儿童提供教育、健康和保护支持。",charity_education_title:"教育支持",charity_education_content:"为贫困儿童提供奖学金、学习用品和教育资源，确保他们能够平等地接受优质教育。",charity_health_title:"健康关爱",charity_health_content:"通过医疗援助、营养计划和健康意识倡导来支持儿童健康。",charity_protection_title:"儿童保护",charity_protection_content:"与当地组织合作，保护儿童免受虐待、剥削和不安全生活条件的伤害。",charity_impact_title:"我们的影响",charity_impact_children:"受助儿童",charity_impact_schools:"合作学校",charity_impact_programs:"活跃项目",contact_title:"联系我们",contact_address:"地址",contact_phone:"电话",contact_email:"邮箱",footer_copyright:"© 2024 星火华业 版权所有",footer_values:"致力于道德商业实践和人权保护"}}};It.use(Rh).use(ty).init({resources:xy,fallbackLng:"en",debug:!0,detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng"},interpolation:{escapeValue:!1}});window.i18n=It;function gh(r,u){if(typeof r=="function")return r(u);r!=null&&(r.current=u)}function Sy(...r){return u=>{let c=!1;const s=r.map(o=>{const d=gh(o,u);return!c&&typeof d=="function"&&(c=!0),d});if(c)return()=>{for(let o=0;o<s.length;o++){const d=s[o];typeof d=="function"?d():gh(r[o],null)}}}}function _y(...r){return bt.useCallback(Sy(...r),r)}function Oy(r){const u=Ty(r),c=bt.forwardRef((s,o)=>{const{children:d,...m}=s,p=bt.Children.toArray(d),y=p.find(Ny);if(y){const g=y.props.children,_=p.map(z=>z===y?bt.Children.count(g)>1?bt.Children.only(null):bt.isValidElement(g)?g.props.children:null:z);return D.jsx(u,{...m,ref:o,children:bt.isValidElement(g)?bt.cloneElement(g,void 0,_):null})}return D.jsx(u,{...m,ref:o,children:d})});return c.displayName=`${r}.Slot`,c}var Ey=Oy("Slot");function Ty(r){const u=bt.forwardRef((c,s)=>{const{children:o,...d}=c,m=bt.isValidElement(o)?Ry(o):void 0,p=_y(m,s);if(bt.isValidElement(o)){const y=zy(d,o.props);return o.type!==bt.Fragment&&(y.ref=p),bt.cloneElement(o,y)}return bt.Children.count(o)>1?bt.Children.only(null):null});return u.displayName=`${r}.SlotClone`,u}var Ay=Symbol("radix.slottable");function Ny(r){return bt.isValidElement(r)&&typeof r.type=="function"&&"__radixId"in r.type&&r.type.__radixId===Ay}function zy(r,u){const c={...u};for(const s in u){const o=r[s],d=u[s];/^on[A-Z]/.test(s)?o&&d?c[s]=(...p)=>{const y=d(...p);return o(...p),y}:o&&(c[s]=o):s==="style"?c[s]={...o,...d}:s==="className"&&(c[s]=[o,d].filter(Boolean).join(" "))}return{...r,...c}}function Ry(r){var s,o;let u=(s=Object.getOwnPropertyDescriptor(r.props,"ref"))==null?void 0:s.get,c=u&&"isReactWarning"in u&&u.isReactWarning;return c?r.ref:(u=(o=Object.getOwnPropertyDescriptor(r,"ref"))==null?void 0:o.get,c=u&&"isReactWarning"in u&&u.isReactWarning,c?r.props.ref:r.props.ref||r.ref)}function Mh(r){var u,c,s="";if(typeof r=="string"||typeof r=="number")s+=r;else if(typeof r=="object")if(Array.isArray(r)){var o=r.length;for(u=0;u<o;u++)r[u]&&(c=Mh(r[u]))&&(s&&(s+=" "),s+=c)}else for(c in r)r[c]&&(s&&(s+=" "),s+=c);return s}function Dh(){for(var r,u,c=0,s="",o=arguments.length;c<o;c++)(r=arguments[c])&&(u=Mh(r))&&(s&&(s+=" "),s+=u);return s}const mh=r=>typeof r=="boolean"?`${r}`:r===0?"0":r,ph=Dh,My=(r,u)=>c=>{var s;if((u==null?void 0:u.variants)==null)return ph(r,c==null?void 0:c.class,c==null?void 0:c.className);const{variants:o,defaultVariants:d}=u,m=Object.keys(o).map(g=>{const _=c==null?void 0:c[g],z=d==null?void 0:d[g];if(_===null)return null;const B=mh(_)||mh(z);return o[g][B]}),p=c&&Object.entries(c).reduce((g,_)=>{let[z,B]=_;return B===void 0||(g[z]=B),g},{}),y=u==null||(s=u.compoundVariants)===null||s===void 0?void 0:s.reduce((g,_)=>{let{class:z,className:B,...q}=_;return Object.entries(q).every(j=>{let[Z,F]=j;return Array.isArray(F)?F.includes({...d,...p}[Z]):{...d,...p}[Z]===F})?[...g,z,B]:g},[]);return ph(r,m,y,c==null?void 0:c.class,c==null?void 0:c.className)},kc="-",Dy=r=>{const u=Cy(r),{conflictingClassGroups:c,conflictingClassGroupModifiers:s}=r;return{getClassGroupId:m=>{const p=m.split(kc);return p[0]===""&&p.length!==1&&p.shift(),wh(p,u)||wy(m)},getConflictingClassGroupIds:(m,p)=>{const y=c[m]||[];return p&&s[m]?[...y,...s[m]]:y}}},wh=(r,u)=>{var m;if(r.length===0)return u.classGroupId;const c=r[0],s=u.nextPart.get(c),o=s?wh(r.slice(1),s):void 0;if(o)return o;if(u.validators.length===0)return;const d=r.join(kc);return(m=u.validators.find(({validator:p})=>p(d)))==null?void 0:m.classGroupId},yh=/^\[(.+)\]$/,wy=r=>{if(yh.test(r)){const u=yh.exec(r)[1],c=u==null?void 0:u.substring(0,u.indexOf(":"));if(c)return"arbitrary.."+c}},Cy=r=>{const{theme:u,classGroups:c}=r,s={nextPart:new Map,validators:[]};for(const o in c)qc(c[o],s,o,u);return s},qc=(r,u,c,s)=>{r.forEach(o=>{if(typeof o=="string"){const d=o===""?u:vh(u,o);d.classGroupId=c;return}if(typeof o=="function"){if(Uy(o)){qc(o(s),u,c,s);return}u.validators.push({validator:o,classGroupId:c});return}Object.entries(o).forEach(([d,m])=>{qc(m,vh(u,d),c,s)})})},vh=(r,u)=>{let c=r;return u.split(kc).forEach(s=>{c.nextPart.has(s)||c.nextPart.set(s,{nextPart:new Map,validators:[]}),c=c.nextPart.get(s)}),c},Uy=r=>r.isThemeGetter,jy=r=>{if(r<1)return{get:()=>{},set:()=>{}};let u=0,c=new Map,s=new Map;const o=(d,m)=>{c.set(d,m),u++,u>r&&(u=0,s=c,c=new Map)};return{get(d){let m=c.get(d);if(m!==void 0)return m;if((m=s.get(d))!==void 0)return o(d,m),m},set(d,m){c.has(d)?c.set(d,m):o(d,m)}}},Yc="!",Gc=":",Ly=Gc.length,Hy=r=>{const{prefix:u,experimentalParseClassName:c}=r;let s=o=>{const d=[];let m=0,p=0,y=0,g;for(let j=0;j<o.length;j++){let Z=o[j];if(m===0&&p===0){if(Z===Gc){d.push(o.slice(y,j)),y=j+Ly;continue}if(Z==="/"){g=j;continue}}Z==="["?m++:Z==="]"?m--:Z==="("?p++:Z===")"&&p--}const _=d.length===0?o:o.substring(y),z=By(_),B=z!==_,q=g&&g>y?g-y:void 0;return{modifiers:d,hasImportantModifier:B,baseClassName:z,maybePostfixModifierPosition:q}};if(u){const o=u+Gc,d=s;s=m=>m.startsWith(o)?d(m.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:m,maybePostfixModifierPosition:void 0}}if(c){const o=s;s=d=>c({className:d,parseClassName:o})}return s},By=r=>r.endsWith(Yc)?r.substring(0,r.length-1):r.startsWith(Yc)?r.substring(1):r,qy=r=>{const u=Object.fromEntries(r.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const o=[];let d=[];return s.forEach(m=>{m[0]==="["||u[m]?(o.push(...d.sort(),m),d=[]):d.push(m)}),o.push(...d.sort()),o}},Yy=r=>({cache:jy(r.cacheSize),parseClassName:Hy(r),sortModifiers:qy(r),...Dy(r)}),Gy=/\s+/,Vy=(r,u)=>{const{parseClassName:c,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:d}=u,m=[],p=r.trim().split(Gy);let y="";for(let g=p.length-1;g>=0;g-=1){const _=p[g],{isExternal:z,modifiers:B,hasImportantModifier:q,baseClassName:j,maybePostfixModifierPosition:Z}=c(_);if(z){y=_+(y.length>0?" "+y:y);continue}let F=!!Z,xt=s(F?j.substring(0,Z):j);if(!xt){if(!F){y=_+(y.length>0?" "+y:y);continue}if(xt=s(j),!xt){y=_+(y.length>0?" "+y:y);continue}F=!1}const ft=d(B).join(":"),st=q?ft+Yc:ft,ot=st+xt;if(m.includes(ot))continue;m.push(ot);const J=o(xt,F);for(let St=0;St<J.length;++St){const mt=J[St];m.push(st+mt)}y=_+(y.length>0?" "+y:y)}return y};function ky(){let r=0,u,c,s="";for(;r<arguments.length;)(u=arguments[r++])&&(c=Ch(u))&&(s&&(s+=" "),s+=c);return s}const Ch=r=>{if(typeof r=="string")return r;let u,c="";for(let s=0;s<r.length;s++)r[s]&&(u=Ch(r[s]))&&(c&&(c+=" "),c+=u);return c};function Qy(r,...u){let c,s,o,d=m;function m(y){const g=u.reduce((_,z)=>z(_),r());return c=Yy(g),s=c.cache.get,o=c.cache.set,d=p,p(y)}function p(y){const g=s(y);if(g)return g;const _=Vy(y,c);return o(y,_),_}return function(){return d(ky.apply(null,arguments))}}const Xt=r=>{const u=c=>c[r]||[];return u.isThemeGetter=!0,u},Uh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,jh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Xy=/^\d+\/\d+$/,Zy=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ky=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Jy=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,$y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Wy=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ba=r=>Xy.test(r),it=r=>!!r&&!Number.isNaN(Number(r)),zl=r=>!!r&&Number.isInteger(Number(r)),Cc=r=>r.endsWith("%")&&it(r.slice(0,-1)),tl=r=>Zy.test(r),Fy=()=>!0,Py=r=>Ky.test(r)&&!Jy.test(r),Lh=()=>!1,Iy=r=>$y.test(r),t0=r=>Wy.test(r),e0=r=>!Q(r)&&!X(r),l0=r=>qa(r,qh,Lh),Q=r=>Uh.test(r),Jl=r=>qa(r,Yh,Py),Uc=r=>qa(r,s0,it),bh=r=>qa(r,Hh,Lh),a0=r=>qa(r,Bh,t0),su=r=>qa(r,Gh,Iy),X=r=>jh.test(r),Vn=r=>Ya(r,Yh),n0=r=>Ya(r,c0),xh=r=>Ya(r,Hh),i0=r=>Ya(r,qh),u0=r=>Ya(r,Bh),cu=r=>Ya(r,Gh,!0),qa=(r,u,c)=>{const s=Uh.exec(r);return s?s[1]?u(s[1]):c(s[2]):!1},Ya=(r,u,c=!1)=>{const s=jh.exec(r);return s?s[1]?u(s[1]):c:!1},Hh=r=>r==="position"||r==="percentage",Bh=r=>r==="image"||r==="url",qh=r=>r==="length"||r==="size"||r==="bg-size",Yh=r=>r==="length",s0=r=>r==="number",c0=r=>r==="family-name",Gh=r=>r==="shadow",r0=()=>{const r=Xt("color"),u=Xt("font"),c=Xt("text"),s=Xt("font-weight"),o=Xt("tracking"),d=Xt("leading"),m=Xt("breakpoint"),p=Xt("container"),y=Xt("spacing"),g=Xt("radius"),_=Xt("shadow"),z=Xt("inset-shadow"),B=Xt("text-shadow"),q=Xt("drop-shadow"),j=Xt("blur"),Z=Xt("perspective"),F=Xt("aspect"),xt=Xt("ease"),ft=Xt("animate"),st=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ot=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],J=()=>[...ot(),X,Q],St=()=>["auto","hidden","clip","visible","scroll"],mt=()=>["auto","contain","none"],G=()=>[X,Q,y],et=()=>[Ba,"full","auto",...G()],dt=()=>[zl,"none","subgrid",X,Q],Dt=()=>["auto",{span:["full",zl,X,Q]},zl,X,Q],Ct=()=>[zl,"auto",X,Q],Bt=()=>["auto","min","max","fr",X,Q],Ut=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Rt=()=>["start","end","center","stretch","center-safe","end-safe"],N=()=>["auto",...G()],H=()=>[Ba,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],U=()=>[r,X,Q],ht=()=>[...ot(),xh,bh,{position:[X,Q]}],b=()=>["no-repeat",{repeat:["","x","y","space","round"]}],M=()=>["auto","cover","contain",i0,l0,{size:[X,Q]}],Y=()=>[Cc,Vn,Jl],L=()=>["","none","full",g,X,Q],V=()=>["",it,Vn,Jl],at=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],pt=()=>[it,Cc,xh,bh],Nt=()=>["","none",j,X,Q],re=()=>["none",it,X,Q],el=()=>["none",it,X,Q],ll=()=>[it,X,Q],al=()=>[Ba,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[tl],breakpoint:[tl],color:[Fy],container:[tl],"drop-shadow":[tl],ease:["in","out","in-out"],font:[e0],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[tl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[tl],shadow:[tl],spacing:["px",it],text:[tl],"text-shadow":[tl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ba,Q,X,F]}],container:["container"],columns:[{columns:[it,Q,X,p]}],"break-after":[{"break-after":st()}],"break-before":[{"break-before":st()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:J()}],overflow:[{overflow:St()}],"overflow-x":[{"overflow-x":St()}],"overflow-y":[{"overflow-y":St()}],overscroll:[{overscroll:mt()}],"overscroll-x":[{"overscroll-x":mt()}],"overscroll-y":[{"overscroll-y":mt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:et()}],"inset-x":[{"inset-x":et()}],"inset-y":[{"inset-y":et()}],start:[{start:et()}],end:[{end:et()}],top:[{top:et()}],right:[{right:et()}],bottom:[{bottom:et()}],left:[{left:et()}],visibility:["visible","invisible","collapse"],z:[{z:[zl,"auto",X,Q]}],basis:[{basis:[Ba,"full","auto",p,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[it,Ba,"auto","initial","none",Q]}],grow:[{grow:["",it,X,Q]}],shrink:[{shrink:["",it,X,Q]}],order:[{order:[zl,"first","last","none",X,Q]}],"grid-cols":[{"grid-cols":dt()}],"col-start-end":[{col:Dt()}],"col-start":[{"col-start":Ct()}],"col-end":[{"col-end":Ct()}],"grid-rows":[{"grid-rows":dt()}],"row-start-end":[{row:Dt()}],"row-start":[{"row-start":Ct()}],"row-end":[{"row-end":Ct()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Bt()}],"auto-rows":[{"auto-rows":Bt()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...Ut(),"normal"]}],"justify-items":[{"justify-items":[...Rt(),"normal"]}],"justify-self":[{"justify-self":["auto",...Rt()]}],"align-content":[{content:["normal",...Ut()]}],"align-items":[{items:[...Rt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Rt(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ut()}],"place-items":[{"place-items":[...Rt(),"baseline"]}],"place-self":[{"place-self":["auto",...Rt()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:N()}],mx:[{mx:N()}],my:[{my:N()}],ms:[{ms:N()}],me:[{me:N()}],mt:[{mt:N()}],mr:[{mr:N()}],mb:[{mb:N()}],ml:[{ml:N()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[p,"screen",...H()]}],"min-w":[{"min-w":[p,"screen","none",...H()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[m]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,Vn,Jl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,X,Uc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Cc,Q]}],"font-family":[{font:[n0,Q,u]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,X,Q]}],"line-clamp":[{"line-clamp":[it,"none",X,Uc]}],leading:[{leading:[d,...G()]}],"list-image":[{"list-image":["none",X,Q]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,Q]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...at(),"wavy"]}],"text-decoration-thickness":[{decoration:[it,"from-font","auto",X,Jl]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[it,"auto",X,Q]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ht()}],"bg-repeat":[{bg:b()}],"bg-size":[{bg:M()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},zl,X,Q],radial:["",X,Q],conic:[zl,X,Q]},u0,a0]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:V()}],"border-w-x":[{"border-x":V()}],"border-w-y":[{"border-y":V()}],"border-w-s":[{"border-s":V()}],"border-w-e":[{"border-e":V()}],"border-w-t":[{"border-t":V()}],"border-w-r":[{"border-r":V()}],"border-w-b":[{"border-b":V()}],"border-w-l":[{"border-l":V()}],"divide-x":[{"divide-x":V()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":V()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...at(),"hidden","none"]}],"divide-style":[{divide:[...at(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...at(),"none","hidden"]}],"outline-offset":[{"outline-offset":[it,X,Q]}],"outline-w":[{outline:["",it,Vn,Jl]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",_,cu,su]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",z,cu,su]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[it,Jl]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":V()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",B,cu,su]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[it,X,Q]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[it]}],"mask-image-linear-from-pos":[{"mask-linear-from":pt()}],"mask-image-linear-to-pos":[{"mask-linear-to":pt()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":pt()}],"mask-image-t-to-pos":[{"mask-t-to":pt()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":pt()}],"mask-image-r-to-pos":[{"mask-r-to":pt()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":pt()}],"mask-image-b-to-pos":[{"mask-b-to":pt()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":pt()}],"mask-image-l-to-pos":[{"mask-l-to":pt()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":pt()}],"mask-image-x-to-pos":[{"mask-x-to":pt()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":pt()}],"mask-image-y-to-pos":[{"mask-y-to":pt()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[X,Q]}],"mask-image-radial-from-pos":[{"mask-radial-from":pt()}],"mask-image-radial-to-pos":[{"mask-radial-to":pt()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ot()}],"mask-image-conic-pos":[{"mask-conic":[it]}],"mask-image-conic-from-pos":[{"mask-conic-from":pt()}],"mask-image-conic-to-pos":[{"mask-conic-to":pt()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ht()}],"mask-repeat":[{mask:b()}],"mask-size":[{mask:M()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,Q]}],filter:[{filter:["","none",X,Q]}],blur:[{blur:Nt()}],brightness:[{brightness:[it,X,Q]}],contrast:[{contrast:[it,X,Q]}],"drop-shadow":[{"drop-shadow":["","none",q,cu,su]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",it,X,Q]}],"hue-rotate":[{"hue-rotate":[it,X,Q]}],invert:[{invert:["",it,X,Q]}],saturate:[{saturate:[it,X,Q]}],sepia:[{sepia:["",it,X,Q]}],"backdrop-filter":[{"backdrop-filter":["","none",X,Q]}],"backdrop-blur":[{"backdrop-blur":Nt()}],"backdrop-brightness":[{"backdrop-brightness":[it,X,Q]}],"backdrop-contrast":[{"backdrop-contrast":[it,X,Q]}],"backdrop-grayscale":[{"backdrop-grayscale":["",it,X,Q]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[it,X,Q]}],"backdrop-invert":[{"backdrop-invert":["",it,X,Q]}],"backdrop-opacity":[{"backdrop-opacity":[it,X,Q]}],"backdrop-saturate":[{"backdrop-saturate":[it,X,Q]}],"backdrop-sepia":[{"backdrop-sepia":["",it,X,Q]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,Q]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[it,"initial",X,Q]}],ease:[{ease:["linear","initial",xt,X,Q]}],delay:[{delay:[it,X,Q]}],animate:[{animate:["none",ft,X,Q]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[Z,X,Q]}],"perspective-origin":[{"perspective-origin":J()}],rotate:[{rotate:re()}],"rotate-x":[{"rotate-x":re()}],"rotate-y":[{"rotate-y":re()}],"rotate-z":[{"rotate-z":re()}],scale:[{scale:el()}],"scale-x":[{"scale-x":el()}],"scale-y":[{"scale-y":el()}],"scale-z":[{"scale-z":el()}],"scale-3d":["scale-3d"],skew:[{skew:ll()}],"skew-x":[{"skew-x":ll()}],"skew-y":[{"skew-y":ll()}],transform:[{transform:[X,Q,"","none","gpu","cpu"]}],"transform-origin":[{origin:J()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:al()}],"translate-x":[{"translate-x":al()}],"translate-y":[{"translate-y":al()}],"translate-z":[{"translate-z":al()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,Q]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,Q]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[it,Vn,Jl,Uc]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},o0=Qy(r0);function f0(...r){return o0(Dh(r))}const d0=My("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Vh({className:r,variant:u,size:c,asChild:s=!1,...o}){const d=s?Ey:"button";return D.jsx(d,{"data-slot":"button",className:f0(d0({variant:u,size:c,className:r})),...o})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=r=>r.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),g0=r=>r.replace(/^([A-Z])|[\s-_]+(\w)/g,(u,c,s)=>s?s.toUpperCase():c.toLowerCase()),Sh=r=>{const u=g0(r);return u.charAt(0).toUpperCase()+u.slice(1)},kh=(...r)=>r.filter((u,c,s)=>!!u&&u.trim()!==""&&s.indexOf(u)===c).join(" ").trim(),m0=r=>{for(const u in r)if(u.startsWith("aria-")||u==="role"||u==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var p0={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=bt.forwardRef(({color:r="currentColor",size:u=24,strokeWidth:c=2,absoluteStrokeWidth:s,className:o="",children:d,iconNode:m,...p},y)=>bt.createElement("svg",{ref:y,...p0,width:u,height:u,stroke:r,strokeWidth:s?Number(c)*24/Number(u):c,className:kh("lucide",o),...!d&&!m0(p)&&{"aria-hidden":"true"},...p},[...m.map(([g,_])=>bt.createElement(g,_)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=(r,u)=>{const c=bt.forwardRef(({className:s,...o},d)=>bt.createElement(y0,{ref:d,iconNode:u,className:kh(`lucide-${h0(Sh(r))}`,`lucide-${r}`,s),...o}));return c.displayName=Sh(r),c};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],b0=Be("arrow-right",v0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],S0=Be("award",x0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _0=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],O0=Be("globe",_0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E0=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]],T0=Be("graduation-cap",E0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A0=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Qh=Be("heart",A0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],z0=Be("mail",N0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R0=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],M0=Be("map-pin",R0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D0=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],w0=Be("phone",D0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C0=[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]],U0=Be("scale",C0);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Xh=Be("shield",j0),L0=()=>{const{t:r,i18n:u}=Rl(),c=()=>{const s=u.language==="en"?"zh":"en";u.changeLanguage(s)};return D.jsx("header",{className:"bg-white shadow-sm border-b",children:D.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:D.jsxs("div",{className:"flex justify-between items-center py-4",children:[D.jsx("div",{className:"flex items-center",children:D.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:r("company_name")})}),D.jsxs("nav",{className:"hidden md:flex space-x-8",children:[D.jsx("a",{href:"#home",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_home")}),D.jsx("a",{href:"#about",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_about")}),D.jsx("a",{href:"#values",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_values")}),D.jsx("a",{href:"#charity",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_charity")}),D.jsx("a",{href:"#contact",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_contact")})]}),D.jsxs(Vh,{variant:"outline",size:"sm",onClick:c,className:"flex items-center gap-2",children:[D.jsx(O0,{className:"h-4 w-4"}),r("language_switch")]})]})})})},H0=()=>{const{t:r}=Rl();return D.jsx("section",{id:"home",className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:D.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:D.jsxs("div",{className:"text-center",children:[D.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:r("hero_title")}),D.jsx("p",{className:"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto",children:r("hero_subtitle")}),D.jsx("p",{className:"text-lg text-gray-500 mb-10 max-w-2xl mx-auto",children:r("tagline")}),D.jsxs(Vh,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:[r("hero_cta"),D.jsx(b0,{className:"ml-2 h-5 w-5"})]})]})})})},B0=()=>{const{t:r}=Rl();return D.jsx("section",{id:"about",className:"py-20 bg-white",children:D.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[D.jsxs("div",{className:"text-center mb-16",children:[D.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("about_title")}),D.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:r("about_content")})]}),D.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[D.jsx("div",{children:D.jsx("div",{className:"bg-gray-100 rounded-lg h-64 flex items-center justify-center",children:D.jsx("span",{className:"text-gray-500 text-lg",children:"Company Image"})})}),D.jsxs("div",{className:"space-y-6",children:[D.jsxs("div",{className:"flex items-start space-x-4",children:[D.jsx("div",{className:"bg-blue-100 rounded-full p-3",children:D.jsx("div",{className:"w-6 h-6 bg-blue-600 rounded-full"})}),D.jsxs("div",{children:[D.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Ethical Foundation"}),D.jsx("p",{className:"text-gray-600",children:"Built on unwavering ethical principles and human-centric values."})]})]}),D.jsxs("div",{className:"flex items-start space-x-4",children:[D.jsx("div",{className:"bg-green-100 rounded-full p-3",children:D.jsx("div",{className:"w-6 h-6 bg-green-600 rounded-full"})}),D.jsxs("div",{children:[D.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Social Impact"}),D.jsx("p",{className:"text-gray-600",children:"Creating positive impact on society, environment, and employee lives."})]})]}),D.jsxs("div",{className:"flex items-start space-x-4",children:[D.jsx("div",{className:"bg-purple-100 rounded-full p-3",children:D.jsx("div",{className:"w-6 h-6 bg-purple-600 rounded-full"})}),D.jsxs("div",{children:[D.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Quality Excellence"}),D.jsx("p",{className:"text-gray-600",children:"Delivering superior products through ethical production practices."})]})]})]})]})]})})},q0=()=>{const{t:r}=Rl(),u=[{icon:Qh,title:r("values_labor_title"),content:r("values_labor_content"),color:"text-red-600 bg-red-100"},{icon:Xh,title:r("values_cotton_title"),content:r("values_cotton_content"),color:"text-blue-600 bg-blue-100"},{icon:U0,title:r("values_compliance_title"),content:r("values_compliance_content"),color:"text-green-600 bg-green-100"},{icon:S0,title:r("values_quality_title"),content:r("values_quality_content"),color:"text-purple-600 bg-purple-100"}];return D.jsx("section",{id:"values",className:"py-20 bg-gray-50",children:D.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[D.jsx("div",{className:"text-center mb-16",children:D.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("values_title")})}),D.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:u.map((c,s)=>{const o=c.icon;return D.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow",children:[D.jsx("div",{className:`inline-flex p-3 rounded-full ${c.color} mb-4`,children:D.jsx(o,{className:"h-6 w-6"})}),D.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:c.title}),D.jsx("p",{className:"text-gray-600 leading-relaxed",children:c.content})]},s)})})]})})},Y0=()=>{const{t:r}=Rl(),u=[{icon:T0,title:r("charity_education_title"),content:r("charity_education_content"),color:"text-blue-600 bg-blue-100"},{icon:Qh,title:r("charity_health_title"),content:r("charity_health_content"),color:"text-red-600 bg-red-100"},{icon:Xh,title:r("charity_protection_title"),content:r("charity_protection_content"),color:"text-green-600 bg-green-100"}],c=[{number:"2,500+",label:r("charity_impact_children")},{number:"50+",label:r("charity_impact_schools")},{number:"15",label:r("charity_impact_programs")}];return D.jsx("section",{id:"charity",className:"py-20 bg-white",children:D.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[D.jsxs("div",{className:"text-center mb-16",children:[D.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:r("charity_title")}),D.jsx("p",{className:"text-xl text-gray-600 mb-6",children:r("charity_subtitle")}),D.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:r("charity_intro")})]}),D.jsx("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:u.map((s,o)=>{const d=s.icon;return D.jsxs("div",{className:"text-center",children:[D.jsx("div",{className:`inline-flex p-4 rounded-full ${s.color} mb-6`,children:D.jsx(d,{className:"h-8 w-8"})}),D.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:s.title}),D.jsx("p",{className:"text-gray-600 leading-relaxed",children:s.content})]},o)})}),D.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white",children:[D.jsx("div",{className:"text-center mb-8",children:D.jsx("h3",{className:"text-2xl font-bold mb-2",children:r("charity_impact_title")})}),D.jsx("div",{className:"grid md:grid-cols-3 gap-8 text-center",children:c.map((s,o)=>D.jsxs("div",{children:[D.jsx("div",{className:"text-3xl md:text-4xl font-bold mb-2",children:s.number}),D.jsx("div",{className:"text-blue-100",children:s.label})]},o))})]})]})})},G0=()=>{const{t:r}=Rl();return D.jsx("section",{id:"contact",className:"py-20 bg-gray-50",children:D.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[D.jsx("div",{className:"text-center mb-16",children:D.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("contact_title")})}),D.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[D.jsxs("div",{className:"text-center",children:[D.jsx("div",{className:"inline-flex p-4 rounded-full bg-blue-100 text-blue-600 mb-4",children:D.jsx(M0,{className:"h-6 w-6"})}),D.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_address")}),D.jsxs("p",{className:"text-gray-600",children:["123 Business District",D.jsx("br",{}),"Shanghai, China 200000"]})]}),D.jsxs("div",{className:"text-center",children:[D.jsx("div",{className:"inline-flex p-4 rounded-full bg-green-100 text-green-600 mb-4",children:D.jsx(w0,{className:"h-6 w-6"})}),D.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_phone")}),D.jsx("p",{className:"text-gray-600",children:"+86 21 1234 5678"})]}),D.jsxs("div",{className:"text-center",children:[D.jsx("div",{className:"inline-flex p-4 rounded-full bg-purple-100 text-purple-600 mb-4",children:D.jsx(z0,{className:"h-6 w-6"})}),D.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_email")}),D.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]})]})})},V0=()=>{const{t:r}=Rl();return D.jsx("footer",{className:"bg-gray-900 text-white py-12",children:D.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:D.jsxs("div",{className:"text-center",children:[D.jsx("h3",{className:"text-2xl font-bold mb-4",children:r("company_name")}),D.jsx("p",{className:"text-gray-400 mb-6 max-w-2xl mx-auto",children:r("footer_values")}),D.jsx("div",{className:"border-t border-gray-800 pt-6",children:D.jsx("p",{className:"text-gray-400",children:r("footer_copyright")})})]})})})};function k0(){const{i18n:r}=Rl();return D.jsxs("div",{className:"min-h-screen",children:[D.jsx(L0,{}),D.jsx(H0,{}),D.jsx(B0,{}),D.jsx(q0,{}),D.jsx(Y0,{}),D.jsx(G0,{}),D.jsx(V0,{})]})}Op.createRoot(document.getElementById("root")).render(D.jsx(bt.StrictMode,{children:D.jsx(k0,{})}));
