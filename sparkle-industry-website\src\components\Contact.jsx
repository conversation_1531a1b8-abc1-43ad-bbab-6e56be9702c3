import { useTranslation } from 'react-i18next';
import { MapPin, Phone, Mail } from 'lucide-react';

const Contact = () => {
  const { t } = useTranslation();

  return (
    <section id="contact" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            {t('contact_title')}
          </h2>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="inline-flex p-4 rounded-full bg-blue-100 text-blue-600 mb-4">
              <MapPin className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {t('contact_address')}
            </h3>
            <p className="text-gray-600">
              123 Business District<br />
              Shanghai, China 200000
            </p>
          </div>
          
          <div className="text-center">
            <div className="inline-flex p-4 rounded-full bg-green-100 text-green-600 mb-4">
              <Phone className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {t('contact_phone')}
            </h3>
            <p className="text-gray-600">
              +86 21 1234 5678
            </p>
          </div>
          
          <div className="text-center">
            <div className="inline-flex p-4 rounded-full bg-purple-100 text-purple-600 mb-4">
              <Mail className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {t('contact_email')}
            </h3>
            <p className="text-gray-600">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;

