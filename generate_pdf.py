#!/usr/bin/env python3
"""
Script to generate PDF whitepaper from HTML
Requires: pip install weasyprint
"""

try:
    from weasyprint import HTML, CSS
    import os
    
    def generate_pdf():
        # Get the current directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_file = os.path.join(current_dir, 'sparkle_industry_whitepaper.html')
        pdf_file = os.path.join(current_dir, 'sparkle_industry_whitepaper.pdf')
        
        # Check if HTML file exists
        if not os.path.exists(html_file):
            print(f"Error: HTML file not found at {html_file}")
            return False
        
        try:
            # Additional CSS for PDF formatting
            pdf_css = CSS(string='''
                @page {
                    size: A4;
                    margin: 2cm;
                    @bottom-center {
                        content: "Sparkle Industry Co. - Corporate Values Whitepaper | Page " counter(page);
                        font-size: 10px;
                        color: #666;
                    }
                }
                body {
                    font-size: 12px;
                }
                .page-break {
                    page-break-before: always;
                }
            ''')
            
            # Generate PDF
            HTML(filename=html_file).write_pdf(pdf_file, stylesheets=[pdf_css])
            print(f"PDF successfully generated: {pdf_file}")
            return True
            
        except Exception as e:
            print(f"Error generating PDF: {e}")
            return False
    
    if __name__ == "__main__":
        generate_pdf()

except ImportError:
    print("WeasyPrint not installed. Installing...")
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "weasyprint"])
        print("WeasyPrint installed successfully. Please run the script again.")
    except subprocess.CalledProcessError:
        print("Failed to install WeasyPrint. Please install manually:")
        print("pip install weasyprint")
        print("\nAlternatively, you can use the HTML file directly or convert it using online tools.")
