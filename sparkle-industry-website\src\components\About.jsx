import { useTranslation } from 'react-i18next';

const About = () => {
  const { t } = useTranslation();

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            {t('about_title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('about_content')}
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center">
              <span className="text-gray-500 text-lg">Company Image</span>
            </div>
          </div>
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <div className="bg-blue-100 rounded-full p-3">
                <div className="w-6 h-6 bg-blue-600 rounded-full"></div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Ethical Foundation</h3>
                <p className="text-gray-600">Built on unwavering ethical principles and human-centric values.</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="bg-green-100 rounded-full p-3">
                <div className="w-6 h-6 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Social Impact</h3>
                <p className="text-gray-600">Creating positive impact on society, environment, and employee lives.</p>
              </div>
            </div>
            <div className="flex items-start space-x-4">
              <div className="bg-purple-100 rounded-full p-3">
                <div className="w-6 h-6 bg-purple-600 rounded-full"></div>
              </div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Quality Excellence</h3>
                <p className="text-gray-600">Delivering superior products through ethical production practices.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;

