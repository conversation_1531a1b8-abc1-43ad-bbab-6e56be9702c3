import { useTranslation } from 'react-i18next';
import { GraduationCap, Heart, Shield, Users } from 'lucide-react';

const Charity = () => {
  const { t } = useTranslation();

  const programs = [
    {
      icon: GraduationCap,
      title: t('charity_education_title'),
      content: t('charity_education_content'),
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: Heart,
      title: t('charity_health_title'),
      content: t('charity_health_content'),
      color: 'text-red-600 bg-red-100'
    },
    {
      icon: Shield,
      title: t('charity_protection_title'),
      content: t('charity_protection_content'),
      color: 'text-green-600 bg-green-100'
    }
  ];

  const stats = [
    { number: '2,500+', label: t('charity_impact_children') },
    { number: '50+', label: t('charity_impact_schools') },
    { number: '15', label: t('charity_impact_programs') }
  ];

  return (
    <section id="charity" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('charity_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-6">
            {t('charity_subtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('charity_intro')}
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {programs.map((program, index) => {
            const IconComponent = program.icon;
            return (
              <div key={index} className="text-center">
                <div className={`inline-flex p-4 rounded-full ${program.color} mb-6`}>
                  <IconComponent className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {program.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {program.content}
                </p>
              </div>
            );
          })}
        </div>
        
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2">{t('charity_impact_title')}</h3>
          </div>
          <div className="grid md:grid-cols-3 gap-8 text-center">
            {stats.map((stat, index) => (
              <div key={index}>
                <div className="text-3xl md:text-4xl font-bold mb-2">{stat.number}</div>
                <div className="text-blue-100">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Charity;

