(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))u(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&u(m)}).observe(document,{childList:!0,subtree:!0});function r(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function u(o){if(o.ep)return;o.ep=!0;const d=r(o);fetch(o.href,d)}})();var Tr={exports:{}},Bn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bd;function vp(){if(Bd)return Bn;Bd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function r(u,o,d){var m=null;if(d!==void 0&&(m=""+d),o.key!==void 0&&(m=""+o.key),"key"in o){d={};for(var y in o)y!=="key"&&(d[y]=o[y])}else d=o;return o=d.ref,{$$typeof:c,type:u,key:m,ref:o!==void 0?o:null,props:d}}return Bn.Fragment=s,Bn.jsx=r,Bn.jsxs=r,Bn}var kd;function bp(){return kd||(kd=1,Tr.exports=vp()),Tr.exports}var p=bp(),Ar={exports:{}},ae={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function xp(){if(Gd)return ae;Gd=1;var c=Symbol.for("react.transitional.element"),s=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),N=Symbol.for("react.lazy"),w=Symbol.iterator;function q(x){return x===null||typeof x!="object"?null:(x=w&&x[w]||x["@@iterator"],typeof x=="function"?x:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,Z={};function F(x,R,k){this.props=x,this.context=R,this.refs=Z,this.updater=k||B}F.prototype.isReactComponent={},F.prototype.setState=function(x,R){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,R,"setState")},F.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function be(){}be.prototype=F.prototype;function fe(x,R,k){this.props=x,this.context=R,this.refs=Z,this.updater=k||B}var ue=fe.prototype=new be;ue.constructor=fe,U(ue,F.prototype),ue.isPureReactComponent=!0;var oe=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},xe=Object.prototype.hasOwnProperty;function me(x,R,k,L,Y,le){return k=le.ref,{$$typeof:c,type:x,key:R,ref:k!==void 0?k:null,props:le}}function G(x,R){return me(x.type,R,void 0,void 0,void 0,x.props)}function te(x){return typeof x=="object"&&x!==null&&x.$$typeof===c}function de(x){var R={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(k){return R[k]})}var Re=/\/+/g;function De(x,R){return typeof x=="object"&&x!==null&&x.key!=null?de(""+x.key):R.toString(36)}function qe(){}function Ce(x){switch(x.status){case"fulfilled":return x.value;case"rejected":throw x.reason;default:switch(typeof x.status=="string"?x.then(qe,qe):(x.status="pending",x.then(function(R){x.status==="pending"&&(x.status="fulfilled",x.value=R)},function(R){x.status==="pending"&&(x.status="rejected",x.reason=R)})),x.status){case"fulfilled":return x.value;case"rejected":throw x.reason}}throw x}function we(x,R,k,L,Y){var le=typeof x;(le==="undefined"||le==="boolean")&&(x=null);var I=!1;if(x===null)I=!0;else switch(le){case"bigint":case"string":case"number":I=!0;break;case"object":switch(x.$$typeof){case c:case s:I=!0;break;case N:return I=x._init,we(I(x._payload),R,k,L,Y)}}if(I)return Y=Y(x),I=L===""?"."+De(x,0):L,oe(Y)?(k="",I!=null&&(k=I.replace(Re,"$&/")+"/"),we(Y,R,k,"",function(ot){return ot})):Y!=null&&(te(Y)&&(Y=G(Y,k+(Y.key==null||x&&x.key===Y.key?"":(""+Y.key).replace(Re,"$&/")+"/")+I)),R.push(Y)),1;I=0;var pe=L===""?".":L+":";if(oe(x))for(var Ae=0;Ae<x.length;Ae++)L=x[Ae],le=pe+De(L,Ae),I+=we(L,R,k,le,Y);else if(Ae=q(x),typeof Ae=="function")for(x=Ae.call(x),Ae=0;!(L=x.next()).done;)L=L.value,le=pe+De(L,Ae++),I+=we(L,R,k,le,Y);else if(le==="object"){if(typeof x.then=="function")return we(Ce(x),R,k,L,Y);throw R=String(x),Error("Objects are not valid as a React child (found: "+(R==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":R)+"). If you meant to render a collection of children, use an array instead.")}return I}function z(x,R,k){if(x==null)return x;var L=[],Y=0;return we(x,L,"","",function(le){return R.call(k,le,Y++)}),L}function H(x){if(x._status===-1){var R=x._result;R=R(),R.then(function(k){(x._status===0||x._status===-1)&&(x._status=1,x._result=k)},function(k){(x._status===0||x._status===-1)&&(x._status=2,x._result=k)}),x._status===-1&&(x._status=0,x._result=R)}if(x._status===1)return x._result.default;throw x._result}var C=typeof reportError=="function"?reportError:function(x){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var R=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof x=="object"&&x!==null&&typeof x.message=="string"?String(x.message):String(x),error:x});if(!window.dispatchEvent(R))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",x);return}console.error(x)};function he(){}return ae.Children={map:z,forEach:function(x,R,k){z(x,function(){R.apply(this,arguments)},k)},count:function(x){var R=0;return z(x,function(){R++}),R},toArray:function(x){return z(x,function(R){return R})||[]},only:function(x){if(!te(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},ae.Component=F,ae.Fragment=r,ae.Profiler=o,ae.PureComponent=fe,ae.StrictMode=u,ae.Suspense=v,ae.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,ae.__COMPILER_RUNTIME={__proto__:null,c:function(x){return J.H.useMemoCache(x)}},ae.cache=function(x){return function(){return x.apply(null,arguments)}},ae.cloneElement=function(x,R,k){if(x==null)throw Error("The argument must be a React element, but you passed "+x+".");var L=U({},x.props),Y=x.key,le=void 0;if(R!=null)for(I in R.ref!==void 0&&(le=void 0),R.key!==void 0&&(Y=""+R.key),R)!xe.call(R,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&R.ref===void 0||(L[I]=R[I]);var I=arguments.length-2;if(I===1)L.children=k;else if(1<I){for(var pe=Array(I),Ae=0;Ae<I;Ae++)pe[Ae]=arguments[Ae+2];L.children=pe}return me(x.type,Y,void 0,void 0,le,L)},ae.createContext=function(x){return x={$$typeof:m,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null},x.Provider=x,x.Consumer={$$typeof:d,_context:x},x},ae.createElement=function(x,R,k){var L,Y={},le=null;if(R!=null)for(L in R.key!==void 0&&(le=""+R.key),R)xe.call(R,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&(Y[L]=R[L]);var I=arguments.length-2;if(I===1)Y.children=k;else if(1<I){for(var pe=Array(I),Ae=0;Ae<I;Ae++)pe[Ae]=arguments[Ae+2];Y.children=pe}if(x&&x.defaultProps)for(L in I=x.defaultProps,I)Y[L]===void 0&&(Y[L]=I[L]);return me(x,le,void 0,void 0,null,Y)},ae.createRef=function(){return{current:null}},ae.forwardRef=function(x){return{$$typeof:y,render:x}},ae.isValidElement=te,ae.lazy=function(x){return{$$typeof:N,_payload:{_status:-1,_result:x},_init:H}},ae.memo=function(x,R){return{$$typeof:g,type:x,compare:R===void 0?null:R}},ae.startTransition=function(x){var R=J.T,k={};J.T=k;try{var L=x(),Y=J.S;Y!==null&&Y(k,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(he,C)}catch(le){C(le)}finally{J.T=R}},ae.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},ae.use=function(x){return J.H.use(x)},ae.useActionState=function(x,R,k){return J.H.useActionState(x,R,k)},ae.useCallback=function(x,R){return J.H.useCallback(x,R)},ae.useContext=function(x){return J.H.useContext(x)},ae.useDebugValue=function(){},ae.useDeferredValue=function(x,R){return J.H.useDeferredValue(x,R)},ae.useEffect=function(x,R,k){var L=J.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(x,R)},ae.useId=function(){return J.H.useId()},ae.useImperativeHandle=function(x,R,k){return J.H.useImperativeHandle(x,R,k)},ae.useInsertionEffect=function(x,R){return J.H.useInsertionEffect(x,R)},ae.useLayoutEffect=function(x,R){return J.H.useLayoutEffect(x,R)},ae.useMemo=function(x,R){return J.H.useMemo(x,R)},ae.useOptimistic=function(x,R){return J.H.useOptimistic(x,R)},ae.useReducer=function(x,R,k){return J.H.useReducer(x,R,k)},ae.useRef=function(x){return J.H.useRef(x)},ae.useState=function(x){return J.H.useState(x)},ae.useSyncExternalStore=function(x,R,k){return J.H.useSyncExternalStore(x,R,k)},ae.useTransition=function(){return J.H.useTransition()},ae.version="19.1.1",ae}var Yd;function Yr(){return Yd||(Yd=1,Ar.exports=xp()),Ar.exports}var Ne=Yr(),zr={exports:{}},kn={},wr={exports:{}},jr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function Sp(){return Vd||(Vd=1,function(c){function s(z,H){var C=z.length;z.push(H);e:for(;0<C;){var he=C-1>>>1,x=z[he];if(0<o(x,H))z[he]=H,z[C]=x,C=he;else break e}}function r(z){return z.length===0?null:z[0]}function u(z){if(z.length===0)return null;var H=z[0],C=z.pop();if(C!==H){z[0]=C;e:for(var he=0,x=z.length,R=x>>>1;he<R;){var k=2*(he+1)-1,L=z[k],Y=k+1,le=z[Y];if(0>o(L,C))Y<x&&0>o(le,L)?(z[he]=le,z[Y]=C,he=Y):(z[he]=L,z[k]=C,he=k);else if(Y<x&&0>o(le,C))z[he]=le,z[Y]=C,he=Y;else break e}}return H}function o(z,H){var C=z.sortIndex-H.sortIndex;return C!==0?C:z.id-H.id}if(c.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;c.unstable_now=function(){return d.now()}}else{var m=Date,y=m.now();c.unstable_now=function(){return m.now()-y}}var v=[],g=[],N=1,w=null,q=3,B=!1,U=!1,Z=!1,F=!1,be=typeof setTimeout=="function"?setTimeout:null,fe=typeof clearTimeout=="function"?clearTimeout:null,ue=typeof setImmediate<"u"?setImmediate:null;function oe(z){for(var H=r(g);H!==null;){if(H.callback===null)u(g);else if(H.startTime<=z)u(g),H.sortIndex=H.expirationTime,s(v,H);else break;H=r(g)}}function J(z){if(Z=!1,oe(z),!U)if(r(v)!==null)U=!0,xe||(xe=!0,De());else{var H=r(g);H!==null&&we(J,H.startTime-z)}}var xe=!1,me=-1,G=5,te=-1;function de(){return F?!0:!(c.unstable_now()-te<G)}function Re(){if(F=!1,xe){var z=c.unstable_now();te=z;var H=!0;try{e:{U=!1,Z&&(Z=!1,fe(me),me=-1),B=!0;var C=q;try{t:{for(oe(z),w=r(v);w!==null&&!(w.expirationTime>z&&de());){var he=w.callback;if(typeof he=="function"){w.callback=null,q=w.priorityLevel;var x=he(w.expirationTime<=z);if(z=c.unstable_now(),typeof x=="function"){w.callback=x,oe(z),H=!0;break t}w===r(v)&&u(v),oe(z)}else u(v);w=r(v)}if(w!==null)H=!0;else{var R=r(g);R!==null&&we(J,R.startTime-z),H=!1}}break e}finally{w=null,q=C,B=!1}H=void 0}}finally{H?De():xe=!1}}}var De;if(typeof ue=="function")De=function(){ue(Re)};else if(typeof MessageChannel<"u"){var qe=new MessageChannel,Ce=qe.port2;qe.port1.onmessage=Re,De=function(){Ce.postMessage(null)}}else De=function(){be(Re,0)};function we(z,H){me=be(function(){z(c.unstable_now())},H)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(z){z.callback=null},c.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<z?Math.floor(1e3/z):5},c.unstable_getCurrentPriorityLevel=function(){return q},c.unstable_next=function(z){switch(q){case 1:case 2:case 3:var H=3;break;default:H=q}var C=q;q=H;try{return z()}finally{q=C}},c.unstable_requestPaint=function(){F=!0},c.unstable_runWithPriority=function(z,H){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var C=q;q=z;try{return H()}finally{q=C}},c.unstable_scheduleCallback=function(z,H,C){var he=c.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?he+C:he):C=he,z){case 1:var x=-1;break;case 2:x=250;break;case 5:x=1073741823;break;case 4:x=1e4;break;default:x=5e3}return x=C+x,z={id:N++,callback:H,priorityLevel:z,startTime:C,expirationTime:x,sortIndex:-1},C>he?(z.sortIndex=C,s(g,z),r(v)===null&&z===r(g)&&(Z?(fe(me),me=-1):Z=!0,we(J,C-he))):(z.sortIndex=x,s(v,z),U||B||(U=!0,xe||(xe=!0,De()))),z},c.unstable_shouldYield=de,c.unstable_wrapCallback=function(z){var H=q;return function(){var C=q;q=H;try{return z.apply(this,arguments)}finally{q=C}}}}(jr)),jr}var Xd;function _p(){return Xd||(Xd=1,wr.exports=Sp()),wr.exports}var Rr={exports:{}},at={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function Np(){if(Qd)return at;Qd=1;var c=Yr();function s(v){var g="https://react.dev/errors/"+v;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var N=2;N<arguments.length;N++)g+="&args[]="+encodeURIComponent(arguments[N])}return"Minified React error #"+v+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var u={d:{f:r,r:function(){throw Error(s(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(v,g,N){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:w==null?null:""+w,children:v,containerInfo:g,implementation:N}}var m=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(v,g){if(v==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return at.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,at.createPortal=function(v,g){var N=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(s(299));return d(v,g,null,N)},at.flushSync=function(v){var g=m.T,N=u.p;try{if(m.T=null,u.p=2,v)return v()}finally{m.T=g,u.p=N,u.d.f()}},at.preconnect=function(v,g){typeof v=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,u.d.C(v,g))},at.prefetchDNS=function(v){typeof v=="string"&&u.d.D(v)},at.preinit=function(v,g){if(typeof v=="string"&&g&&typeof g.as=="string"){var N=g.as,w=y(N,g.crossOrigin),q=typeof g.integrity=="string"?g.integrity:void 0,B=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;N==="style"?u.d.S(v,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:w,integrity:q,fetchPriority:B}):N==="script"&&u.d.X(v,{crossOrigin:w,integrity:q,fetchPriority:B,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},at.preinitModule=function(v,g){if(typeof v=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var N=y(g.as,g.crossOrigin);u.d.M(v,{crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&u.d.M(v)},at.preload=function(v,g){if(typeof v=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var N=g.as,w=y(N,g.crossOrigin);u.d.L(v,N,{crossOrigin:w,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},at.preloadModule=function(v,g){if(typeof v=="string")if(g){var N=y(g.as,g.crossOrigin);u.d.m(v,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:N,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else u.d.m(v)},at.requestFormReset=function(v){u.d.r(v)},at.unstable_batchedUpdates=function(v,g){return v(g)},at.useFormState=function(v,g,N){return m.H.useFormState(v,g,N)},at.useFormStatus=function(){return m.H.useHostTransitionStatus()},at.version="19.1.1",at}var Zd;function Op(){if(Zd)return Rr.exports;Zd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Rr.exports=Np(),Rr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kd;function Ep(){if(Kd)return kn;Kd=1;var c=_p(),s=Yr(),r=Op();function u(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function y(e){if(d(e)!==e)throw Error(u(188))}function v(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(u(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var i=n.alternate;if(i===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===a)return y(n),e;if(i===l)return y(n),t;i=i.sibling}throw Error(u(188))}if(a.return!==l.return)a=n,l=i;else{for(var f=!1,h=n.child;h;){if(h===a){f=!0,a=n,l=i;break}if(h===l){f=!0,l=n,a=i;break}h=h.sibling}if(!f){for(h=i.child;h;){if(h===a){f=!0,a=i,l=n;break}if(h===l){f=!0,l=i,a=n;break}h=h.sibling}if(!f)throw Error(u(189))}}if(a.alternate!==l)throw Error(u(190))}if(a.tag!==3)throw Error(u(188));return a.stateNode.current===a?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var N=Object.assign,w=Symbol.for("react.element"),q=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),Z=Symbol.for("react.strict_mode"),F=Symbol.for("react.profiler"),be=Symbol.for("react.provider"),fe=Symbol.for("react.consumer"),ue=Symbol.for("react.context"),oe=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),xe=Symbol.for("react.suspense_list"),me=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),te=Symbol.for("react.activity"),de=Symbol.for("react.memo_cache_sentinel"),Re=Symbol.iterator;function De(e){return e===null||typeof e!="object"?null:(e=Re&&e[Re]||e["@@iterator"],typeof e=="function"?e:null)}var qe=Symbol.for("react.client.reference");function Ce(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===qe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case F:return"Profiler";case Z:return"StrictMode";case J:return"Suspense";case xe:return"SuspenseList";case te:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case B:return"Portal";case ue:return(e.displayName||"Context")+".Provider";case fe:return(e._context.displayName||"Context")+".Consumer";case oe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case me:return t=e.displayName||null,t!==null?t:Ce(e.type)||"Memo";case G:t=e._payload,e=e._init;try{return Ce(e(t))}catch{}}return null}var we=Array.isArray,z=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C={pending:!1,data:null,method:null,action:null},he=[],x=-1;function R(e){return{current:e}}function k(e){0>x||(e.current=he[x],he[x]=null,x--)}function L(e,t){x++,he[x]=e.current,e.current=t}var Y=R(null),le=R(null),I=R(null),pe=R(null);function Ae(e,t){switch(L(I,t),L(le,e),L(Y,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?hd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=hd(t),e=gd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}k(Y),L(Y,e)}function ot(){k(Y),k(le),k(I)}function aa(e){e.memoizedState!==null&&L(pe,e);var t=Y.current,a=gd(t,e.type);t!==a&&(L(le,e),L(Y,a))}function la(e){le.current===e&&(k(Y),k(le)),pe.current===e&&(k(pe),Cn._currentValue=C)}var na=Object.prototype.hasOwnProperty,hs=c.unstable_scheduleCallback,gs=c.unstable_cancelCallback,Wh=c.unstable_shouldYield,Fh=c.unstable_requestPaint,Mt=c.unstable_now,Ph=c.unstable_getCurrentPriorityLevel,Qr=c.unstable_ImmediatePriority,Zr=c.unstable_UserBlockingPriority,Zn=c.unstable_NormalPriority,Ih=c.unstable_LowPriority,Kr=c.unstable_IdlePriority,eg=c.log,tg=c.unstable_setDisableYieldValue,Gl=null,ft=null;function ia(e){if(typeof eg=="function"&&tg(e),ft&&typeof ft.setStrictMode=="function")try{ft.setStrictMode(Gl,e)}catch{}}var dt=Math.clz32?Math.clz32:ng,ag=Math.log,lg=Math.LN2;function ng(e){return e>>>=0,e===0?32:31-(ag(e)/lg|0)|0}var Kn=256,Jn=4194304;function ja(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function $n(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,i=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var h=l&134217727;return h!==0?(l=h&~i,l!==0?n=ja(l):(f&=h,f!==0?n=ja(f):a||(a=h&~e,a!==0&&(n=ja(a))))):(h=l&~i,h!==0?n=ja(h):f!==0?n=ja(f):a||(a=l&~e,a!==0&&(n=ja(a)))),n===0?0:t!==0&&t!==n&&(t&i)===0&&(i=n&-n,a=t&-t,i>=a||i===32&&(a&4194048)!==0)?t:n}function Yl(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function ig(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jr(){var e=Kn;return Kn<<=1,(Kn&4194048)===0&&(Kn=256),e}function $r(){var e=Jn;return Jn<<=1,(Jn&62914560)===0&&(Jn=4194304),e}function ms(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Vl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function sg(e,t,a,l,n,i){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var h=e.entanglements,b=e.expirationTimes,E=e.hiddenUpdates;for(a=f&~a;0<a;){var j=31-dt(a),D=1<<j;h[j]=0,b[j]=-1;var T=E[j];if(T!==null)for(E[j]=null,j=0;j<T.length;j++){var A=T[j];A!==null&&(A.lane&=-536870913)}a&=~D}l!==0&&Wr(e,l,0),i!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=i&~(f&~t))}function Wr(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-dt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Fr(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-dt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function ps(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function ys(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Pr(){var e=H.p;return e!==0?e:(e=window.event,e===void 0?32:Dd(e.type))}function ug(e,t){var a=H.p;try{return H.p=e,t()}finally{H.p=a}}var sa=Math.random().toString(36).slice(2),et="__reactFiber$"+sa,nt="__reactProps$"+sa,Wa="__reactContainer$"+sa,vs="__reactEvents$"+sa,rg="__reactListeners$"+sa,cg="__reactHandles$"+sa,Ir="__reactResources$"+sa,Xl="__reactMarker$"+sa;function bs(e){delete e[et],delete e[nt],delete e[vs],delete e[rg],delete e[cg]}function Fa(e){var t=e[et];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Wa]||a[et]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=vd(e);e!==null;){if(a=e[et])return a;e=vd(e)}return t}e=a,a=e.parentNode}return null}function Pa(e){if(e=e[et]||e[Wa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ql(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(u(33))}function Ia(e){var t=e[Ir];return t||(t=e[Ir]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ze(e){e[Xl]=!0}var ec=new Set,tc={};function Ra(e,t){el(e,t),el(e+"Capture",t)}function el(e,t){for(tc[e]=t,e=0;e<t.length;e++)ec.add(t[e])}var og=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ac={},lc={};function fg(e){return na.call(lc,e)?!0:na.call(ac,e)?!1:og.test(e)?lc[e]=!0:(ac[e]=!0,!1)}function Wn(e,t,a){if(fg(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Fn(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function kt(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var xs,nc;function tl(e){if(xs===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);xs=t&&t[1]||"",nc=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xs+e+nc}var Ss=!1;function _s(e,t){if(!e||Ss)return"";Ss=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(A){var T=A}Reflect.construct(e,[],D)}else{try{D.call()}catch(A){T=A}e.call(D.prototype)}}else{try{throw Error()}catch(A){T=A}(D=e())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(A){if(A&&T&&typeof A.stack=="string")return[A.stack,T.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=l.DetermineComponentFrameRoot(),f=i[0],h=i[1];if(f&&h){var b=f.split(`
`),E=h.split(`
`);for(n=l=0;l<b.length&&!b[l].includes("DetermineComponentFrameRoot");)l++;for(;n<E.length&&!E[n].includes("DetermineComponentFrameRoot");)n++;if(l===b.length||n===E.length)for(l=b.length-1,n=E.length-1;1<=l&&0<=n&&b[l]!==E[n];)n--;for(;1<=l&&0<=n;l--,n--)if(b[l]!==E[n]){if(l!==1||n!==1)do if(l--,n--,0>n||b[l]!==E[n]){var j=`
`+b[l].replace(" at new "," at ");return e.displayName&&j.includes("<anonymous>")&&(j=j.replace("<anonymous>",e.displayName)),j}while(1<=l&&0<=n);break}}}finally{Ss=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?tl(a):""}function dg(e){switch(e.tag){case 26:case 27:case 5:return tl(e.type);case 16:return tl("Lazy");case 13:return tl("Suspense");case 19:return tl("SuspenseList");case 0:case 15:return _s(e.type,!1);case 11:return _s(e.type.render,!1);case 1:return _s(e.type,!0);case 31:return tl("Activity");default:return""}}function ic(e){try{var t="";do t+=dg(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function xt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function sc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function hg(e){var t=sc(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,i=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,i.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pn(e){e._valueTracker||(e._valueTracker=hg(e))}function uc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=sc(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function In(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var gg=/[\n"\\]/g;function St(e){return e.replace(gg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Ns(e,t,a,l,n,i,f,h){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+xt(t)):e.value!==""+xt(t)&&(e.value=""+xt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Os(e,f,xt(t)):a!=null?Os(e,f,xt(a)):l!=null&&e.removeAttribute("value"),n==null&&i!=null&&(e.defaultChecked=!!i),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+xt(h):e.removeAttribute("name")}function rc(e,t,a,l,n,i,f,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||a!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;a=a!=null?""+xt(a):"",t=t!=null?""+xt(t):a,h||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=h?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Os(e,t,a){t==="number"&&In(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function al(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+xt(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function cc(e,t,a){if(t!=null&&(t=""+xt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+xt(a):""}function oc(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(u(92));if(we(l)){if(1<l.length)throw Error(u(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=xt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function ll(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var mg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function fc(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||mg.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function dc(e,t,a){if(t!=null&&typeof t!="object")throw Error(u(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&fc(e,n,l)}else for(var i in t)t.hasOwnProperty(i)&&fc(e,i,t[i])}function Es(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var pg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),yg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ei(e){return yg.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ts=null;function As(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var nl=null,il=null;function hc(e){var t=Pa(e);if(t&&(e=t.stateNode)){var a=e[nt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Ns(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+St(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[nt]||null;if(!n)throw Error(u(90));Ns(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&uc(l)}break e;case"textarea":cc(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&al(e,!!a.multiple,t,!1)}}}var zs=!1;function gc(e,t,a){if(zs)return e(t,a);zs=!0;try{var l=e(t);return l}finally{if(zs=!1,(nl!==null||il!==null)&&(Bi(),nl&&(t=nl,e=il,il=nl=null,hc(t),e)))for(t=0;t<e.length;t++)hc(e[t])}}function Zl(e,t){var a=e.stateNode;if(a===null)return null;var l=a[nt]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(u(231,t,typeof a));return a}var Gt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ws=!1;if(Gt)try{var Kl={};Object.defineProperty(Kl,"passive",{get:function(){ws=!0}}),window.addEventListener("test",Kl,Kl),window.removeEventListener("test",Kl,Kl)}catch{ws=!1}var ua=null,js=null,ti=null;function mc(){if(ti)return ti;var e,t=js,a=t.length,l,n="value"in ua?ua.value:ua.textContent,i=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[i-l];l++);return ti=n.slice(e,1<l?1-l:void 0)}function ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function li(){return!0}function pc(){return!1}function it(e){function t(a,l,n,i,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(a=e[h],this[h]=a?a(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?li:pc,this.isPropagationStopped=pc,this}return N(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=li)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=li)},persist:function(){},isPersistent:li}),t}var Ma={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ni=it(Ma),Jl=N({},Ma,{view:0,detail:0}),vg=it(Jl),Rs,Ms,$l,ii=N({},Jl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$l&&($l&&e.type==="mousemove"?(Rs=e.screenX-$l.screenX,Ms=e.screenY-$l.screenY):Ms=Rs=0,$l=e),Rs)},movementY:function(e){return"movementY"in e?e.movementY:Ms}}),yc=it(ii),bg=N({},ii,{dataTransfer:0}),xg=it(bg),Sg=N({},Jl,{relatedTarget:0}),Ds=it(Sg),_g=N({},Ma,{animationName:0,elapsedTime:0,pseudoElement:0}),Ng=it(_g),Og=N({},Ma,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Eg=it(Og),Tg=N({},Ma,{data:0}),vc=it(Tg),Ag={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},wg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=wg[e])?!!t[e]:!1}function Cs(){return jg}var Rg=N({},Jl,{key:function(e){if(e.key){var t=Ag[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?zg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cs,charCode:function(e){return e.type==="keypress"?ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Mg=it(Rg),Dg=N({},ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bc=it(Dg),Cg=N({},Jl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cs}),Ug=it(Cg),Lg=N({},Ma,{propertyName:0,elapsedTime:0,pseudoElement:0}),Hg=it(Lg),qg=N({},ii,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Bg=it(qg),kg=N({},Ma,{newState:0,oldState:0}),Gg=it(kg),Yg=[9,13,27,32],Us=Gt&&"CompositionEvent"in window,Wl=null;Gt&&"documentMode"in document&&(Wl=document.documentMode);var Vg=Gt&&"TextEvent"in window&&!Wl,xc=Gt&&(!Us||Wl&&8<Wl&&11>=Wl),Sc=" ",_c=!1;function Nc(e,t){switch(e){case"keyup":return Yg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Oc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var sl=!1;function Xg(e,t){switch(e){case"compositionend":return Oc(t);case"keypress":return t.which!==32?null:(_c=!0,Sc);case"textInput":return e=t.data,e===Sc&&_c?null:e;default:return null}}function Qg(e,t){if(sl)return e==="compositionend"||!Us&&Nc(e,t)?(e=mc(),ti=js=ua=null,sl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return xc&&t.locale!=="ko"?null:t.data;default:return null}}var Zg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ec(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Zg[e.type]:t==="textarea"}function Tc(e,t,a,l){nl?il?il.push(l):il=[l]:nl=l,t=Qi(t,"onChange"),0<t.length&&(a=new ni("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var Fl=null,Pl=null;function Kg(e){rd(e,0)}function si(e){var t=Ql(e);if(uc(t))return e}function Ac(e,t){if(e==="change")return t}var zc=!1;if(Gt){var Ls;if(Gt){var Hs="oninput"in document;if(!Hs){var wc=document.createElement("div");wc.setAttribute("oninput","return;"),Hs=typeof wc.oninput=="function"}Ls=Hs}else Ls=!1;zc=Ls&&(!document.documentMode||9<document.documentMode)}function jc(){Fl&&(Fl.detachEvent("onpropertychange",Rc),Pl=Fl=null)}function Rc(e){if(e.propertyName==="value"&&si(Pl)){var t=[];Tc(t,Pl,e,As(e)),gc(Kg,t)}}function Jg(e,t,a){e==="focusin"?(jc(),Fl=t,Pl=a,Fl.attachEvent("onpropertychange",Rc)):e==="focusout"&&jc()}function $g(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return si(Pl)}function Wg(e,t){if(e==="click")return si(t)}function Fg(e,t){if(e==="input"||e==="change")return si(t)}function Pg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ht=typeof Object.is=="function"?Object.is:Pg;function Il(e,t){if(ht(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!na.call(t,n)||!ht(e[n],t[n]))return!1}return!0}function Mc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Dc(e,t){var a=Mc(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Mc(a)}}function Cc(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Cc(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uc(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=In(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=In(e.document)}return t}function qs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Ig=Gt&&"documentMode"in document&&11>=document.documentMode,ul=null,Bs=null,en=null,ks=!1;function Lc(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;ks||ul==null||ul!==In(l)||(l=ul,"selectionStart"in l&&qs(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),en&&Il(en,l)||(en=l,l=Qi(Bs,"onSelect"),0<l.length&&(t=new ni("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=ul)))}function Da(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var rl={animationend:Da("Animation","AnimationEnd"),animationiteration:Da("Animation","AnimationIteration"),animationstart:Da("Animation","AnimationStart"),transitionrun:Da("Transition","TransitionRun"),transitionstart:Da("Transition","TransitionStart"),transitioncancel:Da("Transition","TransitionCancel"),transitionend:Da("Transition","TransitionEnd")},Gs={},Hc={};Gt&&(Hc=document.createElement("div").style,"AnimationEvent"in window||(delete rl.animationend.animation,delete rl.animationiteration.animation,delete rl.animationstart.animation),"TransitionEvent"in window||delete rl.transitionend.transition);function Ca(e){if(Gs[e])return Gs[e];if(!rl[e])return e;var t=rl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Hc)return Gs[e]=t[a];return e}var qc=Ca("animationend"),Bc=Ca("animationiteration"),kc=Ca("animationstart"),em=Ca("transitionrun"),tm=Ca("transitionstart"),am=Ca("transitioncancel"),Gc=Ca("transitionend"),Yc=new Map,Ys="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ys.push("scrollEnd");function wt(e,t){Yc.set(e,t),Ra(t,[e])}var Vc=new WeakMap;function _t(e,t){if(typeof e=="object"&&e!==null){var a=Vc.get(e);return a!==void 0?a:(t={value:e,source:t,stack:ic(t)},Vc.set(e,t),t)}return{value:e,source:t,stack:ic(t)}}var Nt=[],cl=0,Vs=0;function ui(){for(var e=cl,t=Vs=cl=0;t<e;){var a=Nt[t];Nt[t++]=null;var l=Nt[t];Nt[t++]=null;var n=Nt[t];Nt[t++]=null;var i=Nt[t];if(Nt[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}i!==0&&Xc(a,n,i)}}function ri(e,t,a,l){Nt[cl++]=e,Nt[cl++]=t,Nt[cl++]=a,Nt[cl++]=l,Vs|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Xs(e,t,a,l){return ri(e,t,a,l),ci(e)}function ol(e,t){return ri(e,null,null,t),ci(e)}function Xc(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,i=e.return;i!==null;)i.childLanes|=a,l=i.alternate,l!==null&&(l.childLanes|=a),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(n=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,n&&t!==null&&(n=31-dt(a),e=i.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),i):null}function ci(e){if(50<Tn)throw Tn=0,Wu=null,Error(u(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var fl={};function lm(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function gt(e,t,a,l){return new lm(e,t,a,l)}function Qs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Yt(e,t){var a=e.alternate;return a===null?(a=gt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Qc(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function oi(e,t,a,l,n,i){var f=0;if(l=e,typeof e=="function")Qs(e)&&(f=1);else if(typeof e=="string")f=ip(e,a,Y.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case te:return e=gt(31,a,t,n),e.elementType=te,e.lanes=i,e;case U:return Ua(a.children,n,i,t);case Z:f=8,n|=24;break;case F:return e=gt(12,a,t,n|2),e.elementType=F,e.lanes=i,e;case J:return e=gt(13,a,t,n),e.elementType=J,e.lanes=i,e;case xe:return e=gt(19,a,t,n),e.elementType=xe,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case be:case ue:f=10;break e;case fe:f=9;break e;case oe:f=11;break e;case me:f=14;break e;case G:f=16,l=null;break e}f=29,a=Error(u(130,e===null?"null":typeof e,"")),l=null}return t=gt(f,a,t,n),t.elementType=e,t.type=l,t.lanes=i,t}function Ua(e,t,a,l){return e=gt(7,e,l,t),e.lanes=a,e}function Zs(e,t,a){return e=gt(6,e,null,t),e.lanes=a,e}function Ks(e,t,a){return t=gt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var dl=[],hl=0,fi=null,di=0,Ot=[],Et=0,La=null,Vt=1,Xt="";function Ha(e,t){dl[hl++]=di,dl[hl++]=fi,fi=e,di=t}function Zc(e,t,a){Ot[Et++]=Vt,Ot[Et++]=Xt,Ot[Et++]=La,La=e;var l=Vt;e=Xt;var n=32-dt(l)-1;l&=~(1<<n),a+=1;var i=32-dt(t)+n;if(30<i){var f=n-n%5;i=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Vt=1<<32-dt(t)+n|a<<n|l,Xt=i+e}else Vt=1<<i|a<<n|l,Xt=e}function Js(e){e.return!==null&&(Ha(e,1),Zc(e,1,0))}function $s(e){for(;e===fi;)fi=dl[--hl],dl[hl]=null,di=dl[--hl],dl[hl]=null;for(;e===La;)La=Ot[--Et],Ot[Et]=null,Xt=Ot[--Et],Ot[Et]=null,Vt=Ot[--Et],Ot[Et]=null}var lt=null,Le=null,ve=!1,qa=null,Dt=!1,Ws=Error(u(519));function Ba(e){var t=Error(u(418,""));throw ln(_t(t,e)),Ws}function Kc(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[et]=e,t[nt]=l,a){case"dialog":ce("cancel",t),ce("close",t);break;case"iframe":case"object":case"embed":ce("load",t);break;case"video":case"audio":for(a=0;a<zn.length;a++)ce(zn[a],t);break;case"source":ce("error",t);break;case"img":case"image":case"link":ce("error",t),ce("load",t);break;case"details":ce("toggle",t);break;case"input":ce("invalid",t),rc(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Pn(t);break;case"select":ce("invalid",t);break;case"textarea":ce("invalid",t),oc(t,l.value,l.defaultValue,l.children),Pn(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||dd(t.textContent,a)?(l.popover!=null&&(ce("beforetoggle",t),ce("toggle",t)),l.onScroll!=null&&ce("scroll",t),l.onScrollEnd!=null&&ce("scrollend",t),l.onClick!=null&&(t.onclick=Zi),t=!0):t=!1,t||Ba(e)}function Jc(e){for(lt=e.return;lt;)switch(lt.tag){case 5:case 13:Dt=!1;return;case 27:case 3:Dt=!0;return;default:lt=lt.return}}function tn(e){if(e!==lt)return!1;if(!ve)return Jc(e),ve=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||dr(e.type,e.memoizedProps)),a=!a),a&&Le&&Ba(e),Jc(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(u(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Le=Rt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Le=null}}else t===27?(t=Le,Na(e.type)?(e=pr,pr=null,Le=e):Le=t):Le=lt?Rt(e.stateNode.nextSibling):null;return!0}function an(){Le=lt=null,ve=!1}function $c(){var e=qa;return e!==null&&(rt===null?rt=e:rt.push.apply(rt,e),qa=null),e}function ln(e){qa===null?qa=[e]:qa.push(e)}var Fs=R(null),ka=null,Qt=null;function ra(e,t,a){L(Fs,t._currentValue),t._currentValue=a}function Zt(e){e._currentValue=Fs.current,k(Fs)}function Ps(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Is(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var i=n.dependencies;if(i!==null){var f=n.child;i=i.firstContext;e:for(;i!==null;){var h=i;i=n;for(var b=0;b<t.length;b++)if(h.context===t[b]){i.lanes|=a,h=i.alternate,h!==null&&(h.lanes|=a),Ps(i.return,a,e),l||(f=null);break e}i=h.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(u(341));f.lanes|=a,i=f.alternate,i!==null&&(i.lanes|=a),Ps(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function nn(e,t,a,l){e=null;for(var n=t,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(u(387));if(f=f.memoizedProps,f!==null){var h=n.type;ht(n.pendingProps.value,f.value)||(e!==null?e.push(h):e=[h])}}else if(n===pe.current){if(f=n.alternate,f===null)throw Error(u(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Cn):e=[Cn])}n=n.return}e!==null&&Is(t,e,a,l),t.flags|=262144}function hi(e){for(e=e.firstContext;e!==null;){if(!ht(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ga(e){ka=e,Qt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function tt(e){return Wc(ka,e)}function gi(e,t){return ka===null&&Ga(e),Wc(e,t)}function Wc(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Qt===null){if(e===null)throw Error(u(308));Qt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Qt=Qt.next=t;return a}var nm=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},im=c.unstable_scheduleCallback,sm=c.unstable_NormalPriority,Ve={$$typeof:ue,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function eu(){return{controller:new nm,data:new Map,refCount:0}}function sn(e){e.refCount--,e.refCount===0&&im(sm,function(){e.controller.abort()})}var un=null,tu=0,gl=0,ml=null;function um(e,t){if(un===null){var a=un=[];tu=0,gl=lr(),ml={status:"pending",value:void 0,then:function(l){a.push(l)}}}return tu++,t.then(Fc,Fc),t}function Fc(){if(--tu===0&&un!==null){ml!==null&&(ml.status="fulfilled");var e=un;un=null,gl=0,ml=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function rm(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Pc=z.S;z.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&um(e,t),Pc!==null&&Pc(e,t)};var Ya=R(null);function au(){var e=Ya.current;return e!==null?e:je.pooledCache}function mi(e,t){t===null?L(Ya,Ya.current):L(Ya,t.pool)}function Ic(){var e=au();return e===null?null:{parent:Ve._currentValue,pool:e}}var rn=Error(u(460)),eo=Error(u(474)),pi=Error(u(542)),lu={then:function(){}};function to(e){return e=e.status,e==="fulfilled"||e==="rejected"}function yi(){}function ao(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(yi,yi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,no(e),e;default:if(typeof t.status=="string")t.then(yi,yi);else{if(e=je,e!==null&&100<e.shellSuspendCounter)throw Error(u(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,no(e),e}throw cn=t,rn}}var cn=null;function lo(){if(cn===null)throw Error(u(459));var e=cn;return cn=null,e}function no(e){if(e===rn||e===pi)throw Error(u(483))}var ca=!1;function nu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function iu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function oa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function fa(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Se&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=ci(e),Xc(e,null,a),t}return ri(e,l,t,a),ci(e)}function on(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Fr(e,a)}}function su(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,i=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};i===null?n=i=f:i=i.next=f,a=a.next}while(a!==null);i===null?n=i=t:i=i.next=t}else n=i=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var uu=!1;function fn(){if(uu){var e=ml;if(e!==null)throw e}}function dn(e,t,a,l){uu=!1;var n=e.updateQueue;ca=!1;var i=n.firstBaseUpdate,f=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var b=h,E=b.next;b.next=null,f===null?i=E:f.next=E,f=b;var j=e.alternate;j!==null&&(j=j.updateQueue,h=j.lastBaseUpdate,h!==f&&(h===null?j.firstBaseUpdate=E:h.next=E,j.lastBaseUpdate=b))}if(i!==null){var D=n.baseState;f=0,j=E=b=null,h=i;do{var T=h.lane&-536870913,A=T!==h.lane;if(A?(ge&T)===T:(l&T)===T){T!==0&&T===gl&&(uu=!0),j!==null&&(j=j.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var P=e,$=h;T=t;var Te=a;switch($.tag){case 1:if(P=$.payload,typeof P=="function"){D=P.call(Te,D,T);break e}D=P;break e;case 3:P.flags=P.flags&-65537|128;case 0:if(P=$.payload,T=typeof P=="function"?P.call(Te,D,T):P,T==null)break e;D=N({},D,T);break e;case 2:ca=!0}}T=h.callback,T!==null&&(e.flags|=64,A&&(e.flags|=8192),A=n.callbacks,A===null?n.callbacks=[T]:A.push(T))}else A={lane:T,tag:h.tag,payload:h.payload,callback:h.callback,next:null},j===null?(E=j=A,b=D):j=j.next=A,f|=T;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;A=h,h=A.next,A.next=null,n.lastBaseUpdate=A,n.shared.pending=null}}while(!0);j===null&&(b=D),n.baseState=b,n.firstBaseUpdate=E,n.lastBaseUpdate=j,i===null&&(n.shared.lanes=0),ba|=f,e.lanes=f,e.memoizedState=D}}function io(e,t){if(typeof e!="function")throw Error(u(191,e));e.call(t)}function so(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)io(a[e],t)}var pl=R(null),vi=R(0);function uo(e,t){e=It,L(vi,e),L(pl,t),It=e|t.baseLanes}function ru(){L(vi,It),L(pl,pl.current)}function cu(){It=vi.current,k(pl),k(vi)}var da=0,ne=null,Oe=null,Ge=null,bi=!1,yl=!1,Va=!1,xi=0,hn=0,vl=null,cm=0;function Be(){throw Error(u(321))}function ou(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!ht(e[a],t[a]))return!1;return!0}function fu(e,t,a,l,n,i){return da=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=e===null||e.memoizedState===null?Qo:Zo,Va=!1,i=a(l,n),Va=!1,yl&&(i=co(t,a,l,n)),ro(e),i}function ro(e){z.H=Ti;var t=Oe!==null&&Oe.next!==null;if(da=0,Ge=Oe=ne=null,bi=!1,hn=0,vl=null,t)throw Error(u(300));e===null||Ke||(e=e.dependencies,e!==null&&hi(e)&&(Ke=!0))}function co(e,t,a,l){ne=e;var n=0;do{if(yl&&(vl=null),hn=0,yl=!1,25<=n)throw Error(u(301));if(n+=1,Ge=Oe=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}z.H=pm,i=t(a,l)}while(yl);return i}function om(){var e=z.H,t=e.useState()[0];return t=typeof t.then=="function"?gn(t):t,e=e.useState()[0],(Oe!==null?Oe.memoizedState:null)!==e&&(ne.flags|=1024),t}function du(){var e=xi!==0;return xi=0,e}function hu(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function gu(e){if(bi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}bi=!1}da=0,Ge=Oe=ne=null,yl=!1,hn=xi=0,vl=null}function st(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?ne.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Ye(){if(Oe===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var t=Ge===null?ne.memoizedState:Ge.next;if(t!==null)Ge=t,Oe=e;else{if(e===null)throw ne.alternate===null?Error(u(467)):Error(u(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},Ge===null?ne.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function mu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function gn(e){var t=hn;return hn+=1,vl===null&&(vl=[]),e=ao(vl,e,t),t=ne,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,z.H=t===null||t.memoizedState===null?Qo:Zo),e}function Si(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return gn(e);if(e.$$typeof===ue)return tt(e)}throw Error(u(438,String(e)))}function pu(e){var t=null,a=ne.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ne.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=mu(),ne.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=de;return t.index++,a}function Kt(e,t){return typeof t=="function"?t(e):t}function _i(e){var t=Ye();return yu(t,Oe,e)}function yu(e,t,a){var l=e.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=a;var n=e.baseQueue,i=l.pending;if(i!==null){if(n!==null){var f=n.next;n.next=i.next,i.next=f}t.baseQueue=n=i,l.pending=null}if(i=e.baseState,n===null)e.memoizedState=i;else{t=n.next;var h=f=null,b=null,E=t,j=!1;do{var D=E.lane&-536870913;if(D!==E.lane?(ge&D)===D:(da&D)===D){var T=E.revertLane;if(T===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),D===gl&&(j=!0);else if((da&T)===T){E=E.next,T===gl&&(j=!0);continue}else D={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},b===null?(h=b=D,f=i):b=b.next=D,ne.lanes|=T,ba|=T;D=E.action,Va&&a(i,D),i=E.hasEagerState?E.eagerState:a(i,D)}else T={lane:D,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},b===null?(h=b=T,f=i):b=b.next=T,ne.lanes|=D,ba|=D;E=E.next}while(E!==null&&E!==t);if(b===null?f=i:b.next=h,!ht(i,e.memoizedState)&&(Ke=!0,j&&(a=ml,a!==null)))throw a;e.memoizedState=i,e.baseState=f,e.baseQueue=b,l.lastRenderedState=i}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function vu(e){var t=Ye(),a=t.queue;if(a===null)throw Error(u(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,i=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do i=e(i,f.action),f=f.next;while(f!==n);ht(i,t.memoizedState)||(Ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),a.lastRenderedState=i}return[i,l]}function oo(e,t,a){var l=ne,n=Ye(),i=ve;if(i){if(a===void 0)throw Error(u(407));a=a()}else a=t();var f=!ht((Oe||n).memoizedState,a);f&&(n.memoizedState=a,Ke=!0),n=n.queue;var h=go.bind(null,l,n,e);if(mn(2048,8,h,[e]),n.getSnapshot!==t||f||Ge!==null&&Ge.memoizedState.tag&1){if(l.flags|=2048,bl(9,Ni(),ho.bind(null,l,n,a,t),null),je===null)throw Error(u(349));i||(da&124)!==0||fo(l,t,a)}return a}function fo(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ne.updateQueue,t===null?(t=mu(),ne.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function ho(e,t,a,l){t.value=a,t.getSnapshot=l,mo(t)&&po(e)}function go(e,t,a){return a(function(){mo(t)&&po(e)})}function mo(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!ht(e,a)}catch{return!0}}function po(e){var t=ol(e,2);t!==null&&bt(t,e,2)}function bu(e){var t=st();if(typeof e=="function"){var a=e;if(e=a(),Va){ia(!0);try{a()}finally{ia(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Kt,lastRenderedState:e},t}function yo(e,t,a,l){return e.baseState=a,yu(e,Oe,typeof l=="function"?l:Kt)}function fm(e,t,a,l,n){if(Ei(e))throw Error(u(485));if(e=t.action,e!==null){var i={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};z.T!==null?a(!0):i.isTransition=!1,l(i),a=t.pending,a===null?(i.next=t.pending=i,vo(t,i)):(i.next=a.next,t.pending=a.next=i)}}function vo(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var i=z.T,f={};z.T=f;try{var h=a(n,l),b=z.S;b!==null&&b(f,h),bo(e,t,h)}catch(E){xu(e,t,E)}finally{z.T=i}}else try{i=a(n,l),bo(e,t,i)}catch(E){xu(e,t,E)}}function bo(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){xo(e,t,l)},function(l){return xu(e,t,l)}):xo(e,t,a)}function xo(e,t,a){t.status="fulfilled",t.value=a,So(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,vo(e,a)))}function xu(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,So(t),t=t.next;while(t!==l)}e.action=null}function So(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function _o(e,t){return t}function No(e,t){if(ve){var a=je.formState;if(a!==null){e:{var l=ne;if(ve){if(Le){t:{for(var n=Le,i=Dt;n.nodeType!==8;){if(!i){n=null;break t}if(n=Rt(n.nextSibling),n===null){n=null;break t}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Le=Rt(n.nextSibling),l=n.data==="F!";break e}}Ba(l)}l=!1}l&&(t=a[0])}}return a=st(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:_o,lastRenderedState:t},a.queue=l,a=Yo.bind(null,ne,l),l.dispatch=a,l=bu(!1),i=Eu.bind(null,ne,!1,l.queue),l=st(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=fm.bind(null,ne,n,i,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function Oo(e){var t=Ye();return Eo(t,Oe,e)}function Eo(e,t,a){if(t=yu(e,t,_o)[0],e=_i(Kt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=gn(t)}catch(f){throw f===rn?pi:f}else l=t;t=Ye();var n=t.queue,i=n.dispatch;return a!==t.memoizedState&&(ne.flags|=2048,bl(9,Ni(),dm.bind(null,n,a),null)),[l,i,e]}function dm(e,t){e.action=t}function To(e){var t=Ye(),a=Oe;if(a!==null)return Eo(t,a,e);Ye(),t=t.memoizedState,a=Ye();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function bl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ne.updateQueue,t===null&&(t=mu(),ne.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Ni(){return{destroy:void 0,resource:void 0}}function Ao(){return Ye().memoizedState}function Oi(e,t,a,l){var n=st();l=l===void 0?null:l,ne.flags|=e,n.memoizedState=bl(1|t,Ni(),a,l)}function mn(e,t,a,l){var n=Ye();l=l===void 0?null:l;var i=n.memoizedState.inst;Oe!==null&&l!==null&&ou(l,Oe.memoizedState.deps)?n.memoizedState=bl(t,i,a,l):(ne.flags|=e,n.memoizedState=bl(1|t,i,a,l))}function zo(e,t){Oi(8390656,8,e,t)}function wo(e,t){mn(2048,8,e,t)}function jo(e,t){return mn(4,2,e,t)}function Ro(e,t){return mn(4,4,e,t)}function Mo(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Do(e,t,a){a=a!=null?a.concat([e]):null,mn(4,4,Mo.bind(null,t,e),a)}function Su(){}function Co(e,t){var a=Ye();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&ou(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Uo(e,t){var a=Ye();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&ou(t,l[1]))return l[0];if(l=e(),Va){ia(!0);try{e()}finally{ia(!1)}}return a.memoizedState=[l,t],l}function _u(e,t,a){return a===void 0||(da&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Bf(),ne.lanes|=e,ba|=e,a)}function Lo(e,t,a,l){return ht(a,t)?a:pl.current!==null?(e=_u(e,a,l),ht(e,t)||(Ke=!0),e):(da&42)===0?(Ke=!0,e.memoizedState=a):(e=Bf(),ne.lanes|=e,ba|=e,t)}function Ho(e,t,a,l,n){var i=H.p;H.p=i!==0&&8>i?i:8;var f=z.T,h={};z.T=h,Eu(e,!1,t,a);try{var b=n(),E=z.S;if(E!==null&&E(h,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var j=rm(b,l);pn(e,t,j,vt(e))}else pn(e,t,l,vt(e))}catch(D){pn(e,t,{then:function(){},status:"rejected",reason:D},vt())}finally{H.p=i,z.T=f}}function hm(){}function Nu(e,t,a,l){if(e.tag!==5)throw Error(u(476));var n=qo(e).queue;Ho(e,n,t,C,a===null?hm:function(){return Bo(e),a(l)})}function qo(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:C,baseState:C,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Kt,lastRenderedState:C},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Kt,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Bo(e){var t=qo(e).next.queue;pn(e,t,{},vt())}function Ou(){return tt(Cn)}function ko(){return Ye().memoizedState}function Go(){return Ye().memoizedState}function gm(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=vt();e=oa(a);var l=fa(t,e,a);l!==null&&(bt(l,t,a),on(l,t,a)),t={cache:eu()},e.payload=t;return}t=t.return}}function mm(e,t,a){var l=vt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Ei(e)?Vo(t,a):(a=Xs(e,t,a,l),a!==null&&(bt(a,e,l),Xo(a,t,l)))}function Yo(e,t,a){var l=vt();pn(e,t,a,l)}function pn(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Ei(e))Vo(t,n);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var f=t.lastRenderedState,h=i(f,a);if(n.hasEagerState=!0,n.eagerState=h,ht(h,f))return ri(e,t,n,0),je===null&&ui(),!1}catch{}finally{}if(a=Xs(e,t,n,l),a!==null)return bt(a,e,l),Xo(a,t,l),!0}return!1}function Eu(e,t,a,l){if(l={lane:2,revertLane:lr(),action:l,hasEagerState:!1,eagerState:null,next:null},Ei(e)){if(t)throw Error(u(479))}else t=Xs(e,a,l,2),t!==null&&bt(t,e,2)}function Ei(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function Vo(e,t){yl=bi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Xo(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Fr(e,a)}}var Ti={readContext:tt,use:Si,useCallback:Be,useContext:Be,useEffect:Be,useImperativeHandle:Be,useLayoutEffect:Be,useInsertionEffect:Be,useMemo:Be,useReducer:Be,useRef:Be,useState:Be,useDebugValue:Be,useDeferredValue:Be,useTransition:Be,useSyncExternalStore:Be,useId:Be,useHostTransitionStatus:Be,useFormState:Be,useActionState:Be,useOptimistic:Be,useMemoCache:Be,useCacheRefresh:Be},Qo={readContext:tt,use:Si,useCallback:function(e,t){return st().memoizedState=[e,t===void 0?null:t],e},useContext:tt,useEffect:zo,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Oi(4194308,4,Mo.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Oi(4194308,4,e,t)},useInsertionEffect:function(e,t){Oi(4,2,e,t)},useMemo:function(e,t){var a=st();t=t===void 0?null:t;var l=e();if(Va){ia(!0);try{e()}finally{ia(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=st();if(a!==void 0){var n=a(t);if(Va){ia(!0);try{a(t)}finally{ia(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=mm.bind(null,ne,e),[l.memoizedState,e]},useRef:function(e){var t=st();return e={current:e},t.memoizedState=e},useState:function(e){e=bu(e);var t=e.queue,a=Yo.bind(null,ne,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Su,useDeferredValue:function(e,t){var a=st();return _u(a,e,t)},useTransition:function(){var e=bu(!1);return e=Ho.bind(null,ne,e.queue,!0,!1),st().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ne,n=st();if(ve){if(a===void 0)throw Error(u(407));a=a()}else{if(a=t(),je===null)throw Error(u(349));(ge&124)!==0||fo(l,t,a)}n.memoizedState=a;var i={value:a,getSnapshot:t};return n.queue=i,zo(go.bind(null,l,i,e),[e]),l.flags|=2048,bl(9,Ni(),ho.bind(null,l,i,a,t),null),a},useId:function(){var e=st(),t=je.identifierPrefix;if(ve){var a=Xt,l=Vt;a=(l&~(1<<32-dt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=xi++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=cm++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ou,useFormState:No,useActionState:No,useOptimistic:function(e){var t=st();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Eu.bind(null,ne,!0,a),a.dispatch=t,[e,t]},useMemoCache:pu,useCacheRefresh:function(){return st().memoizedState=gm.bind(null,ne)}},Zo={readContext:tt,use:Si,useCallback:Co,useContext:tt,useEffect:wo,useImperativeHandle:Do,useInsertionEffect:jo,useLayoutEffect:Ro,useMemo:Uo,useReducer:_i,useRef:Ao,useState:function(){return _i(Kt)},useDebugValue:Su,useDeferredValue:function(e,t){var a=Ye();return Lo(a,Oe.memoizedState,e,t)},useTransition:function(){var e=_i(Kt)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:gn(e),t]},useSyncExternalStore:oo,useId:ko,useHostTransitionStatus:Ou,useFormState:Oo,useActionState:Oo,useOptimistic:function(e,t){var a=Ye();return yo(a,Oe,e,t)},useMemoCache:pu,useCacheRefresh:Go},pm={readContext:tt,use:Si,useCallback:Co,useContext:tt,useEffect:wo,useImperativeHandle:Do,useInsertionEffect:jo,useLayoutEffect:Ro,useMemo:Uo,useReducer:vu,useRef:Ao,useState:function(){return vu(Kt)},useDebugValue:Su,useDeferredValue:function(e,t){var a=Ye();return Oe===null?_u(a,e,t):Lo(a,Oe.memoizedState,e,t)},useTransition:function(){var e=vu(Kt)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:gn(e),t]},useSyncExternalStore:oo,useId:ko,useHostTransitionStatus:Ou,useFormState:To,useActionState:To,useOptimistic:function(e,t){var a=Ye();return Oe!==null?yo(a,Oe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:pu,useCacheRefresh:Go},xl=null,yn=0;function Ai(e){var t=yn;return yn+=1,xl===null&&(xl=[]),ao(xl,e,t)}function vn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function zi(e,t){throw t.$$typeof===w?Error(u(525)):(e=Object.prototype.toString.call(t),Error(u(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Ko(e){var t=e._init;return t(e._payload)}function Jo(e){function t(_,S){if(e){var O=_.deletions;O===null?(_.deletions=[S],_.flags|=16):O.push(S)}}function a(_,S){if(!e)return null;for(;S!==null;)t(_,S),S=S.sibling;return null}function l(_){for(var S=new Map;_!==null;)_.key!==null?S.set(_.key,_):S.set(_.index,_),_=_.sibling;return S}function n(_,S){return _=Yt(_,S),_.index=0,_.sibling=null,_}function i(_,S,O){return _.index=O,e?(O=_.alternate,O!==null?(O=O.index,O<S?(_.flags|=67108866,S):O):(_.flags|=67108866,S)):(_.flags|=1048576,S)}function f(_){return e&&_.alternate===null&&(_.flags|=67108866),_}function h(_,S,O,M){return S===null||S.tag!==6?(S=Zs(O,_.mode,M),S.return=_,S):(S=n(S,O),S.return=_,S)}function b(_,S,O,M){var V=O.type;return V===U?j(_,S,O.props.children,M,O.key):S!==null&&(S.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===G&&Ko(V)===S.type)?(S=n(S,O.props),vn(S,O),S.return=_,S):(S=oi(O.type,O.key,O.props,null,_.mode,M),vn(S,O),S.return=_,S)}function E(_,S,O,M){return S===null||S.tag!==4||S.stateNode.containerInfo!==O.containerInfo||S.stateNode.implementation!==O.implementation?(S=Ks(O,_.mode,M),S.return=_,S):(S=n(S,O.children||[]),S.return=_,S)}function j(_,S,O,M,V){return S===null||S.tag!==7?(S=Ua(O,_.mode,M,V),S.return=_,S):(S=n(S,O),S.return=_,S)}function D(_,S,O){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return S=Zs(""+S,_.mode,O),S.return=_,S;if(typeof S=="object"&&S!==null){switch(S.$$typeof){case q:return O=oi(S.type,S.key,S.props,null,_.mode,O),vn(O,S),O.return=_,O;case B:return S=Ks(S,_.mode,O),S.return=_,S;case G:var M=S._init;return S=M(S._payload),D(_,S,O)}if(we(S)||De(S))return S=Ua(S,_.mode,O,null),S.return=_,S;if(typeof S.then=="function")return D(_,Ai(S),O);if(S.$$typeof===ue)return D(_,gi(_,S),O);zi(_,S)}return null}function T(_,S,O,M){var V=S!==null?S.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return V!==null?null:h(_,S,""+O,M);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case q:return O.key===V?b(_,S,O,M):null;case B:return O.key===V?E(_,S,O,M):null;case G:return V=O._init,O=V(O._payload),T(_,S,O,M)}if(we(O)||De(O))return V!==null?null:j(_,S,O,M,null);if(typeof O.then=="function")return T(_,S,Ai(O),M);if(O.$$typeof===ue)return T(_,S,gi(_,O),M);zi(_,O)}return null}function A(_,S,O,M,V){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return _=_.get(O)||null,h(S,_,""+M,V);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case q:return _=_.get(M.key===null?O:M.key)||null,b(S,_,M,V);case B:return _=_.get(M.key===null?O:M.key)||null,E(S,_,M,V);case G:var se=M._init;return M=se(M._payload),A(_,S,O,M,V)}if(we(M)||De(M))return _=_.get(O)||null,j(S,_,M,V,null);if(typeof M.then=="function")return A(_,S,O,Ai(M),V);if(M.$$typeof===ue)return A(_,S,O,gi(S,M),V);zi(S,M)}return null}function P(_,S,O,M){for(var V=null,se=null,K=S,W=S=0,$e=null;K!==null&&W<O.length;W++){K.index>W?($e=K,K=null):$e=K.sibling;var ye=T(_,K,O[W],M);if(ye===null){K===null&&(K=$e);break}e&&K&&ye.alternate===null&&t(_,K),S=i(ye,S,W),se===null?V=ye:se.sibling=ye,se=ye,K=$e}if(W===O.length)return a(_,K),ve&&Ha(_,W),V;if(K===null){for(;W<O.length;W++)K=D(_,O[W],M),K!==null&&(S=i(K,S,W),se===null?V=K:se.sibling=K,se=K);return ve&&Ha(_,W),V}for(K=l(K);W<O.length;W++)$e=A(K,_,W,O[W],M),$e!==null&&(e&&$e.alternate!==null&&K.delete($e.key===null?W:$e.key),S=i($e,S,W),se===null?V=$e:se.sibling=$e,se=$e);return e&&K.forEach(function(za){return t(_,za)}),ve&&Ha(_,W),V}function $(_,S,O,M){if(O==null)throw Error(u(151));for(var V=null,se=null,K=S,W=S=0,$e=null,ye=O.next();K!==null&&!ye.done;W++,ye=O.next()){K.index>W?($e=K,K=null):$e=K.sibling;var za=T(_,K,ye.value,M);if(za===null){K===null&&(K=$e);break}e&&K&&za.alternate===null&&t(_,K),S=i(za,S,W),se===null?V=za:se.sibling=za,se=za,K=$e}if(ye.done)return a(_,K),ve&&Ha(_,W),V;if(K===null){for(;!ye.done;W++,ye=O.next())ye=D(_,ye.value,M),ye!==null&&(S=i(ye,S,W),se===null?V=ye:se.sibling=ye,se=ye);return ve&&Ha(_,W),V}for(K=l(K);!ye.done;W++,ye=O.next())ye=A(K,_,W,ye.value,M),ye!==null&&(e&&ye.alternate!==null&&K.delete(ye.key===null?W:ye.key),S=i(ye,S,W),se===null?V=ye:se.sibling=ye,se=ye);return e&&K.forEach(function(yp){return t(_,yp)}),ve&&Ha(_,W),V}function Te(_,S,O,M){if(typeof O=="object"&&O!==null&&O.type===U&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case q:e:{for(var V=O.key;S!==null;){if(S.key===V){if(V=O.type,V===U){if(S.tag===7){a(_,S.sibling),M=n(S,O.props.children),M.return=_,_=M;break e}}else if(S.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===G&&Ko(V)===S.type){a(_,S.sibling),M=n(S,O.props),vn(M,O),M.return=_,_=M;break e}a(_,S);break}else t(_,S);S=S.sibling}O.type===U?(M=Ua(O.props.children,_.mode,M,O.key),M.return=_,_=M):(M=oi(O.type,O.key,O.props,null,_.mode,M),vn(M,O),M.return=_,_=M)}return f(_);case B:e:{for(V=O.key;S!==null;){if(S.key===V)if(S.tag===4&&S.stateNode.containerInfo===O.containerInfo&&S.stateNode.implementation===O.implementation){a(_,S.sibling),M=n(S,O.children||[]),M.return=_,_=M;break e}else{a(_,S);break}else t(_,S);S=S.sibling}M=Ks(O,_.mode,M),M.return=_,_=M}return f(_);case G:return V=O._init,O=V(O._payload),Te(_,S,O,M)}if(we(O))return P(_,S,O,M);if(De(O)){if(V=De(O),typeof V!="function")throw Error(u(150));return O=V.call(O),$(_,S,O,M)}if(typeof O.then=="function")return Te(_,S,Ai(O),M);if(O.$$typeof===ue)return Te(_,S,gi(_,O),M);zi(_,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,S!==null&&S.tag===6?(a(_,S.sibling),M=n(S,O),M.return=_,_=M):(a(_,S),M=Zs(O,_.mode,M),M.return=_,_=M),f(_)):a(_,S)}return function(_,S,O,M){try{yn=0;var V=Te(_,S,O,M);return xl=null,V}catch(K){if(K===rn||K===pi)throw K;var se=gt(29,K,null,_.mode);return se.lanes=M,se.return=_,se}finally{}}}var Sl=Jo(!0),$o=Jo(!1),Tt=R(null),Ct=null;function ha(e){var t=e.alternate;L(Xe,Xe.current&1),L(Tt,e),Ct===null&&(t===null||pl.current!==null||t.memoizedState!==null)&&(Ct=e)}function Wo(e){if(e.tag===22){if(L(Xe,Xe.current),L(Tt,e),Ct===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ct=e)}}else ga()}function ga(){L(Xe,Xe.current),L(Tt,Tt.current)}function Jt(e){k(Tt),Ct===e&&(Ct=null),k(Xe)}var Xe=R(0);function wi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||mr(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Tu(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:N({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Au={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=vt(),n=oa(l);n.payload=t,a!=null&&(n.callback=a),t=fa(e,n,l),t!==null&&(bt(t,e,l),on(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=vt(),n=oa(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=fa(e,n,l),t!==null&&(bt(t,e,l),on(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=vt(),l=oa(a);l.tag=2,t!=null&&(l.callback=t),t=fa(e,l,a),t!==null&&(bt(t,e,a),on(t,e,a))}};function Fo(e,t,a,l,n,i,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,i,f):t.prototype&&t.prototype.isPureReactComponent?!Il(a,l)||!Il(n,i):!0}function Po(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&Au.enqueueReplaceState(t,t.state,null)}function Xa(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=N({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var ji=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Io(e){ji(e)}function ef(e){console.error(e)}function tf(e){ji(e)}function Ri(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function af(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function zu(e,t,a){return a=oa(a),a.tag=3,a.payload={element:null},a.callback=function(){Ri(e,t)},a}function lf(e){return e=oa(e),e.tag=3,e}function nf(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var i=l.value;e.payload=function(){return n(i)},e.callback=function(){af(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){af(t,a,l),typeof n!="function"&&(xa===null?xa=new Set([this]):xa.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function ym(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&nn(t,a,n,!0),a=Tt.current,a!==null){switch(a.tag){case 13:return Ct===null?Pu():a.alternate===null&&He===0&&(He=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===lu?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),er(e,l,n)),!1;case 22:return a.flags|=65536,l===lu?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),er(e,l,n)),!1}throw Error(u(435,a.tag))}return er(e,l,n),Pu(),!1}if(ve)return t=Tt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Ws&&(e=Error(u(422),{cause:l}),ln(_t(e,a)))):(l!==Ws&&(t=Error(u(423),{cause:l}),ln(_t(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=_t(l,a),n=zu(e.stateNode,l,n),su(e,n),He!==4&&(He=2)),!1;var i=Error(u(520),{cause:l});if(i=_t(i,a),En===null?En=[i]:En.push(i),He!==4&&(He=2),t===null)return!0;l=_t(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=zu(a.stateNode,l,e),su(a,e),!1;case 1:if(t=a.type,i=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(xa===null||!xa.has(i))))return a.flags|=65536,n&=-n,a.lanes|=n,n=lf(n),nf(n,e,a,l),su(a,n),!1}a=a.return}while(a!==null);return!1}var sf=Error(u(461)),Ke=!1;function We(e,t,a,l){t.child=e===null?$o(t,null,a,l):Sl(t,e.child,a,l)}function uf(e,t,a,l,n){a=a.render;var i=t.ref;if("ref"in l){var f={};for(var h in l)h!=="ref"&&(f[h]=l[h])}else f=l;return Ga(t),l=fu(e,t,a,f,i,n),h=du(),e!==null&&!Ke?(hu(e,t,n),$t(e,t,n)):(ve&&h&&Js(t),t.flags|=1,We(e,t,l,n),t.child)}function rf(e,t,a,l,n){if(e===null){var i=a.type;return typeof i=="function"&&!Qs(i)&&i.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=i,cf(e,t,i,l,n)):(e=oi(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!Lu(e,n)){var f=i.memoizedProps;if(a=a.compare,a=a!==null?a:Il,a(f,l)&&e.ref===t.ref)return $t(e,t,n)}return t.flags|=1,e=Yt(i,l),e.ref=t.ref,e.return=t,t.child=e}function cf(e,t,a,l,n){if(e!==null){var i=e.memoizedProps;if(Il(i,l)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=l=i,Lu(e,n))(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,$t(e,t,n)}return wu(e,t,a,l,n)}function of(e,t,a){var l=t.pendingProps,n=l.children,i=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=i!==null?i.baseLanes|a:a,e!==null){for(n=t.child=e.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;t.childLanes=i&~l}else t.childLanes=0,t.child=null;return ff(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&mi(t,i!==null?i.cachePool:null),i!==null?uo(t,i):ru(),Wo(t);else return t.lanes=t.childLanes=536870912,ff(e,t,i!==null?i.baseLanes|a:a,a)}else i!==null?(mi(t,i.cachePool),uo(t,i),ga(),t.memoizedState=null):(e!==null&&mi(t,null),ru(),ga());return We(e,t,n,a),t.child}function ff(e,t,a,l){var n=au();return n=n===null?null:{parent:Ve._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&mi(t,null),ru(),Wo(t),e!==null&&nn(e,t,l,!0),null}function Mi(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(u(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function wu(e,t,a,l,n){return Ga(t),a=fu(e,t,a,l,void 0,n),l=du(),e!==null&&!Ke?(hu(e,t,n),$t(e,t,n)):(ve&&l&&Js(t),t.flags|=1,We(e,t,a,n),t.child)}function df(e,t,a,l,n,i){return Ga(t),t.updateQueue=null,a=co(t,l,a,n),ro(e),l=du(),e!==null&&!Ke?(hu(e,t,i),$t(e,t,i)):(ve&&l&&Js(t),t.flags|=1,We(e,t,a,i),t.child)}function hf(e,t,a,l,n){if(Ga(t),t.stateNode===null){var i=fl,f=a.contextType;typeof f=="object"&&f!==null&&(i=tt(f)),i=new a(l,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Au,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=l,i.state=t.memoizedState,i.refs={},nu(t),f=a.contextType,i.context=typeof f=="object"&&f!==null?tt(f):fl,i.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(Tu(t,a,f,l),i.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&Au.enqueueReplaceState(i,i.state,null),dn(t,l,i,n),fn(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){i=t.stateNode;var h=t.memoizedProps,b=Xa(a,h);i.props=b;var E=i.context,j=a.contextType;f=fl,typeof j=="object"&&j!==null&&(f=tt(j));var D=a.getDerivedStateFromProps;j=typeof D=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,j||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||E!==f)&&Po(t,i,l,f),ca=!1;var T=t.memoizedState;i.state=T,dn(t,l,i,n),fn(),E=t.memoizedState,h||T!==E||ca?(typeof D=="function"&&(Tu(t,a,D,l),E=t.memoizedState),(b=ca||Fo(t,a,b,l,T,E,f))?(j||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=E),i.props=l,i.state=E,i.context=f,l=b):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{i=t.stateNode,iu(e,t),f=t.memoizedProps,j=Xa(a,f),i.props=j,D=t.pendingProps,T=i.context,E=a.contextType,b=fl,typeof E=="object"&&E!==null&&(b=tt(E)),h=a.getDerivedStateFromProps,(E=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==D||T!==b)&&Po(t,i,l,b),ca=!1,T=t.memoizedState,i.state=T,dn(t,l,i,n),fn();var A=t.memoizedState;f!==D||T!==A||ca||e!==null&&e.dependencies!==null&&hi(e.dependencies)?(typeof h=="function"&&(Tu(t,a,h,l),A=t.memoizedState),(j=ca||Fo(t,a,j,l,T,A,b)||e!==null&&e.dependencies!==null&&hi(e.dependencies))?(E||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(l,A,b),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(l,A,b)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=A),i.props=l,i.state=A,i.context=b,l=j):(typeof i.componentDidUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&T===e.memoizedState||(t.flags|=1024),l=!1)}return i=l,Mi(e,t),l=(t.flags&128)!==0,i||l?(i=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&l?(t.child=Sl(t,e.child,null,n),t.child=Sl(t,null,a,n)):We(e,t,a,n),t.memoizedState=i.state,e=t.child):e=$t(e,t,n),e}function gf(e,t,a,l){return an(),t.flags|=256,We(e,t,a,l),t.child}var ju={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ru(e){return{baseLanes:e,cachePool:Ic()}}function Mu(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=At),e}function mf(e,t,a){var l=t.pendingProps,n=!1,i=(t.flags&128)!==0,f;if((f=i)||(f=e!==null&&e.memoizedState===null?!1:(Xe.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(ve){if(n?ha(t):ga(),ve){var h=Le,b;if(b=h){e:{for(b=h,h=Dt;b.nodeType!==8;){if(!h){h=null;break e}if(b=Rt(b.nextSibling),b===null){h=null;break e}}h=b}h!==null?(t.memoizedState={dehydrated:h,treeContext:La!==null?{id:Vt,overflow:Xt}:null,retryLane:536870912,hydrationErrors:null},b=gt(18,null,null,0),b.stateNode=h,b.return=t,t.child=b,lt=t,Le=null,b=!0):b=!1}b||Ba(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return mr(h)?t.lanes=32:t.lanes=536870912,null;Jt(t)}return h=l.children,l=l.fallback,n?(ga(),n=t.mode,h=Di({mode:"hidden",children:h},n),l=Ua(l,n,a,null),h.return=t,l.return=t,h.sibling=l,t.child=h,n=t.child,n.memoizedState=Ru(a),n.childLanes=Mu(e,f,a),t.memoizedState=ju,l):(ha(t),Du(t,h))}if(b=e.memoizedState,b!==null&&(h=b.dehydrated,h!==null)){if(i)t.flags&256?(ha(t),t.flags&=-257,t=Cu(e,t,a)):t.memoizedState!==null?(ga(),t.child=e.child,t.flags|=128,t=null):(ga(),n=l.fallback,h=t.mode,l=Di({mode:"visible",children:l.children},h),n=Ua(n,h,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,Sl(t,e.child,null,a),l=t.child,l.memoizedState=Ru(a),l.childLanes=Mu(e,f,a),t.memoizedState=ju,t=n);else if(ha(t),mr(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var E=f.dgst;f=E,l=Error(u(419)),l.stack="",l.digest=f,ln({value:l,source:null,stack:null}),t=Cu(e,t,a)}else if(Ke||nn(e,t,a,!1),f=(a&e.childLanes)!==0,Ke||f){if(f=je,f!==null&&(l=a&-a,l=(l&42)!==0?1:ps(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==b.retryLane))throw b.retryLane=l,ol(e,l),bt(f,e,l),sf;h.data==="$?"||Pu(),t=Cu(e,t,a)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Le=Rt(h.nextSibling),lt=t,ve=!0,qa=null,Dt=!1,e!==null&&(Ot[Et++]=Vt,Ot[Et++]=Xt,Ot[Et++]=La,Vt=e.id,Xt=e.overflow,La=t),t=Du(t,l.children),t.flags|=4096);return t}return n?(ga(),n=l.fallback,h=t.mode,b=e.child,E=b.sibling,l=Yt(b,{mode:"hidden",children:l.children}),l.subtreeFlags=b.subtreeFlags&65011712,E!==null?n=Yt(E,n):(n=Ua(n,h,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,h=e.child.memoizedState,h===null?h=Ru(a):(b=h.cachePool,b!==null?(E=Ve._currentValue,b=b.parent!==E?{parent:E,pool:E}:b):b=Ic(),h={baseLanes:h.baseLanes|a,cachePool:b}),n.memoizedState=h,n.childLanes=Mu(e,f,a),t.memoizedState=ju,l):(ha(t),a=e.child,e=a.sibling,a=Yt(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function Du(e,t){return t=Di({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Di(e,t){return e=gt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Cu(e,t,a){return Sl(t,e.child,null,a),e=Du(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function pf(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Ps(e.return,t,a)}function Uu(e,t,a,l,n){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=l,i.tail=a,i.tailMode=n)}function yf(e,t,a){var l=t.pendingProps,n=l.revealOrder,i=l.tail;if(We(e,t,l.children,a),l=Xe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pf(e,a,t);else if(e.tag===19)pf(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(L(Xe,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&wi(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),Uu(t,!1,n,a,i);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&wi(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}Uu(t,!0,a,null,i);break;case"together":Uu(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function $t(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),ba|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(nn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(u(153));if(t.child!==null){for(e=t.child,a=Yt(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Yt(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function Lu(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&hi(e)))}function vm(e,t,a){switch(t.tag){case 3:Ae(t,t.stateNode.containerInfo),ra(t,Ve,e.memoizedState.cache),an();break;case 27:case 5:aa(t);break;case 4:Ae(t,t.stateNode.containerInfo);break;case 10:ra(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(ha(t),t.flags|=128,null):(a&t.child.childLanes)!==0?mf(e,t,a):(ha(t),e=$t(e,t,a),e!==null?e.sibling:null);ha(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(nn(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return yf(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(Xe,Xe.current),l)break;return null;case 22:case 23:return t.lanes=0,of(e,t,a);case 24:ra(t,Ve,e.memoizedState.cache)}return $t(e,t,a)}function vf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ke=!0;else{if(!Lu(e,a)&&(t.flags&128)===0)return Ke=!1,vm(e,t,a);Ke=(e.flags&131072)!==0}else Ke=!1,ve&&(t.flags&1048576)!==0&&Zc(t,di,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")Qs(l)?(e=Xa(l,e),t.tag=1,t=hf(null,t,l,e,a)):(t.tag=0,t=wu(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===oe){t.tag=11,t=uf(null,t,l,e,a);break e}else if(n===me){t.tag=14,t=rf(null,t,l,e,a);break e}}throw t=Ce(l)||l,Error(u(306,t,""))}}return t;case 0:return wu(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=Xa(l,t.pendingProps),hf(e,t,l,n,a);case 3:e:{if(Ae(t,t.stateNode.containerInfo),e===null)throw Error(u(387));l=t.pendingProps;var i=t.memoizedState;n=i.element,iu(e,t),dn(t,l,null,a);var f=t.memoizedState;if(l=f.cache,ra(t,Ve,l),l!==i.cache&&Is(t,[Ve],a,!0),fn(),l=f.element,i.isDehydrated)if(i={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=gf(e,t,l,a);break e}else if(l!==n){n=_t(Error(u(424)),t),ln(n),t=gf(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Le=Rt(e.firstChild),lt=t,ve=!0,qa=null,Dt=!0,a=$o(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(an(),l===n){t=$t(e,t,a);break e}We(e,t,l,a)}t=t.child}return t;case 26:return Mi(e,t),e===null?(a=_d(t.type,null,t.pendingProps,null))?t.memoizedState=a:ve||(a=t.type,e=t.pendingProps,l=Ki(I.current).createElement(a),l[et]=t,l[nt]=e,Pe(l,a,e),Ze(l),t.stateNode=l):t.memoizedState=_d(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return aa(t),e===null&&ve&&(l=t.stateNode=bd(t.type,t.pendingProps,I.current),lt=t,Dt=!0,n=Le,Na(t.type)?(pr=n,Le=Rt(l.firstChild)):Le=n),We(e,t,t.pendingProps.children,a),Mi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ve&&((n=l=Le)&&(l=Zm(l,t.type,t.pendingProps,Dt),l!==null?(t.stateNode=l,lt=t,Le=Rt(l.firstChild),Dt=!1,n=!0):n=!1),n||Ba(t)),aa(t),n=t.type,i=t.pendingProps,f=e!==null?e.memoizedProps:null,l=i.children,dr(n,i)?l=null:f!==null&&dr(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=fu(e,t,om,null,null,a),Cn._currentValue=n),Mi(e,t),We(e,t,l,a),t.child;case 6:return e===null&&ve&&((e=a=Le)&&(a=Km(a,t.pendingProps,Dt),a!==null?(t.stateNode=a,lt=t,Le=null,e=!0):e=!1),e||Ba(t)),null;case 13:return mf(e,t,a);case 4:return Ae(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Sl(t,null,l,a):We(e,t,l,a),t.child;case 11:return uf(e,t,t.type,t.pendingProps,a);case 7:return We(e,t,t.pendingProps,a),t.child;case 8:return We(e,t,t.pendingProps.children,a),t.child;case 12:return We(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,ra(t,t.type,l.value),We(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,Ga(t),n=tt(n),l=l(n),t.flags|=1,We(e,t,l,a),t.child;case 14:return rf(e,t,t.type,t.pendingProps,a);case 15:return cf(e,t,t.type,t.pendingProps,a);case 19:return yf(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=Di(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Yt(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return of(e,t,a);case 24:return Ga(t),l=tt(Ve),e===null?(n=au(),n===null&&(n=je,i=eu(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=a),n=i),t.memoizedState={parent:l,cache:n},nu(t),ra(t,Ve,n)):((e.lanes&a)!==0&&(iu(e,t),dn(t,null,null,a),fn()),n=e.memoizedState,i=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ra(t,Ve,l)):(l=i.cache,ra(t,Ve,l),l!==n.cache&&Is(t,[Ve],a,!0))),We(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(u(156,t.tag))}function Wt(e){e.flags|=4}function bf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Ad(t)){if(t=Tt.current,t!==null&&((ge&4194048)===ge?Ct!==null:(ge&62914560)!==ge&&(ge&536870912)===0||t!==Ct))throw cn=lu,eo;e.flags|=8192}}function Ci(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?$r():536870912,e.lanes|=t,El|=t)}function bn(e,t){if(!ve)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function bm(e,t,a){var l=t.pendingProps;switch($s(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ue(t),null;case 1:return Ue(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Zt(Ve),ot(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(tn(t)?Wt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,$c())),Ue(t),null;case 26:return a=t.memoizedState,e===null?(Wt(t),a!==null?(Ue(t),bf(t,a)):(Ue(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Wt(t),Ue(t),bf(t,a)):(Ue(t),t.flags&=-16777217):(e.memoizedProps!==l&&Wt(t),Ue(t),t.flags&=-16777217),null;case 27:la(t),a=I.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Wt(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Ue(t),null}e=Y.current,tn(t)?Kc(t):(e=bd(n,l,a),t.stateNode=e,Wt(t))}return Ue(t),null;case 5:if(la(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Wt(t);else{if(!l){if(t.stateNode===null)throw Error(u(166));return Ue(t),null}if(e=Y.current,tn(t))Kc(t);else{switch(n=Ki(I.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[et]=t,e[nt]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(Pe(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Wt(t)}}return Ue(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Wt(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(u(166));if(e=I.current,tn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=lt,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[et]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||dd(e.nodeValue,a)),e||Ba(t)}else e=Ki(e).createTextNode(l),e[et]=t,t.stateNode=e}return Ue(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=tn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(u(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(u(317));n[et]=t}else an(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ue(t),n=!1}else n=$c(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Jt(t),t):(Jt(t),null)}if(Jt(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var i=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(i=l.memoizedState.cachePool.pool),i!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Ci(t,t.updateQueue),Ue(t),null;case 4:return ot(),e===null&&ur(t.stateNode.containerInfo),Ue(t),null;case 10:return Zt(t.type),Ue(t),null;case 19:if(k(Xe),n=t.memoizedState,n===null)return Ue(t),null;if(l=(t.flags&128)!==0,i=n.rendering,i===null)if(l)bn(n,!1);else{if(He!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=wi(e),i!==null){for(t.flags|=128,bn(n,!1),e=i.updateQueue,t.updateQueue=e,Ci(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Qc(a,e),a=a.sibling;return L(Xe,Xe.current&1|2),t.child}e=e.sibling}n.tail!==null&&Mt()>Hi&&(t.flags|=128,l=!0,bn(n,!1),t.lanes=4194304)}else{if(!l)if(e=wi(i),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Ci(t,e),bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!ve)return Ue(t),null}else 2*Mt()-n.renderingStartTime>Hi&&a!==536870912&&(t.flags|=128,l=!0,bn(n,!1),t.lanes=4194304);n.isBackwards?(i.sibling=t.child,t.child=i):(e=n.last,e!==null?e.sibling=i:t.child=i,n.last=i)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Mt(),t.sibling=null,e=Xe.current,L(Xe,l?e&1|2:e&1),t):(Ue(t),null);case 22:case 23:return Jt(t),cu(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Ue(t),t.subtreeFlags&6&&(t.flags|=8192)):Ue(t),a=t.updateQueue,a!==null&&Ci(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&k(Ya),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Zt(Ve),Ue(t),null;case 25:return null;case 30:return null}throw Error(u(156,t.tag))}function xm(e,t){switch($s(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zt(Ve),ot(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return la(t),null;case 13:if(Jt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(u(340));an()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return k(Xe),null;case 4:return ot(),null;case 10:return Zt(t.type),null;case 22:case 23:return Jt(t),cu(),e!==null&&k(Ya),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Zt(Ve),null;case 25:return null;default:return null}}function xf(e,t){switch($s(t),t.tag){case 3:Zt(Ve),ot();break;case 26:case 27:case 5:la(t);break;case 4:ot();break;case 13:Jt(t);break;case 19:k(Xe);break;case 10:Zt(t.type);break;case 22:case 23:Jt(t),cu(),e!==null&&k(Ya);break;case 24:Zt(Ve)}}function xn(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var i=a.create,f=a.inst;l=i(),f.destroy=l}a=a.next}while(a!==n)}}catch(h){ze(t,t.return,h)}}function ma(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var i=n.next;l=i;do{if((l.tag&e)===e){var f=l.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,n=t;var b=a,E=h;try{E()}catch(j){ze(n,b,j)}}}l=l.next}while(l!==i)}}catch(j){ze(t,t.return,j)}}function Sf(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{so(t,a)}catch(l){ze(e,e.return,l)}}}function _f(e,t,a){a.props=Xa(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){ze(e,t,l)}}function Sn(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){ze(e,t,n)}}function Ut(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){ze(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){ze(e,t,n)}else a.current=null}function Nf(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){ze(e,e.return,n)}}function Hu(e,t,a){try{var l=e.stateNode;Gm(l,e.type,a,t),l[nt]=t}catch(n){ze(e,e.return,n)}}function Of(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Na(e.type)||e.tag===4}function qu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Of(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Na(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Bu(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Zi));else if(l!==4&&(l===27&&Na(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Bu(e,t,a),e=e.sibling;e!==null;)Bu(e,t,a),e=e.sibling}function Ui(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Na(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ui(e,t,a),e=e.sibling;e!==null;)Ui(e,t,a),e=e.sibling}function Ef(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);Pe(t,l,a),t[et]=e,t[nt]=a}catch(i){ze(e,e.return,i)}}var Ft=!1,ke=!1,ku=!1,Tf=typeof WeakSet=="function"?WeakSet:Set,Je=null;function Sm(e,t){if(e=e.containerInfo,or=Ii,e=Uc(e),qs(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,i=l.focusNode;l=l.focusOffset;try{a.nodeType,i.nodeType}catch{a=null;break e}var f=0,h=-1,b=-1,E=0,j=0,D=e,T=null;t:for(;;){for(var A;D!==a||n!==0&&D.nodeType!==3||(h=f+n),D!==i||l!==0&&D.nodeType!==3||(b=f+l),D.nodeType===3&&(f+=D.nodeValue.length),(A=D.firstChild)!==null;)T=D,D=A;for(;;){if(D===e)break t;if(T===a&&++E===n&&(h=f),T===i&&++j===l&&(b=f),(A=D.nextSibling)!==null)break;D=T,T=D.parentNode}D=A}a=h===-1||b===-1?null:{start:h,end:b}}else a=null}a=a||{start:0,end:0}}else a=null;for(fr={focusedElem:e,selectionRange:a},Ii=!1,Je=t;Je!==null;)if(t=Je,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Je=e;else for(;Je!==null;){switch(t=Je,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,a=t,n=i.memoizedProps,i=i.memoizedState,l=a.stateNode;try{var P=Xa(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(P,i),l.__reactInternalSnapshotBeforeUpdate=e}catch($){ze(a,a.return,$)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)gr(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(u(163))}if(e=t.sibling,e!==null){e.return=t.return,Je=e;break}Je=t.return}}function Af(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:pa(e,a),l&4&&xn(5,a);break;case 1:if(pa(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){ze(a,a.return,f)}else{var n=Xa(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ze(a,a.return,f)}}l&64&&Sf(a),l&512&&Sn(a,a.return);break;case 3:if(pa(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{so(e,t)}catch(f){ze(a,a.return,f)}}break;case 27:t===null&&l&4&&Ef(a);case 26:case 5:pa(e,a),t===null&&l&4&&Nf(a),l&512&&Sn(a,a.return);break;case 12:pa(e,a);break;case 13:pa(e,a),l&4&&jf(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=jm.bind(null,a),Jm(e,a))));break;case 22:if(l=a.memoizedState!==null||Ft,!l){t=t!==null&&t.memoizedState!==null||ke,n=Ft;var i=ke;Ft=l,(ke=t)&&!i?ya(e,a,(a.subtreeFlags&8772)!==0):pa(e,a),Ft=n,ke=i}break;case 30:break;default:pa(e,a)}}function zf(e){var t=e.alternate;t!==null&&(e.alternate=null,zf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&bs(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Me=null,ut=!1;function Pt(e,t,a){for(a=a.child;a!==null;)wf(e,t,a),a=a.sibling}function wf(e,t,a){if(ft&&typeof ft.onCommitFiberUnmount=="function")try{ft.onCommitFiberUnmount(Gl,a)}catch{}switch(a.tag){case 26:ke||Ut(a,t),Pt(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:ke||Ut(a,t);var l=Me,n=ut;Na(a.type)&&(Me=a.stateNode,ut=!1),Pt(e,t,a),jn(a.stateNode),Me=l,ut=n;break;case 5:ke||Ut(a,t);case 6:if(l=Me,n=ut,Me=null,Pt(e,t,a),Me=l,ut=n,Me!==null)if(ut)try{(Me.nodeType===9?Me.body:Me.nodeName==="HTML"?Me.ownerDocument.body:Me).removeChild(a.stateNode)}catch(i){ze(a,t,i)}else try{Me.removeChild(a.stateNode)}catch(i){ze(a,t,i)}break;case 18:Me!==null&&(ut?(e=Me,yd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),qn(e)):yd(Me,a.stateNode));break;case 4:l=Me,n=ut,Me=a.stateNode.containerInfo,ut=!0,Pt(e,t,a),Me=l,ut=n;break;case 0:case 11:case 14:case 15:ke||ma(2,a,t),ke||ma(4,a,t),Pt(e,t,a);break;case 1:ke||(Ut(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&_f(a,t,l)),Pt(e,t,a);break;case 21:Pt(e,t,a);break;case 22:ke=(l=ke)||a.memoizedState!==null,Pt(e,t,a),ke=l;break;default:Pt(e,t,a)}}function jf(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{qn(e)}catch(a){ze(t,t.return,a)}}function _m(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Tf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Tf),t;default:throw Error(u(435,e.tag))}}function Gu(e,t){var a=_m(e);t.forEach(function(l){var n=Rm.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function mt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],i=e,f=t,h=f;e:for(;h!==null;){switch(h.tag){case 27:if(Na(h.type)){Me=h.stateNode,ut=!1;break e}break;case 5:Me=h.stateNode,ut=!1;break e;case 3:case 4:Me=h.stateNode.containerInfo,ut=!0;break e}h=h.return}if(Me===null)throw Error(u(160));wf(i,f,n),Me=null,ut=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Rf(t,e),t=t.sibling}var jt=null;function Rf(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:mt(t,e),pt(e),l&4&&(ma(3,e,e.return),xn(3,e),ma(5,e,e.return));break;case 1:mt(t,e),pt(e),l&512&&(ke||a===null||Ut(a,a.return)),l&64&&Ft&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=jt;if(mt(t,e),pt(e),l&512&&(ke||a===null||Ut(a,a.return)),l&4){var i=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":i=n.getElementsByTagName("title")[0],(!i||i[Xl]||i[et]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(l),n.head.insertBefore(i,n.querySelector("head > title"))),Pe(i,l,a),i[et]=e,Ze(i),l=i;break e;case"link":var f=Ed("link","href",n).get(l+(a.href||""));if(f){for(var h=0;h<f.length;h++)if(i=f[h],i.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&i.getAttribute("rel")===(a.rel==null?null:a.rel)&&i.getAttribute("title")===(a.title==null?null:a.title)&&i.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(h,1);break t}}i=n.createElement(l),Pe(i,l,a),n.head.appendChild(i);break;case"meta":if(f=Ed("meta","content",n).get(l+(a.content||""))){for(h=0;h<f.length;h++)if(i=f[h],i.getAttribute("content")===(a.content==null?null:""+a.content)&&i.getAttribute("name")===(a.name==null?null:a.name)&&i.getAttribute("property")===(a.property==null?null:a.property)&&i.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&i.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(h,1);break t}}i=n.createElement(l),Pe(i,l,a),n.head.appendChild(i);break;default:throw Error(u(468,l))}i[et]=e,Ze(i),l=i}e.stateNode=l}else Td(n,e.type,e.stateNode);else e.stateNode=Od(n,l,e.memoizedProps);else i!==l?(i===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):i.count--,l===null?Td(n,e.type,e.stateNode):Od(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Hu(e,e.memoizedProps,a.memoizedProps)}break;case 27:mt(t,e),pt(e),l&512&&(ke||a===null||Ut(a,a.return)),a!==null&&l&4&&Hu(e,e.memoizedProps,a.memoizedProps);break;case 5:if(mt(t,e),pt(e),l&512&&(ke||a===null||Ut(a,a.return)),e.flags&32){n=e.stateNode;try{ll(n,"")}catch(A){ze(e,e.return,A)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,Hu(e,n,a!==null?a.memoizedProps:n)),l&1024&&(ku=!0);break;case 6:if(mt(t,e),pt(e),l&4){if(e.stateNode===null)throw Error(u(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(A){ze(e,e.return,A)}}break;case 3:if(Wi=null,n=jt,jt=Ji(t.containerInfo),mt(t,e),jt=n,pt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{qn(t.containerInfo)}catch(A){ze(e,e.return,A)}ku&&(ku=!1,Mf(e));break;case 4:l=jt,jt=Ji(e.stateNode.containerInfo),mt(t,e),pt(e),jt=l;break;case 12:mt(t,e),pt(e);break;case 13:mt(t,e),pt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Ku=Mt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Gu(e,l)));break;case 22:n=e.memoizedState!==null;var b=a!==null&&a.memoizedState!==null,E=Ft,j=ke;if(Ft=E||n,ke=j||b,mt(t,e),ke=j,Ft=E,pt(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||b||Ft||ke||Qa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){b=a=t;try{if(i=b.stateNode,n)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=b.stateNode;var D=b.memoizedProps.style,T=D!=null&&D.hasOwnProperty("display")?D.display:null;h.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(A){ze(b,b.return,A)}}}else if(t.tag===6){if(a===null){b=t;try{b.stateNode.nodeValue=n?"":b.memoizedProps}catch(A){ze(b,b.return,A)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Gu(e,a))));break;case 19:mt(t,e),pt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Gu(e,l)));break;case 30:break;case 21:break;default:mt(t,e),pt(e)}}function pt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(Of(l)){a=l;break}l=l.return}if(a==null)throw Error(u(160));switch(a.tag){case 27:var n=a.stateNode,i=qu(e);Ui(e,i,n);break;case 5:var f=a.stateNode;a.flags&32&&(ll(f,""),a.flags&=-33);var h=qu(e);Ui(e,h,f);break;case 3:case 4:var b=a.stateNode.containerInfo,E=qu(e);Bu(e,E,b);break;default:throw Error(u(161))}}catch(j){ze(e,e.return,j)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Mf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Mf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function pa(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Af(e,t.alternate,t),t=t.sibling}function Qa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ma(4,t,t.return),Qa(t);break;case 1:Ut(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&_f(t,t.return,a),Qa(t);break;case 27:jn(t.stateNode);case 26:case 5:Ut(t,t.return),Qa(t);break;case 22:t.memoizedState===null&&Qa(t);break;case 30:Qa(t);break;default:Qa(t)}e=e.sibling}}function ya(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,i=t,f=i.flags;switch(i.tag){case 0:case 11:case 15:ya(n,i,a),xn(4,i);break;case 1:if(ya(n,i,a),l=i,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(E){ze(l,l.return,E)}if(l=i,n=l.updateQueue,n!==null){var h=l.stateNode;try{var b=n.shared.hiddenCallbacks;if(b!==null)for(n.shared.hiddenCallbacks=null,n=0;n<b.length;n++)io(b[n],h)}catch(E){ze(l,l.return,E)}}a&&f&64&&Sf(i),Sn(i,i.return);break;case 27:Ef(i);case 26:case 5:ya(n,i,a),a&&l===null&&f&4&&Nf(i),Sn(i,i.return);break;case 12:ya(n,i,a);break;case 13:ya(n,i,a),a&&f&4&&jf(n,i);break;case 22:i.memoizedState===null&&ya(n,i,a),Sn(i,i.return);break;case 30:break;default:ya(n,i,a)}t=t.sibling}}function Yu(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&sn(a))}function Vu(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&sn(e))}function Lt(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Df(e,t,a,l),t=t.sibling}function Df(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Lt(e,t,a,l),n&2048&&xn(9,t);break;case 1:Lt(e,t,a,l);break;case 3:Lt(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&sn(e)));break;case 12:if(n&2048){Lt(e,t,a,l),e=t.stateNode;try{var i=t.memoizedProps,f=i.id,h=i.onPostCommit;typeof h=="function"&&h(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){ze(t,t.return,b)}}else Lt(e,t,a,l);break;case 13:Lt(e,t,a,l);break;case 23:break;case 22:i=t.stateNode,f=t.alternate,t.memoizedState!==null?i._visibility&2?Lt(e,t,a,l):_n(e,t):i._visibility&2?Lt(e,t,a,l):(i._visibility|=2,_l(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Yu(f,t);break;case 24:Lt(e,t,a,l),n&2048&&Vu(t.alternate,t);break;default:Lt(e,t,a,l)}}function _l(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,f=t,h=a,b=l,E=f.flags;switch(f.tag){case 0:case 11:case 15:_l(i,f,h,b,n),xn(8,f);break;case 23:break;case 22:var j=f.stateNode;f.memoizedState!==null?j._visibility&2?_l(i,f,h,b,n):_n(i,f):(j._visibility|=2,_l(i,f,h,b,n)),n&&E&2048&&Yu(f.alternate,f);break;case 24:_l(i,f,h,b,n),n&&E&2048&&Vu(f.alternate,f);break;default:_l(i,f,h,b,n)}t=t.sibling}}function _n(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:_n(a,l),n&2048&&Yu(l.alternate,l);break;case 24:_n(a,l),n&2048&&Vu(l.alternate,l);break;default:_n(a,l)}t=t.sibling}}var Nn=8192;function Nl(e){if(e.subtreeFlags&Nn)for(e=e.child;e!==null;)Cf(e),e=e.sibling}function Cf(e){switch(e.tag){case 26:Nl(e),e.flags&Nn&&e.memoizedState!==null&&up(jt,e.memoizedState,e.memoizedProps);break;case 5:Nl(e);break;case 3:case 4:var t=jt;jt=Ji(e.stateNode.containerInfo),Nl(e),jt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Nn,Nn=16777216,Nl(e),Nn=t):Nl(e));break;default:Nl(e)}}function Uf(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function On(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Je=l,Hf(l,e)}Uf(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Lf(e),e=e.sibling}function Lf(e){switch(e.tag){case 0:case 11:case 15:On(e),e.flags&2048&&ma(9,e,e.return);break;case 3:On(e);break;case 12:On(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Li(e)):On(e);break;default:On(e)}}function Li(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Je=l,Hf(l,e)}Uf(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ma(8,t,t.return),Li(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Li(t));break;default:Li(t)}e=e.sibling}}function Hf(e,t){for(;Je!==null;){var a=Je;switch(a.tag){case 0:case 11:case 15:ma(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:sn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Je=l;else e:for(a=e;Je!==null;){l=Je;var n=l.sibling,i=l.return;if(zf(l),l===a){Je=null;break e}if(n!==null){n.return=i,Je=n;break e}Je=i}}}var Nm={getCacheForType:function(e){var t=tt(Ve),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Om=typeof WeakMap=="function"?WeakMap:Map,Se=0,je=null,re=null,ge=0,_e=0,yt=null,va=!1,Ol=!1,Xu=!1,It=0,He=0,ba=0,Za=0,Qu=0,At=0,El=0,En=null,rt=null,Zu=!1,Ku=0,Hi=1/0,qi=null,xa=null,Fe=0,Sa=null,Tl=null,Al=0,Ju=0,$u=null,qf=null,Tn=0,Wu=null;function vt(){if((Se&2)!==0&&ge!==0)return ge&-ge;if(z.T!==null){var e=gl;return e!==0?e:lr()}return Pr()}function Bf(){At===0&&(At=(ge&536870912)===0||ve?Jr():536870912);var e=Tt.current;return e!==null&&(e.flags|=32),At}function bt(e,t,a){(e===je&&(_e===2||_e===9)||e.cancelPendingCommit!==null)&&(zl(e,0),_a(e,ge,At,!1)),Vl(e,a),((Se&2)===0||e!==je)&&(e===je&&((Se&2)===0&&(Za|=a),He===4&&_a(e,ge,At,!1)),Ht(e))}function kf(e,t,a){if((Se&6)!==0)throw Error(u(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Yl(e,t),n=l?Am(e,t):Iu(e,t,!0),i=l;do{if(n===0){Ol&&!l&&_a(e,t,0,!1);break}else{if(a=e.current.alternate,i&&!Em(a)){n=Iu(e,t,!1),i=!1;continue}if(n===2){if(i=t,e.errorRecoveryDisabledLanes&i)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var h=e;n=En;var b=h.current.memoizedState.isDehydrated;if(b&&(zl(h,f).flags|=256),f=Iu(h,f,!1),f!==2){if(Xu&&!b){h.errorRecoveryDisabledLanes|=i,Za|=i,n=4;break e}i=rt,rt=n,i!==null&&(rt===null?rt=i:rt.push.apply(rt,i))}n=f}if(i=!1,n!==2)continue}}if(n===1){zl(e,0),_a(e,t,0,!0);break}e:{switch(l=e,i=n,i){case 0:case 1:throw Error(u(345));case 4:if((t&4194048)!==t)break;case 6:_a(l,t,At,!va);break e;case 2:rt=null;break;case 3:case 5:break;default:throw Error(u(329))}if((t&62914560)===t&&(n=Ku+300-Mt(),10<n)){if(_a(l,t,At,!va),$n(l,0,!0)!==0)break e;l.timeoutHandle=md(Gf.bind(null,l,a,rt,qi,Zu,t,At,Za,El,va,i,2,-0,0),n);break e}Gf(l,a,rt,qi,Zu,t,At,Za,El,va,i,0,-0,0)}}break}while(!0);Ht(e)}function Gf(e,t,a,l,n,i,f,h,b,E,j,D,T,A){if(e.timeoutHandle=-1,D=t.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Dn={stylesheets:null,count:0,unsuspend:sp},Cf(t),D=rp(),D!==null)){e.cancelPendingCommit=D(Jf.bind(null,e,t,i,a,l,n,f,h,b,j,1,T,A)),_a(e,i,f,!E);return}Jf(e,t,i,a,l,n,f,h,b)}function Em(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],i=n.getSnapshot;n=n.value;try{if(!ht(i(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function _a(e,t,a,l){t&=~Qu,t&=~Za,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var i=31-dt(n),f=1<<i;l[i]=-1,n&=~f}a!==0&&Wr(e,a,t)}function Bi(){return(Se&6)===0?(An(0),!1):!0}function Fu(){if(re!==null){if(_e===0)var e=re.return;else e=re,Qt=ka=null,gu(e),xl=null,yn=0,e=re;for(;e!==null;)xf(e.alternate,e),e=e.return;re=null}}function zl(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Vm(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Fu(),je=e,re=a=Yt(e.current,null),ge=t,_e=0,yt=null,va=!1,Ol=Yl(e,t),Xu=!1,El=At=Qu=Za=ba=He=0,rt=En=null,Zu=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-dt(l),i=1<<n;t|=e[n],l&=~i}return It=t,ui(),a}function Yf(e,t){ne=null,z.H=Ti,t===rn||t===pi?(t=lo(),_e=3):t===eo?(t=lo(),_e=4):_e=t===sf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,yt=t,re===null&&(He=1,Ri(e,_t(t,e.current)))}function Vf(){var e=z.H;return z.H=Ti,e===null?Ti:e}function Xf(){var e=z.A;return z.A=Nm,e}function Pu(){He=4,va||(ge&4194048)!==ge&&Tt.current!==null||(Ol=!0),(ba&134217727)===0&&(Za&134217727)===0||je===null||_a(je,ge,At,!1)}function Iu(e,t,a){var l=Se;Se|=2;var n=Vf(),i=Xf();(je!==e||ge!==t)&&(qi=null,zl(e,t)),t=!1;var f=He;e:do try{if(_e!==0&&re!==null){var h=re,b=yt;switch(_e){case 8:Fu(),f=6;break e;case 3:case 2:case 9:case 6:Tt.current===null&&(t=!0);var E=_e;if(_e=0,yt=null,wl(e,h,b,E),a&&Ol){f=0;break e}break;default:E=_e,_e=0,yt=null,wl(e,h,b,E)}}Tm(),f=He;break}catch(j){Yf(e,j)}while(!0);return t&&e.shellSuspendCounter++,Qt=ka=null,Se=l,z.H=n,z.A=i,re===null&&(je=null,ge=0,ui()),f}function Tm(){for(;re!==null;)Qf(re)}function Am(e,t){var a=Se;Se|=2;var l=Vf(),n=Xf();je!==e||ge!==t?(qi=null,Hi=Mt()+500,zl(e,t)):Ol=Yl(e,t);e:do try{if(_e!==0&&re!==null){t=re;var i=yt;t:switch(_e){case 1:_e=0,yt=null,wl(e,t,i,1);break;case 2:case 9:if(to(i)){_e=0,yt=null,Zf(t);break}t=function(){_e!==2&&_e!==9||je!==e||(_e=7),Ht(e)},i.then(t,t);break e;case 3:_e=7;break e;case 4:_e=5;break e;case 7:to(i)?(_e=0,yt=null,Zf(t)):(_e=0,yt=null,wl(e,t,i,7));break;case 5:var f=null;switch(re.tag){case 26:f=re.memoizedState;case 5:case 27:var h=re;if(!f||Ad(f)){_e=0,yt=null;var b=h.sibling;if(b!==null)re=b;else{var E=h.return;E!==null?(re=E,ki(E)):re=null}break t}}_e=0,yt=null,wl(e,t,i,5);break;case 6:_e=0,yt=null,wl(e,t,i,6);break;case 8:Fu(),He=6;break e;default:throw Error(u(462))}}zm();break}catch(j){Yf(e,j)}while(!0);return Qt=ka=null,z.H=l,z.A=n,Se=a,re!==null?0:(je=null,ge=0,ui(),He)}function zm(){for(;re!==null&&!Wh();)Qf(re)}function Qf(e){var t=vf(e.alternate,e,It);e.memoizedProps=e.pendingProps,t===null?ki(e):re=t}function Zf(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=df(a,t,t.pendingProps,t.type,void 0,ge);break;case 11:t=df(a,t,t.pendingProps,t.type.render,t.ref,ge);break;case 5:gu(t);default:xf(a,t),t=re=Qc(t,It),t=vf(a,t,It)}e.memoizedProps=e.pendingProps,t===null?ki(e):re=t}function wl(e,t,a,l){Qt=ka=null,gu(t),xl=null,yn=0;var n=t.return;try{if(ym(e,n,t,a,ge)){He=1,Ri(e,_t(a,e.current)),re=null;return}}catch(i){if(n!==null)throw re=n,i;He=1,Ri(e,_t(a,e.current)),re=null;return}t.flags&32768?(ve||l===1?e=!0:Ol||(ge&536870912)!==0?e=!1:(va=e=!0,(l===2||l===9||l===3||l===6)&&(l=Tt.current,l!==null&&l.tag===13&&(l.flags|=16384))),Kf(t,e)):ki(t)}function ki(e){var t=e;do{if((t.flags&32768)!==0){Kf(t,va);return}e=t.return;var a=bm(t.alternate,t,It);if(a!==null){re=a;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);He===0&&(He=5)}function Kf(e,t){do{var a=xm(e.alternate,e);if(a!==null){a.flags&=32767,re=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){re=e;return}re=e=a}while(e!==null);He=6,re=null}function Jf(e,t,a,l,n,i,f,h,b){e.cancelPendingCommit=null;do Gi();while(Fe!==0);if((Se&6)!==0)throw Error(u(327));if(t!==null){if(t===e.current)throw Error(u(177));if(i=t.lanes|t.childLanes,i|=Vs,sg(e,a,i,f,h,b),e===je&&(re=je=null,ge=0),Tl=t,Sa=e,Al=a,Ju=i,$u=n,qf=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Mm(Zn,function(){return If(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=z.T,z.T=null,n=H.p,H.p=2,f=Se,Se|=4;try{Sm(e,t,a)}finally{Se=f,H.p=n,z.T=l}}Fe=1,$f(),Wf(),Ff()}}function $f(){if(Fe===1){Fe=0;var e=Sa,t=Tl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=Se;Se|=4;try{Rf(t,e);var i=fr,f=Uc(e.containerInfo),h=i.focusedElem,b=i.selectionRange;if(f!==h&&h&&h.ownerDocument&&Cc(h.ownerDocument.documentElement,h)){if(b!==null&&qs(h)){var E=b.start,j=b.end;if(j===void 0&&(j=E),"selectionStart"in h)h.selectionStart=E,h.selectionEnd=Math.min(j,h.value.length);else{var D=h.ownerDocument||document,T=D&&D.defaultView||window;if(T.getSelection){var A=T.getSelection(),P=h.textContent.length,$=Math.min(b.start,P),Te=b.end===void 0?$:Math.min(b.end,P);!A.extend&&$>Te&&(f=Te,Te=$,$=f);var _=Dc(h,$),S=Dc(h,Te);if(_&&S&&(A.rangeCount!==1||A.anchorNode!==_.node||A.anchorOffset!==_.offset||A.focusNode!==S.node||A.focusOffset!==S.offset)){var O=D.createRange();O.setStart(_.node,_.offset),A.removeAllRanges(),$>Te?(A.addRange(O),A.extend(S.node,S.offset)):(O.setEnd(S.node,S.offset),A.addRange(O))}}}}for(D=[],A=h;A=A.parentNode;)A.nodeType===1&&D.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<D.length;h++){var M=D[h];M.element.scrollLeft=M.left,M.element.scrollTop=M.top}}Ii=!!or,fr=or=null}finally{Se=n,H.p=l,z.T=a}}e.current=t,Fe=2}}function Wf(){if(Fe===2){Fe=0;var e=Sa,t=Tl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=Se;Se|=4;try{Af(e,t.alternate,t)}finally{Se=n,H.p=l,z.T=a}}Fe=3}}function Ff(){if(Fe===4||Fe===3){Fe=0,Fh();var e=Sa,t=Tl,a=Al,l=qf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Fe=5:(Fe=0,Tl=Sa=null,Pf(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(xa=null),ys(a),t=t.stateNode,ft&&typeof ft.onCommitFiberRoot=="function")try{ft.onCommitFiberRoot(Gl,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=z.T,n=H.p,H.p=2,z.T=null;try{for(var i=e.onRecoverableError,f=0;f<l.length;f++){var h=l[f];i(h.value,{componentStack:h.stack})}}finally{z.T=t,H.p=n}}(Al&3)!==0&&Gi(),Ht(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Wu?Tn++:(Tn=0,Wu=e):Tn=0,An(0)}}function Pf(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,sn(t)))}function Gi(e){return $f(),Wf(),Ff(),If()}function If(){if(Fe!==5)return!1;var e=Sa,t=Ju;Ju=0;var a=ys(Al),l=z.T,n=H.p;try{H.p=32>a?32:a,z.T=null,a=$u,$u=null;var i=Sa,f=Al;if(Fe=0,Tl=Sa=null,Al=0,(Se&6)!==0)throw Error(u(331));var h=Se;if(Se|=4,Lf(i.current),Df(i,i.current,f,a),Se=h,An(0,!1),ft&&typeof ft.onPostCommitFiberRoot=="function")try{ft.onPostCommitFiberRoot(Gl,i)}catch{}return!0}finally{H.p=n,z.T=l,Pf(e,t)}}function ed(e,t,a){t=_t(a,t),t=zu(e.stateNode,t,2),e=fa(e,t,2),e!==null&&(Vl(e,2),Ht(e))}function ze(e,t,a){if(e.tag===3)ed(e,e,a);else for(;t!==null;){if(t.tag===3){ed(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(xa===null||!xa.has(l))){e=_t(a,e),a=lf(2),l=fa(t,a,2),l!==null&&(nf(a,l,t,e),Vl(l,2),Ht(l));break}}t=t.return}}function er(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Om;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(Xu=!0,n.add(a),e=wm.bind(null,e,t,a),t.then(e,e))}function wm(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,je===e&&(ge&a)===a&&(He===4||He===3&&(ge&62914560)===ge&&300>Mt()-Ku?(Se&2)===0&&zl(e,0):Qu|=a,El===ge&&(El=0)),Ht(e)}function td(e,t){t===0&&(t=$r()),e=ol(e,t),e!==null&&(Vl(e,t),Ht(e))}function jm(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),td(e,a)}function Rm(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(t),td(e,a)}function Mm(e,t){return hs(e,t)}var Yi=null,jl=null,tr=!1,Vi=!1,ar=!1,Ka=0;function Ht(e){e!==jl&&e.next===null&&(jl===null?Yi=jl=e:jl=jl.next=e),Vi=!0,tr||(tr=!0,Cm())}function An(e,t){if(!ar&&Vi){ar=!0;do for(var a=!1,l=Yi;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var i=0;else{var f=l.suspendedLanes,h=l.pingedLanes;i=(1<<31-dt(42|e)+1)-1,i&=n&~(f&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(a=!0,id(l,i))}else i=ge,i=$n(l,l===je?i:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(i&3)===0||Yl(l,i)||(a=!0,id(l,i));l=l.next}while(a);ar=!1}}function Dm(){ad()}function ad(){Vi=tr=!1;var e=0;Ka!==0&&(Ym()&&(e=Ka),Ka=0);for(var t=Mt(),a=null,l=Yi;l!==null;){var n=l.next,i=ld(l,t);i===0?(l.next=null,a===null?Yi=n:a.next=n,n===null&&(jl=a)):(a=l,(e!==0||(i&3)!==0)&&(Vi=!0)),l=n}An(e)}function ld(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var f=31-dt(i),h=1<<f,b=n[f];b===-1?((h&a)===0||(h&l)!==0)&&(n[f]=ig(h,t)):b<=t&&(e.expiredLanes|=h),i&=~h}if(t=je,a=ge,a=$n(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(_e===2||_e===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&gs(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Yl(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&gs(l),ys(a)){case 2:case 8:a=Zr;break;case 32:a=Zn;break;case 268435456:a=Kr;break;default:a=Zn}return l=nd.bind(null,e),a=hs(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&gs(l),e.callbackPriority=2,e.callbackNode=null,2}function nd(e,t){if(Fe!==0&&Fe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Gi()&&e.callbackNode!==a)return null;var l=ge;return l=$n(e,e===je?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(kf(e,l,t),ld(e,Mt()),e.callbackNode!=null&&e.callbackNode===a?nd.bind(null,e):null)}function id(e,t){if(Gi())return null;kf(e,t,!0)}function Cm(){Xm(function(){(Se&6)!==0?hs(Qr,Dm):ad()})}function lr(){return Ka===0&&(Ka=Jr()),Ka}function sd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ei(""+e)}function ud(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Um(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var i=sd((n[nt]||null).action),f=l.submitter;f&&(t=(t=f[nt]||null)?sd(t.formAction):f.getAttribute("formAction"),t!==null&&(i=t,f=null));var h=new ni("action","action",null,l,n);e.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ka!==0){var b=f?ud(n,f):new FormData(n);Nu(a,{pending:!0,data:b,method:n.method,action:i},null,b)}}else typeof i=="function"&&(h.preventDefault(),b=f?ud(n,f):new FormData(n),Nu(a,{pending:!0,data:b,method:n.method,action:i},i,b))},currentTarget:n}]})}}for(var nr=0;nr<Ys.length;nr++){var ir=Ys[nr],Lm=ir.toLowerCase(),Hm=ir[0].toUpperCase()+ir.slice(1);wt(Lm,"on"+Hm)}wt(qc,"onAnimationEnd"),wt(Bc,"onAnimationIteration"),wt(kc,"onAnimationStart"),wt("dblclick","onDoubleClick"),wt("focusin","onFocus"),wt("focusout","onBlur"),wt(em,"onTransitionRun"),wt(tm,"onTransitionStart"),wt(am,"onTransitionCancel"),wt(Gc,"onTransitionEnd"),el("onMouseEnter",["mouseout","mouseover"]),el("onMouseLeave",["mouseout","mouseover"]),el("onPointerEnter",["pointerout","pointerover"]),el("onPointerLeave",["pointerout","pointerover"]),Ra("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ra("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ra("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ra("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),qm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zn));function rd(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var i=void 0;if(t)for(var f=l.length-1;0<=f;f--){var h=l[f],b=h.instance,E=h.currentTarget;if(h=h.listener,b!==i&&n.isPropagationStopped())break e;i=h,n.currentTarget=E;try{i(n)}catch(j){ji(j)}n.currentTarget=null,i=b}else for(f=0;f<l.length;f++){if(h=l[f],b=h.instance,E=h.currentTarget,h=h.listener,b!==i&&n.isPropagationStopped())break e;i=h,n.currentTarget=E;try{i(n)}catch(j){ji(j)}n.currentTarget=null,i=b}}}}function ce(e,t){var a=t[vs];a===void 0&&(a=t[vs]=new Set);var l=e+"__bubble";a.has(l)||(cd(t,e,2,!1),a.add(l))}function sr(e,t,a){var l=0;t&&(l|=4),cd(a,e,l,t)}var Xi="_reactListening"+Math.random().toString(36).slice(2);function ur(e){if(!e[Xi]){e[Xi]=!0,ec.forEach(function(a){a!=="selectionchange"&&(qm.has(a)||sr(a,!1,e),sr(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Xi]||(t[Xi]=!0,sr("selectionchange",!1,t))}}function cd(e,t,a,l){switch(Dd(t)){case 2:var n=fp;break;case 8:n=dp;break;default:n=Sr}a=n.bind(null,t,a,e),n=void 0,!ws||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function rr(e,t,a,l,n){var i=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var h=l.stateNode.containerInfo;if(h===n)break;if(f===4)for(f=l.return;f!==null;){var b=f.tag;if((b===3||b===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;h!==null;){if(f=Fa(h),f===null)return;if(b=f.tag,b===5||b===6||b===26||b===27){l=i=f;continue e}h=h.parentNode}}l=l.return}gc(function(){var E=i,j=As(a),D=[];e:{var T=Yc.get(e);if(T!==void 0){var A=ni,P=e;switch(e){case"keypress":if(ai(a)===0)break e;case"keydown":case"keyup":A=Mg;break;case"focusin":P="focus",A=Ds;break;case"focusout":P="blur",A=Ds;break;case"beforeblur":case"afterblur":A=Ds;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=yc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=xg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Ug;break;case qc:case Bc:case kc:A=Ng;break;case Gc:A=Hg;break;case"scroll":case"scrollend":A=vg;break;case"wheel":A=Bg;break;case"copy":case"cut":case"paste":A=Eg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=bc;break;case"toggle":case"beforetoggle":A=Gg}var $=(t&4)!==0,Te=!$&&(e==="scroll"||e==="scrollend"),_=$?T!==null?T+"Capture":null:T;$=[];for(var S=E,O;S!==null;){var M=S;if(O=M.stateNode,M=M.tag,M!==5&&M!==26&&M!==27||O===null||_===null||(M=Zl(S,_),M!=null&&$.push(wn(S,M,O))),Te)break;S=S.return}0<$.length&&(T=new A(T,P,null,a,j),D.push({event:T,listeners:$}))}}if((t&7)===0){e:{if(T=e==="mouseover"||e==="pointerover",A=e==="mouseout"||e==="pointerout",T&&a!==Ts&&(P=a.relatedTarget||a.fromElement)&&(Fa(P)||P[Wa]))break e;if((A||T)&&(T=j.window===j?j:(T=j.ownerDocument)?T.defaultView||T.parentWindow:window,A?(P=a.relatedTarget||a.toElement,A=E,P=P?Fa(P):null,P!==null&&(Te=d(P),$=P.tag,P!==Te||$!==5&&$!==27&&$!==6)&&(P=null)):(A=null,P=E),A!==P)){if($=yc,M="onMouseLeave",_="onMouseEnter",S="mouse",(e==="pointerout"||e==="pointerover")&&($=bc,M="onPointerLeave",_="onPointerEnter",S="pointer"),Te=A==null?T:Ql(A),O=P==null?T:Ql(P),T=new $(M,S+"leave",A,a,j),T.target=Te,T.relatedTarget=O,M=null,Fa(j)===E&&($=new $(_,S+"enter",P,a,j),$.target=O,$.relatedTarget=Te,M=$),Te=M,A&&P)t:{for($=A,_=P,S=0,O=$;O;O=Rl(O))S++;for(O=0,M=_;M;M=Rl(M))O++;for(;0<S-O;)$=Rl($),S--;for(;0<O-S;)_=Rl(_),O--;for(;S--;){if($===_||_!==null&&$===_.alternate)break t;$=Rl($),_=Rl(_)}$=null}else $=null;A!==null&&od(D,T,A,$,!1),P!==null&&Te!==null&&od(D,Te,P,$,!0)}}e:{if(T=E?Ql(E):window,A=T.nodeName&&T.nodeName.toLowerCase(),A==="select"||A==="input"&&T.type==="file")var V=Ac;else if(Ec(T))if(zc)V=Fg;else{V=$g;var se=Jg}else A=T.nodeName,!A||A.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?E&&Es(E.elementType)&&(V=Ac):V=Wg;if(V&&(V=V(e,E))){Tc(D,V,a,j);break e}se&&se(e,T,E),e==="focusout"&&E&&T.type==="number"&&E.memoizedProps.value!=null&&Os(T,"number",T.value)}switch(se=E?Ql(E):window,e){case"focusin":(Ec(se)||se.contentEditable==="true")&&(ul=se,Bs=E,en=null);break;case"focusout":en=Bs=ul=null;break;case"mousedown":ks=!0;break;case"contextmenu":case"mouseup":case"dragend":ks=!1,Lc(D,a,j);break;case"selectionchange":if(Ig)break;case"keydown":case"keyup":Lc(D,a,j)}var K;if(Us)e:{switch(e){case"compositionstart":var W="onCompositionStart";break e;case"compositionend":W="onCompositionEnd";break e;case"compositionupdate":W="onCompositionUpdate";break e}W=void 0}else sl?Nc(e,a)&&(W="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(W="onCompositionStart");W&&(xc&&a.locale!=="ko"&&(sl||W!=="onCompositionStart"?W==="onCompositionEnd"&&sl&&(K=mc()):(ua=j,js="value"in ua?ua.value:ua.textContent,sl=!0)),se=Qi(E,W),0<se.length&&(W=new vc(W,e,null,a,j),D.push({event:W,listeners:se}),K?W.data=K:(K=Oc(a),K!==null&&(W.data=K)))),(K=Vg?Xg(e,a):Qg(e,a))&&(W=Qi(E,"onBeforeInput"),0<W.length&&(se=new vc("onBeforeInput","beforeinput",null,a,j),D.push({event:se,listeners:W}),se.data=K)),Um(D,e,E,a,j)}rd(D,t)})}function wn(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Qi(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=Zl(e,a),n!=null&&l.unshift(wn(e,n,i)),n=Zl(e,t),n!=null&&l.push(wn(e,n,i))),e.tag===3)return l;e=e.return}return[]}function Rl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function od(e,t,a,l,n){for(var i=t._reactName,f=[];a!==null&&a!==l;){var h=a,b=h.alternate,E=h.stateNode;if(h=h.tag,b!==null&&b===l)break;h!==5&&h!==26&&h!==27||E===null||(b=E,n?(E=Zl(a,i),E!=null&&f.unshift(wn(a,E,b))):n||(E=Zl(a,i),E!=null&&f.push(wn(a,E,b)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var Bm=/\r\n?/g,km=/\u0000|\uFFFD/g;function fd(e){return(typeof e=="string"?e:""+e).replace(Bm,`
`).replace(km,"")}function dd(e,t){return t=fd(t),fd(e)===t}function Zi(){}function Ee(e,t,a,l,n,i){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||ll(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&ll(e,""+l);break;case"className":Fn(e,"class",l);break;case"tabIndex":Fn(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Fn(e,a,l);break;case"style":dc(e,l,i);break;case"data":if(t!=="object"){Fn(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=ei(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(a==="formAction"?(t!=="input"&&Ee(e,t,"name",n.name,n,null),Ee(e,t,"formEncType",n.formEncType,n,null),Ee(e,t,"formMethod",n.formMethod,n,null),Ee(e,t,"formTarget",n.formTarget,n,null)):(Ee(e,t,"encType",n.encType,n,null),Ee(e,t,"method",n.method,n,null),Ee(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=ei(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=Zi);break;case"onScroll":l!=null&&ce("scroll",e);break;case"onScrollEnd":l!=null&&ce("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=ei(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":ce("beforetoggle",e),ce("toggle",e),Wn(e,"popover",l);break;case"xlinkActuate":kt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":kt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":kt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":kt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":kt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":kt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":kt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":kt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":kt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Wn(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=pg.get(a)||a,Wn(e,a,l))}}function cr(e,t,a,l,n,i){switch(a){case"style":dc(e,l,i);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(u(60));e.innerHTML=a}}break;case"children":typeof l=="string"?ll(e,l):(typeof l=="number"||typeof l=="bigint")&&ll(e,""+l);break;case"onScroll":l!=null&&ce("scroll",e);break;case"onScrollEnd":l!=null&&ce("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Zi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!tc.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),i=e[nt]||null,i=i!=null?i[a]:null,typeof i=="function"&&e.removeEventListener(t,i,n),typeof l=="function")){typeof i!="function"&&i!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):Wn(e,a,l)}}}function Pe(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ce("error",e),ce("load",e);var l=!1,n=!1,i;for(i in a)if(a.hasOwnProperty(i)){var f=a[i];if(f!=null)switch(i){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ee(e,t,i,f,a,null)}}n&&Ee(e,t,"srcSet",a.srcSet,a,null),l&&Ee(e,t,"src",a.src,a,null);return;case"input":ce("invalid",e);var h=i=f=n=null,b=null,E=null;for(l in a)if(a.hasOwnProperty(l)){var j=a[l];if(j!=null)switch(l){case"name":n=j;break;case"type":f=j;break;case"checked":b=j;break;case"defaultChecked":E=j;break;case"value":i=j;break;case"defaultValue":h=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(u(137,t));break;default:Ee(e,t,l,j,a,null)}}rc(e,i,h,b,E,f,n,!1),Pn(e);return;case"select":ce("invalid",e),l=f=i=null;for(n in a)if(a.hasOwnProperty(n)&&(h=a[n],h!=null))switch(n){case"value":i=h;break;case"defaultValue":f=h;break;case"multiple":l=h;default:Ee(e,t,n,h,a,null)}t=i,a=f,e.multiple=!!l,t!=null?al(e,!!l,t,!1):a!=null&&al(e,!!l,a,!0);return;case"textarea":ce("invalid",e),i=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(h=a[f],h!=null))switch(f){case"value":l=h;break;case"defaultValue":n=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(u(91));break;default:Ee(e,t,f,h,a,null)}oc(e,l,n,i),Pn(e);return;case"option":for(b in a)if(a.hasOwnProperty(b)&&(l=a[b],l!=null))switch(b){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ee(e,t,b,l,a,null)}return;case"dialog":ce("beforetoggle",e),ce("toggle",e),ce("cancel",e),ce("close",e);break;case"iframe":case"object":ce("load",e);break;case"video":case"audio":for(l=0;l<zn.length;l++)ce(zn[l],e);break;case"image":ce("error",e),ce("load",e);break;case"details":ce("toggle",e);break;case"embed":case"source":case"link":ce("error",e),ce("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in a)if(a.hasOwnProperty(E)&&(l=a[E],l!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,t));default:Ee(e,t,E,l,a,null)}return;default:if(Es(t)){for(j in a)a.hasOwnProperty(j)&&(l=a[j],l!==void 0&&cr(e,t,j,l,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(l=a[h],l!=null&&Ee(e,t,h,l,a,null))}function Gm(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,f=null,h=null,b=null,E=null,j=null;for(A in a){var D=a[A];if(a.hasOwnProperty(A)&&D!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":b=D;default:l.hasOwnProperty(A)||Ee(e,t,A,null,l,D)}}for(var T in l){var A=l[T];if(D=a[T],l.hasOwnProperty(T)&&(A!=null||D!=null))switch(T){case"type":i=A;break;case"name":n=A;break;case"checked":E=A;break;case"defaultChecked":j=A;break;case"value":f=A;break;case"defaultValue":h=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(u(137,t));break;default:A!==D&&Ee(e,t,T,A,l,D)}}Ns(e,f,h,b,E,j,i,n);return;case"select":A=f=h=T=null;for(i in a)if(b=a[i],a.hasOwnProperty(i)&&b!=null)switch(i){case"value":break;case"multiple":A=b;default:l.hasOwnProperty(i)||Ee(e,t,i,null,l,b)}for(n in l)if(i=l[n],b=a[n],l.hasOwnProperty(n)&&(i!=null||b!=null))switch(n){case"value":T=i;break;case"defaultValue":h=i;break;case"multiple":f=i;default:i!==b&&Ee(e,t,n,i,l,b)}t=h,a=f,l=A,T!=null?al(e,!!a,T,!1):!!l!=!!a&&(t!=null?al(e,!!a,t,!0):al(e,!!a,a?[]:"",!1));return;case"textarea":A=T=null;for(h in a)if(n=a[h],a.hasOwnProperty(h)&&n!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Ee(e,t,h,null,l,n)}for(f in l)if(n=l[f],i=a[f],l.hasOwnProperty(f)&&(n!=null||i!=null))switch(f){case"value":T=n;break;case"defaultValue":A=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(u(91));break;default:n!==i&&Ee(e,t,f,n,l,i)}cc(e,T,A);return;case"option":for(var P in a)if(T=a[P],a.hasOwnProperty(P)&&T!=null&&!l.hasOwnProperty(P))switch(P){case"selected":e.selected=!1;break;default:Ee(e,t,P,null,l,T)}for(b in l)if(T=l[b],A=a[b],l.hasOwnProperty(b)&&T!==A&&(T!=null||A!=null))switch(b){case"selected":e.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:Ee(e,t,b,T,l,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var $ in a)T=a[$],a.hasOwnProperty($)&&T!=null&&!l.hasOwnProperty($)&&Ee(e,t,$,null,l,T);for(E in l)if(T=l[E],A=a[E],l.hasOwnProperty(E)&&T!==A&&(T!=null||A!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(u(137,t));break;default:Ee(e,t,E,T,l,A)}return;default:if(Es(t)){for(var Te in a)T=a[Te],a.hasOwnProperty(Te)&&T!==void 0&&!l.hasOwnProperty(Te)&&cr(e,t,Te,void 0,l,T);for(j in l)T=l[j],A=a[j],!l.hasOwnProperty(j)||T===A||T===void 0&&A===void 0||cr(e,t,j,T,l,A);return}}for(var _ in a)T=a[_],a.hasOwnProperty(_)&&T!=null&&!l.hasOwnProperty(_)&&Ee(e,t,_,null,l,T);for(D in l)T=l[D],A=a[D],!l.hasOwnProperty(D)||T===A||T==null&&A==null||Ee(e,t,D,T,l,A)}var or=null,fr=null;function Ki(e){return e.nodeType===9?e:e.ownerDocument}function hd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function dr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var hr=null;function Ym(){var e=window.event;return e&&e.type==="popstate"?e===hr?!1:(hr=e,!0):(hr=null,!1)}var md=typeof setTimeout=="function"?setTimeout:void 0,Vm=typeof clearTimeout=="function"?clearTimeout:void 0,pd=typeof Promise=="function"?Promise:void 0,Xm=typeof queueMicrotask=="function"?queueMicrotask:typeof pd<"u"?function(e){return pd.resolve(null).then(e).catch(Qm)}:md;function Qm(e){setTimeout(function(){throw e})}function Na(e){return e==="head"}function yd(e,t){var a=t,l=0,n=0;do{var i=a.nextSibling;if(e.removeChild(a),i&&i.nodeType===8)if(a=i.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&jn(f.documentElement),a&2&&jn(f.body),a&4)for(a=f.head,jn(a),f=a.firstChild;f;){var h=f.nextSibling,b=f.nodeName;f[Xl]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=h}}if(n===0){e.removeChild(i),qn(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=i}while(a);qn(t)}function gr(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":gr(a),bs(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Zm(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Xl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=Rt(e.nextSibling),e===null)break}return null}function Km(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Rt(e.nextSibling),e===null))return null;return e}function mr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Jm(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var pr=null;function vd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function bd(e,t,a){switch(t=Ki(a),e){case"html":if(e=t.documentElement,!e)throw Error(u(452));return e;case"head":if(e=t.head,!e)throw Error(u(453));return e;case"body":if(e=t.body,!e)throw Error(u(454));return e;default:throw Error(u(451))}}function jn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);bs(e)}var zt=new Map,xd=new Set;function Ji(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ea=H.d;H.d={f:$m,r:Wm,D:Fm,C:Pm,L:Im,m:ep,X:ap,S:tp,M:lp};function $m(){var e=ea.f(),t=Bi();return e||t}function Wm(e){var t=Pa(e);t!==null&&t.tag===5&&t.type==="form"?Bo(t):ea.r(e)}var Ml=typeof document>"u"?null:document;function Sd(e,t,a){var l=Ml;if(l&&typeof t=="string"&&t){var n=St(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),xd.has(n)||(xd.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function Fm(e){ea.D(e),Sd("dns-prefetch",e,null)}function Pm(e,t){ea.C(e,t),Sd("preconnect",e,t)}function Im(e,t,a){ea.L(e,t,a);var l=Ml;if(l&&e&&t){var n='link[rel="preload"][as="'+St(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+St(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+St(a.imageSizes)+'"]')):n+='[href="'+St(e)+'"]';var i=n;switch(t){case"style":i=Dl(e);break;case"script":i=Cl(e)}zt.has(i)||(e=N({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),zt.set(i,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Rn(i))||t==="script"&&l.querySelector(Mn(i))||(t=l.createElement("link"),Pe(t,"link",e),Ze(t),l.head.appendChild(t)))}}function ep(e,t){ea.m(e,t);var a=Ml;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+St(l)+'"][href="'+St(e)+'"]',i=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Cl(e)}if(!zt.has(i)&&(e=N({rel:"modulepreload",href:e},t),zt.set(i,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Mn(i)))return}l=a.createElement("link"),Pe(l,"link",e),Ze(l),a.head.appendChild(l)}}}function tp(e,t,a){ea.S(e,t,a);var l=Ml;if(l&&e){var n=Ia(l).hoistableStyles,i=Dl(e);t=t||"default";var f=n.get(i);if(!f){var h={loading:0,preload:null};if(f=l.querySelector(Rn(i)))h.loading=5;else{e=N({rel:"stylesheet",href:e,"data-precedence":t},a),(a=zt.get(i))&&yr(e,a);var b=f=l.createElement("link");Ze(b),Pe(b,"link",e),b._p=new Promise(function(E,j){b.onload=E,b.onerror=j}),b.addEventListener("load",function(){h.loading|=1}),b.addEventListener("error",function(){h.loading|=2}),h.loading|=4,$i(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:h},n.set(i,f)}}}function ap(e,t){ea.X(e,t);var a=Ml;if(a&&e){var l=Ia(a).hoistableScripts,n=Cl(e),i=l.get(n);i||(i=a.querySelector(Mn(n)),i||(e=N({src:e,async:!0},t),(t=zt.get(n))&&vr(e,t),i=a.createElement("script"),Ze(i),Pe(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function lp(e,t){ea.M(e,t);var a=Ml;if(a&&e){var l=Ia(a).hoistableScripts,n=Cl(e),i=l.get(n);i||(i=a.querySelector(Mn(n)),i||(e=N({src:e,async:!0,type:"module"},t),(t=zt.get(n))&&vr(e,t),i=a.createElement("script"),Ze(i),Pe(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function _d(e,t,a,l){var n=(n=I.current)?Ji(n):null;if(!n)throw Error(u(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Dl(a.href),a=Ia(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Dl(a.href);var i=Ia(n).hoistableStyles,f=i.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,f),(i=n.querySelector(Rn(e)))&&!i._p&&(f.instance=i,f.state.loading=5),zt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},zt.set(e,a),i||np(n,e,a,f.state))),t&&l===null)throw Error(u(528,""));return f}if(t&&l!==null)throw Error(u(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Cl(a),a=Ia(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,e))}}function Dl(e){return'href="'+St(e)+'"'}function Rn(e){return'link[rel="stylesheet"]['+e+"]"}function Nd(e){return N({},e,{"data-precedence":e.precedence,precedence:null})}function np(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Pe(t,"link",a),Ze(t),e.head.appendChild(t))}function Cl(e){return'[src="'+St(e)+'"]'}function Mn(e){return"script[async]"+e}function Od(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+St(a.href)+'"]');if(l)return t.instance=l,Ze(l),l;var n=N({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ze(l),Pe(l,"style",n),$i(l,a.precedence,e),t.instance=l;case"stylesheet":n=Dl(a.href);var i=e.querySelector(Rn(n));if(i)return t.state.loading|=4,t.instance=i,Ze(i),i;l=Nd(a),(n=zt.get(n))&&yr(l,n),i=(e.ownerDocument||e).createElement("link"),Ze(i);var f=i;return f._p=new Promise(function(h,b){f.onload=h,f.onerror=b}),Pe(i,"link",l),t.state.loading|=4,$i(i,a.precedence,e),t.instance=i;case"script":return i=Cl(a.src),(n=e.querySelector(Mn(i)))?(t.instance=n,Ze(n),n):(l=a,(n=zt.get(i))&&(l=N({},a),vr(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ze(n),Pe(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(u(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,$i(l,a.precedence,e));return t.instance}function $i(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,i=n,f=0;f<l.length;f++){var h=l[f];if(h.dataset.precedence===t)i=h;else if(i!==n)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function yr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function vr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Wi=null;function Ed(e,t,a){if(Wi===null){var l=new Map,n=Wi=new Map;n.set(a,l)}else n=Wi,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var i=a[n];if(!(i[Xl]||i[et]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(t)||"";f=e+f;var h=l.get(f);h?h.push(i):l.set(f,[i])}}return l}function Td(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function ip(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Ad(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Dn=null;function sp(){}function up(e,t,a){if(Dn===null)throw Error(u(475));var l=Dn;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Dl(a.href),i=e.querySelector(Rn(n));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Fi.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=i,Ze(i);return}i=e.ownerDocument||e,a=Nd(a),(n=zt.get(n))&&yr(a,n),i=i.createElement("link"),Ze(i);var f=i;f._p=new Promise(function(h,b){f.onload=h,f.onerror=b}),Pe(i,"link",a),t.instance=i}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Fi.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function rp(){if(Dn===null)throw Error(u(475));var e=Dn;return e.stylesheets&&e.count===0&&br(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&br(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Fi(){if(this.count--,this.count===0){if(this.stylesheets)br(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Pi=null;function br(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Pi=new Map,t.forEach(cp,e),Pi=null,Fi.call(e))}function cp(e,t){if(!(t.state.loading&4)){var a=Pi.get(e);if(a)var l=a.get(null);else{a=new Map,Pi.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var f=n[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),i=a.get(f)||l,i===l&&a.set(null,n),a.set(f,n),this.count++,l=Fi.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),i?i.parentNode.insertBefore(n,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Cn={$$typeof:ue,Provider:null,Consumer:null,_currentValue:C,_currentValue2:C,_threadCount:0};function op(e,t,a,l,n,i,f,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ms(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ms(0),this.hiddenUpdates=ms(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function zd(e,t,a,l,n,i,f,h,b,E,j,D){return e=new op(e,t,a,f,h,b,E,D),t=1,i===!0&&(t|=24),i=gt(3,null,null,t),e.current=i,i.stateNode=e,t=eu(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:l,isDehydrated:a,cache:t},nu(i),e}function wd(e){return e?(e=fl,e):fl}function jd(e,t,a,l,n,i){n=wd(n),l.context===null?l.context=n:l.pendingContext=n,l=oa(t),l.payload={element:a},i=i===void 0?null:i,i!==null&&(l.callback=i),a=fa(e,l,t),a!==null&&(bt(a,e,t),on(a,e,t))}function Rd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function xr(e,t){Rd(e,t),(e=e.alternate)&&Rd(e,t)}function Md(e){if(e.tag===13){var t=ol(e,67108864);t!==null&&bt(t,e,67108864),xr(e,67108864)}}var Ii=!0;function fp(e,t,a,l){var n=z.T;z.T=null;var i=H.p;try{H.p=2,Sr(e,t,a,l)}finally{H.p=i,z.T=n}}function dp(e,t,a,l){var n=z.T;z.T=null;var i=H.p;try{H.p=8,Sr(e,t,a,l)}finally{H.p=i,z.T=n}}function Sr(e,t,a,l){if(Ii){var n=_r(l);if(n===null)rr(e,t,l,es,a),Cd(e,l);else if(gp(n,e,t,a,l))l.stopPropagation();else if(Cd(e,l),t&4&&-1<hp.indexOf(e)){for(;n!==null;){var i=Pa(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=ja(i.pendingLanes);if(f!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var b=1<<31-dt(f);h.entanglements[1]|=b,f&=~b}Ht(i),(Se&6)===0&&(Hi=Mt()+500,An(0))}}break;case 13:h=ol(i,2),h!==null&&bt(h,i,2),Bi(),xr(i,2)}if(i=_r(l),i===null&&rr(e,t,l,es,a),i===n)break;n=i}n!==null&&l.stopPropagation()}else rr(e,t,l,null,a)}}function _r(e){return e=As(e),Nr(e)}var es=null;function Nr(e){if(es=null,e=Fa(e),e!==null){var t=d(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=m(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return es=e,null}function Dd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ph()){case Qr:return 2;case Zr:return 8;case Zn:case Ih:return 32;case Kr:return 268435456;default:return 32}default:return 32}}var Or=!1,Oa=null,Ea=null,Ta=null,Un=new Map,Ln=new Map,Aa=[],hp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Cd(e,t){switch(e){case"focusin":case"focusout":Oa=null;break;case"dragenter":case"dragleave":Ea=null;break;case"mouseover":case"mouseout":Ta=null;break;case"pointerover":case"pointerout":Un.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ln.delete(t.pointerId)}}function Hn(e,t,a,l,n,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:i,targetContainers:[n]},t!==null&&(t=Pa(t),t!==null&&Md(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function gp(e,t,a,l,n){switch(t){case"focusin":return Oa=Hn(Oa,e,t,a,l,n),!0;case"dragenter":return Ea=Hn(Ea,e,t,a,l,n),!0;case"mouseover":return Ta=Hn(Ta,e,t,a,l,n),!0;case"pointerover":var i=n.pointerId;return Un.set(i,Hn(Un.get(i)||null,e,t,a,l,n)),!0;case"gotpointercapture":return i=n.pointerId,Ln.set(i,Hn(Ln.get(i)||null,e,t,a,l,n)),!0}return!1}function Ud(e){var t=Fa(e.target);if(t!==null){var a=d(t);if(a!==null){if(t=a.tag,t===13){if(t=m(a),t!==null){e.blockedOn=t,ug(e.priority,function(){if(a.tag===13){var l=vt();l=ps(l);var n=ol(a,l);n!==null&&bt(n,a,l),xr(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ts(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=_r(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Ts=l,a.target.dispatchEvent(l),Ts=null}else return t=Pa(a),t!==null&&Md(t),e.blockedOn=a,!1;t.shift()}return!0}function Ld(e,t,a){ts(e)&&a.delete(t)}function mp(){Or=!1,Oa!==null&&ts(Oa)&&(Oa=null),Ea!==null&&ts(Ea)&&(Ea=null),Ta!==null&&ts(Ta)&&(Ta=null),Un.forEach(Ld),Ln.forEach(Ld)}function as(e,t){e.blockedOn===t&&(e.blockedOn=null,Or||(Or=!0,c.unstable_scheduleCallback(c.unstable_NormalPriority,mp)))}var ls=null;function Hd(e){ls!==e&&(ls=e,c.unstable_scheduleCallback(c.unstable_NormalPriority,function(){ls===e&&(ls=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(Nr(l||a)===null)continue;break}var i=Pa(a);i!==null&&(e.splice(t,3),t-=3,Nu(i,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function qn(e){function t(b){return as(b,e)}Oa!==null&&as(Oa,e),Ea!==null&&as(Ea,e),Ta!==null&&as(Ta,e),Un.forEach(t),Ln.forEach(t);for(var a=0;a<Aa.length;a++){var l=Aa[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Aa.length&&(a=Aa[0],a.blockedOn===null);)Ud(a),a.blockedOn===null&&Aa.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],i=a[l+1],f=n[nt]||null;if(typeof i=="function")f||Hd(a);else if(f){var h=null;if(i&&i.hasAttribute("formAction")){if(n=i,f=i[nt]||null)h=f.formAction;else if(Nr(n)!==null)continue}else h=f.action;typeof h=="function"?a[l+1]=h:(a.splice(l,3),l-=3),Hd(a)}}}function Er(e){this._internalRoot=e}ns.prototype.render=Er.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(u(409));var a=t.current,l=vt();jd(a,l,e,t,null,null)},ns.prototype.unmount=Er.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;jd(e.current,2,null,e,null,null),Bi(),t[Wa]=null}};function ns(e){this._internalRoot=e}ns.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pr();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Aa.length&&t!==0&&t<Aa[a].priority;a++);Aa.splice(a,0,e),a===0&&Ud(e)}};var qd=s.version;if(qd!=="19.1.1")throw Error(u(527,qd,"19.1.1"));H.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(u(188)):(e=Object.keys(e).join(","),Error(u(268,e)));return e=v(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var pp={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var is=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!is.isDisabled&&is.supportsFiber)try{Gl=is.inject(pp),ft=is}catch{}}return kn.createRoot=function(e,t){if(!o(e))throw Error(u(299));var a=!1,l="",n=Io,i=ef,f=tf,h=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=zd(e,1,!1,null,null,a,l,n,i,f,h,null),e[Wa]=t.current,ur(e),new Er(t)},kn.hydrateRoot=function(e,t,a){if(!o(e))throw Error(u(299));var l=!1,n="",i=Io,f=ef,h=tf,b=null,E=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(i=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(b=a.unstable_transitionCallbacks),a.formState!==void 0&&(E=a.formState)),t=zd(e,1,!0,t,a??null,l,n,i,f,h,b,E),t.context=wd(null),a=t.current,l=vt(),l=ps(l),n=oa(l),n.callback=null,fa(a,n,l),a=l,t.current.lanes=a,Vl(t,a),Ht(t),e[Wa]=t.current,ur(e),new ns(t)},kn.version="19.1.1",kn}var Jd;function Tp(){if(Jd)return zr.exports;Jd=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),zr.exports=Ep(),zr.exports}var Ap=Tp();const ee=c=>typeof c=="string",Gn=()=>{let c,s;const r=new Promise((u,o)=>{c=u,s=o});return r.resolve=c,r.reject=s,r},$d=c=>c==null?"":""+c,zp=(c,s,r)=>{c.forEach(u=>{s[u]&&(r[u]=s[u])})},wp=/###/g,Wd=c=>c&&c.indexOf("###")>-1?c.replace(wp,"."):c,Fd=c=>!c||ee(c),Vn=(c,s,r)=>{const u=ee(s)?s.split("."):s;let o=0;for(;o<u.length-1;){if(Fd(c))return{};const d=Wd(u[o]);!c[d]&&r&&(c[d]=new r),Object.prototype.hasOwnProperty.call(c,d)?c=c[d]:c={},++o}return Fd(c)?{}:{obj:c,k:Wd(u[o])}},Pd=(c,s,r)=>{const{obj:u,k:o}=Vn(c,s,Object);if(u!==void 0||s.length===1){u[o]=r;return}let d=s[s.length-1],m=s.slice(0,s.length-1),y=Vn(c,m,Object);for(;y.obj===void 0&&m.length;)d=`${m[m.length-1]}.${d}`,m=m.slice(0,m.length-1),y=Vn(c,m,Object),y!=null&&y.obj&&typeof y.obj[`${y.k}.${d}`]<"u"&&(y.obj=void 0);y.obj[`${y.k}.${d}`]=r},jp=(c,s,r,u)=>{const{obj:o,k:d}=Vn(c,s,Object);o[d]=o[d]||[],o[d].push(r)},cs=(c,s)=>{const{obj:r,k:u}=Vn(c,s);if(r&&Object.prototype.hasOwnProperty.call(r,u))return r[u]},Rp=(c,s,r)=>{const u=cs(c,r);return u!==void 0?u:cs(s,r)},Nh=(c,s,r)=>{for(const u in s)u!=="__proto__"&&u!=="constructor"&&(u in c?ee(c[u])||c[u]instanceof String||ee(s[u])||s[u]instanceof String?r&&(c[u]=s[u]):Nh(c[u],s[u],r):c[u]=s[u]);return c},Ul=c=>c.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Mp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Dp=c=>ee(c)?c.replace(/[&<>"'\/]/g,s=>Mp[s]):c;class Cp{constructor(s){this.capacity=s,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(s){const r=this.regExpMap.get(s);if(r!==void 0)return r;const u=new RegExp(s);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(s,u),this.regExpQueue.push(s),u}}const Up=[" ",",","?","!",";"],Lp=new Cp(20),Hp=(c,s,r)=>{s=s||"",r=r||"";const u=Up.filter(m=>s.indexOf(m)<0&&r.indexOf(m)<0);if(u.length===0)return!0;const o=Lp.getRegExp(`(${u.map(m=>m==="?"?"\\?":m).join("|")})`);let d=!o.test(c);if(!d){const m=c.indexOf(r);m>0&&!o.test(c.substring(0,m))&&(d=!0)}return d},Ur=(c,s,r=".")=>{if(!c)return;if(c[s])return Object.prototype.hasOwnProperty.call(c,s)?c[s]:void 0;const u=s.split(r);let o=c;for(let d=0;d<u.length;){if(!o||typeof o!="object")return;let m,y="";for(let v=d;v<u.length;++v)if(v!==d&&(y+=r),y+=u[v],m=o[y],m!==void 0){if(["string","number","boolean"].indexOf(typeof m)>-1&&v<u.length-1)continue;d+=v-d+1;break}o=m}return o},Xn=c=>c==null?void 0:c.replace("_","-"),qp={type:"logger",log(c){this.output("log",c)},warn(c){this.output("warn",c)},error(c){this.output("error",c)},output(c,s){var r,u;(u=(r=console==null?void 0:console[c])==null?void 0:r.apply)==null||u.call(r,console,s)}};class os{constructor(s,r={}){this.init(s,r)}init(s,r={}){this.prefix=r.prefix||"i18next:",this.logger=s||qp,this.options=r,this.debug=r.debug}log(...s){return this.forward(s,"log","",!0)}warn(...s){return this.forward(s,"warn","",!0)}error(...s){return this.forward(s,"error","")}deprecate(...s){return this.forward(s,"warn","WARNING DEPRECATED: ",!0)}forward(s,r,u,o){return o&&!this.debug?null:(ee(s[0])&&(s[0]=`${u}${this.prefix} ${s[0]}`),this.logger[r](s))}create(s){return new os(this.logger,{prefix:`${this.prefix}:${s}:`,...this.options})}clone(s){return s=s||this.options,s.prefix=s.prefix||this.prefix,new os(this.logger,s)}}var qt=new os;class ds{constructor(){this.observers={}}on(s,r){return s.split(" ").forEach(u=>{this.observers[u]||(this.observers[u]=new Map);const o=this.observers[u].get(r)||0;this.observers[u].set(r,o+1)}),this}off(s,r){if(this.observers[s]){if(!r){delete this.observers[s];return}this.observers[s].delete(r)}}emit(s,...r){this.observers[s]&&Array.from(this.observers[s].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o(...r)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o.apply(o,[s,...r])})}}class Id extends ds{constructor(s,r={ns:["translation"],defaultNS:"translation"}){super(),this.data=s||{},this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(s){this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}removeNamespaces(s){const r=this.options.ns.indexOf(s);r>-1&&this.options.ns.splice(r,1)}getResource(s,r,u,o={}){var g,N;const d=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,m=o.ignoreJSONStructure!==void 0?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let y;s.indexOf(".")>-1?y=s.split("."):(y=[s,r],u&&(Array.isArray(u)?y.push(...u):ee(u)&&d?y.push(...u.split(d)):y.push(u)));const v=cs(this.data,y);return!v&&!r&&!u&&s.indexOf(".")>-1&&(s=y[0],r=y[1],u=y.slice(2).join(".")),v||!m||!ee(u)?v:Ur((N=(g=this.data)==null?void 0:g[s])==null?void 0:N[r],u,d)}addResource(s,r,u,o,d={silent:!1}){const m=d.keySeparator!==void 0?d.keySeparator:this.options.keySeparator;let y=[s,r];u&&(y=y.concat(m?u.split(m):u)),s.indexOf(".")>-1&&(y=s.split("."),o=r,r=y[1]),this.addNamespaces(r),Pd(this.data,y,o),d.silent||this.emit("added",s,r,u,o)}addResources(s,r,u,o={silent:!1}){for(const d in u)(ee(u[d])||Array.isArray(u[d]))&&this.addResource(s,r,d,u[d],{silent:!0});o.silent||this.emit("added",s,r,u)}addResourceBundle(s,r,u,o,d,m={silent:!1,skipCopy:!1}){let y=[s,r];s.indexOf(".")>-1&&(y=s.split("."),o=u,u=r,r=y[1]),this.addNamespaces(r);let v=cs(this.data,y)||{};m.skipCopy||(u=JSON.parse(JSON.stringify(u))),o?Nh(v,u,d):v={...v,...u},Pd(this.data,y,v),m.silent||this.emit("added",s,r,u)}removeResourceBundle(s,r){this.hasResourceBundle(s,r)&&delete this.data[s][r],this.removeNamespaces(r),this.emit("removed",s,r)}hasResourceBundle(s,r){return this.getResource(s,r)!==void 0}getResourceBundle(s,r){return r||(r=this.options.defaultNS),this.getResource(s,r)}getDataByLanguage(s){return this.data[s]}hasLanguageSomeTranslations(s){const r=this.getDataByLanguage(s);return!!(r&&Object.keys(r)||[]).find(o=>r[o]&&Object.keys(r[o]).length>0)}toJSON(){return this.data}}var Oh={processors:{},addPostProcessor(c){this.processors[c.name]=c},handle(c,s,r,u,o){return c.forEach(d=>{var m;s=((m=this.processors[d])==null?void 0:m.process(s,r,u,o))??s}),s}};const eh={},th=c=>!ee(c)&&typeof c!="boolean"&&typeof c!="number";class fs extends ds{constructor(s,r={}){super(),zp(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],s,this),this.options=r,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=qt.create("translator")}changeLanguage(s){s&&(this.language=s)}exists(s,r={interpolation:{}}){const u={...r};if(s==null)return!1;const o=this.resolve(s,u);return(o==null?void 0:o.res)!==void 0}extractFromKey(s,r){let u=r.nsSeparator!==void 0?r.nsSeparator:this.options.nsSeparator;u===void 0&&(u=":");const o=r.keySeparator!==void 0?r.keySeparator:this.options.keySeparator;let d=r.ns||this.options.defaultNS||[];const m=u&&s.indexOf(u)>-1,y=!this.options.userDefinedKeySeparator&&!r.keySeparator&&!this.options.userDefinedNsSeparator&&!r.nsSeparator&&!Hp(s,u,o);if(m&&!y){const v=s.match(this.interpolator.nestingRegexp);if(v&&v.length>0)return{key:s,namespaces:ee(d)?[d]:d};const g=s.split(u);(u!==o||u===o&&this.options.ns.indexOf(g[0])>-1)&&(d=g.shift()),s=g.join(o)}return{key:s,namespaces:ee(d)?[d]:d}}translate(s,r,u){let o=typeof r=="object"?{...r}:r;if(typeof o!="object"&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(o={...o}),o||(o={}),s==null)return"";Array.isArray(s)||(s=[String(s)]);const d=o.returnDetails!==void 0?o.returnDetails:this.options.returnDetails,m=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,{key:y,namespaces:v}=this.extractFromKey(s[s.length-1],o),g=v[v.length-1];let N=o.nsSeparator!==void 0?o.nsSeparator:this.options.nsSeparator;N===void 0&&(N=":");const w=o.lng||this.language,q=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((w==null?void 0:w.toLowerCase())==="cimode")return q?d?{res:`${g}${N}${y}`,usedKey:y,exactUsedKey:y,usedLng:w,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:`${g}${N}${y}`:d?{res:y,usedKey:y,exactUsedKey:y,usedLng:w,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:y;const B=this.resolve(s,o);let U=B==null?void 0:B.res;const Z=(B==null?void 0:B.usedKey)||y,F=(B==null?void 0:B.exactUsedKey)||y,be=["[object Number]","[object Function]","[object RegExp]"],fe=o.joinArrays!==void 0?o.joinArrays:this.options.joinArrays,ue=!this.i18nFormat||this.i18nFormat.handleAsObject,oe=o.count!==void 0&&!ee(o.count),J=fs.hasDefaultValue(o),xe=oe?this.pluralResolver.getSuffix(w,o.count,o):"",me=o.ordinal&&oe?this.pluralResolver.getSuffix(w,o.count,{ordinal:!1}):"",G=oe&&!o.ordinal&&o.count===0,te=G&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${xe}`]||o[`defaultValue${me}`]||o.defaultValue;let de=U;ue&&!U&&J&&(de=te);const Re=th(de),De=Object.prototype.toString.apply(de);if(ue&&de&&Re&&be.indexOf(De)<0&&!(ee(fe)&&Array.isArray(de))){if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const qe=this.options.returnedObjectHandler?this.options.returnedObjectHandler(Z,de,{...o,ns:v}):`key '${y} (${this.language})' returned an object instead of string.`;return d?(B.res=qe,B.usedParams=this.getUsedParamsDetails(o),B):qe}if(m){const qe=Array.isArray(de),Ce=qe?[]:{},we=qe?F:Z;for(const z in de)if(Object.prototype.hasOwnProperty.call(de,z)){const H=`${we}${m}${z}`;J&&!U?Ce[z]=this.translate(H,{...o,defaultValue:th(te)?te[z]:void 0,joinArrays:!1,ns:v}):Ce[z]=this.translate(H,{...o,joinArrays:!1,ns:v}),Ce[z]===H&&(Ce[z]=de[z])}U=Ce}}else if(ue&&ee(fe)&&Array.isArray(U))U=U.join(fe),U&&(U=this.extendTranslation(U,s,o,u));else{let qe=!1,Ce=!1;!this.isValidLookup(U)&&J&&(qe=!0,U=te),this.isValidLookup(U)||(Ce=!0,U=y);const z=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&Ce?void 0:U,H=J&&te!==U&&this.options.updateMissing;if(Ce||qe||H){if(this.logger.log(H?"updateKey":"missingKey",w,g,y,H?te:U),m){const R=this.resolve(y,{...o,keySeparator:!1});R&&R.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let C=[];const he=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if(this.options.saveMissingTo==="fallback"&&he&&he[0])for(let R=0;R<he.length;R++)C.push(he[R]);else this.options.saveMissingTo==="all"?C=this.languageUtils.toResolveHierarchy(o.lng||this.language):C.push(o.lng||this.language);const x=(R,k,L)=>{var le;const Y=J&&L!==U?L:z;this.options.missingKeyHandler?this.options.missingKeyHandler(R,g,k,Y,H,o):(le=this.backendConnector)!=null&&le.saveMissing&&this.backendConnector.saveMissing(R,g,k,Y,H,o),this.emit("missingKey",R,g,k,U)};this.options.saveMissing&&(this.options.saveMissingPlurals&&oe?C.forEach(R=>{const k=this.pluralResolver.getSuffixes(R,o);G&&o[`defaultValue${this.options.pluralSeparator}zero`]&&k.indexOf(`${this.options.pluralSeparator}zero`)<0&&k.push(`${this.options.pluralSeparator}zero`),k.forEach(L=>{x([R],y+L,o[`defaultValue${L}`]||te)})}):x(C,y,te))}U=this.extendTranslation(U,s,o,B,u),Ce&&U===y&&this.options.appendNamespaceToMissingKey&&(U=`${g}${N}${y}`),(Ce||qe)&&this.options.parseMissingKeyHandler&&(U=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${g}${N}${y}`:y,qe?U:void 0,o))}return d?(B.res=U,B.usedParams=this.getUsedParamsDetails(o),B):U}extendTranslation(s,r,u,o,d){var v,g;if((v=this.i18nFormat)!=null&&v.parse)s=this.i18nFormat.parse(s,{...this.options.interpolation.defaultVariables,...u},u.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!u.skipInterpolation){u.interpolation&&this.interpolator.init({...u,interpolation:{...this.options.interpolation,...u.interpolation}});const N=ee(s)&&(((g=u==null?void 0:u.interpolation)==null?void 0:g.skipOnVariables)!==void 0?u.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let w;if(N){const B=s.match(this.interpolator.nestingRegexp);w=B&&B.length}let q=u.replace&&!ee(u.replace)?u.replace:u;if(this.options.interpolation.defaultVariables&&(q={...this.options.interpolation.defaultVariables,...q}),s=this.interpolator.interpolate(s,q,u.lng||this.language||o.usedLng,u),N){const B=s.match(this.interpolator.nestingRegexp),U=B&&B.length;w<U&&(u.nest=!1)}!u.lng&&o&&o.res&&(u.lng=this.language||o.usedLng),u.nest!==!1&&(s=this.interpolator.nest(s,(...B)=>(d==null?void 0:d[0])===B[0]&&!u.context?(this.logger.warn(`It seems you are nesting recursively key: ${B[0]} in key: ${r[0]}`),null):this.translate(...B,r),u)),u.interpolation&&this.interpolator.reset()}const m=u.postProcess||this.options.postProcess,y=ee(m)?[m]:m;return s!=null&&(y!=null&&y.length)&&u.applyPostProcessor!==!1&&(s=Oh.handle(y,s,r,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(u)},...u}:u,this)),s}resolve(s,r={}){let u,o,d,m,y;return ee(s)&&(s=[s]),s.forEach(v=>{if(this.isValidLookup(u))return;const g=this.extractFromKey(v,r),N=g.key;o=N;let w=g.namespaces;this.options.fallbackNS&&(w=w.concat(this.options.fallbackNS));const q=r.count!==void 0&&!ee(r.count),B=q&&!r.ordinal&&r.count===0,U=r.context!==void 0&&(ee(r.context)||typeof r.context=="number")&&r.context!=="",Z=r.lngs?r.lngs:this.languageUtils.toResolveHierarchy(r.lng||this.language,r.fallbackLng);w.forEach(F=>{var be,fe;this.isValidLookup(u)||(y=F,!eh[`${Z[0]}-${F}`]&&((be=this.utils)!=null&&be.hasLoadedNamespace)&&!((fe=this.utils)!=null&&fe.hasLoadedNamespace(y))&&(eh[`${Z[0]}-${F}`]=!0,this.logger.warn(`key "${o}" for languages "${Z.join(", ")}" won't get resolved as namespace "${y}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),Z.forEach(ue=>{var xe;if(this.isValidLookup(u))return;m=ue;const oe=[N];if((xe=this.i18nFormat)!=null&&xe.addLookupKeys)this.i18nFormat.addLookupKeys(oe,N,ue,F,r);else{let me;q&&(me=this.pluralResolver.getSuffix(ue,r.count,r));const G=`${this.options.pluralSeparator}zero`,te=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(q&&(oe.push(N+me),r.ordinal&&me.indexOf(te)===0&&oe.push(N+me.replace(te,this.options.pluralSeparator)),B&&oe.push(N+G)),U){const de=`${N}${this.options.contextSeparator}${r.context}`;oe.push(de),q&&(oe.push(de+me),r.ordinal&&me.indexOf(te)===0&&oe.push(de+me.replace(te,this.options.pluralSeparator)),B&&oe.push(de+G))}}let J;for(;J=oe.pop();)this.isValidLookup(u)||(d=J,u=this.getResource(ue,F,J,r))}))})}),{res:u,usedKey:o,exactUsedKey:d,usedLng:m,usedNS:y}}isValidLookup(s){return s!==void 0&&!(!this.options.returnNull&&s===null)&&!(!this.options.returnEmptyString&&s==="")}getResource(s,r,u,o={}){var d;return(d=this.i18nFormat)!=null&&d.getResource?this.i18nFormat.getResource(s,r,u,o):this.resourceStore.getResource(s,r,u,o)}getUsedParamsDetails(s={}){const r=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],u=s.replace&&!ee(s.replace);let o=u?s.replace:s;if(u&&typeof s.count<"u"&&(o.count=s.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!u){o={...o};for(const d of r)delete o[d]}return o}static hasDefaultValue(s){const r="defaultValue";for(const u in s)if(Object.prototype.hasOwnProperty.call(s,u)&&r===u.substring(0,r.length)&&s[u]!==void 0)return!0;return!1}}class ah{constructor(s){this.options=s,this.supportedLngs=this.options.supportedLngs||!1,this.logger=qt.create("languageUtils")}getScriptPartFromCode(s){if(s=Xn(s),!s||s.indexOf("-")<0)return null;const r=s.split("-");return r.length===2||(r.pop(),r[r.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(r.join("-"))}getLanguagePartFromCode(s){if(s=Xn(s),!s||s.indexOf("-")<0)return s;const r=s.split("-");return this.formatLanguageCode(r[0])}formatLanguageCode(s){if(ee(s)&&s.indexOf("-")>-1){let r;try{r=Intl.getCanonicalLocales(s)[0]}catch{}return r&&this.options.lowerCaseLng&&(r=r.toLowerCase()),r||(this.options.lowerCaseLng?s.toLowerCase():s)}return this.options.cleanCode||this.options.lowerCaseLng?s.toLowerCase():s}isSupportedCode(s){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(s=this.getLanguagePartFromCode(s)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(s)>-1}getBestMatchFromCodes(s){if(!s)return null;let r;return s.forEach(u=>{if(r)return;const o=this.formatLanguageCode(u);(!this.options.supportedLngs||this.isSupportedCode(o))&&(r=o)}),!r&&this.options.supportedLngs&&s.forEach(u=>{if(r)return;const o=this.getScriptPartFromCode(u);if(this.isSupportedCode(o))return r=o;const d=this.getLanguagePartFromCode(u);if(this.isSupportedCode(d))return r=d;r=this.options.supportedLngs.find(m=>{if(m===d)return m;if(!(m.indexOf("-")<0&&d.indexOf("-")<0)&&(m.indexOf("-")>0&&d.indexOf("-")<0&&m.substring(0,m.indexOf("-"))===d||m.indexOf(d)===0&&d.length>1))return m})}),r||(r=this.getFallbackCodes(this.options.fallbackLng)[0]),r}getFallbackCodes(s,r){if(!s)return[];if(typeof s=="function"&&(s=s(r)),ee(s)&&(s=[s]),Array.isArray(s))return s;if(!r)return s.default||[];let u=s[r];return u||(u=s[this.getScriptPartFromCode(r)]),u||(u=s[this.formatLanguageCode(r)]),u||(u=s[this.getLanguagePartFromCode(r)]),u||(u=s.default),u||[]}toResolveHierarchy(s,r){const u=this.getFallbackCodes((r===!1?[]:r)||this.options.fallbackLng||[],s),o=[],d=m=>{m&&(this.isSupportedCode(m)?o.push(m):this.logger.warn(`rejecting language code not found in supportedLngs: ${m}`))};return ee(s)&&(s.indexOf("-")>-1||s.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&d(this.formatLanguageCode(s)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&d(this.getScriptPartFromCode(s)),this.options.load!=="currentOnly"&&d(this.getLanguagePartFromCode(s))):ee(s)&&d(this.formatLanguageCode(s)),u.forEach(m=>{o.indexOf(m)<0&&d(this.formatLanguageCode(m))}),o}}const lh={zero:0,one:1,two:2,few:3,many:4,other:5},nh={select:c=>c===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Bp{constructor(s,r={}){this.languageUtils=s,this.options=r,this.logger=qt.create("pluralResolver"),this.pluralRulesCache={}}addRule(s,r){this.rules[s]=r}clearCache(){this.pluralRulesCache={}}getRule(s,r={}){const u=Xn(s==="dev"?"en":s),o=r.ordinal?"ordinal":"cardinal",d=JSON.stringify({cleanedCode:u,type:o});if(d in this.pluralRulesCache)return this.pluralRulesCache[d];let m;try{m=new Intl.PluralRules(u,{type:o})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),nh;if(!s.match(/-|_/))return nh;const v=this.languageUtils.getLanguagePartFromCode(s);m=this.getRule(v,r)}return this.pluralRulesCache[d]=m,m}needsPlural(s,r={}){let u=this.getRule(s,r);return u||(u=this.getRule("dev",r)),(u==null?void 0:u.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(s,r,u={}){return this.getSuffixes(s,u).map(o=>`${r}${o}`)}getSuffixes(s,r={}){let u=this.getRule(s,r);return u||(u=this.getRule("dev",r)),u?u.resolvedOptions().pluralCategories.sort((o,d)=>lh[o]-lh[d]).map(o=>`${this.options.prepend}${r.ordinal?`ordinal${this.options.prepend}`:""}${o}`):[]}getSuffix(s,r,u={}){const o=this.getRule(s,u);return o?`${this.options.prepend}${u.ordinal?`ordinal${this.options.prepend}`:""}${o.select(r)}`:(this.logger.warn(`no plural rule found for: ${s}`),this.getSuffix("dev",r,u))}}const ih=(c,s,r,u=".",o=!0)=>{let d=Rp(c,s,r);return!d&&o&&ee(r)&&(d=Ur(c,r,u),d===void 0&&(d=Ur(s,r,u))),d},Mr=c=>c.replace(/\$/g,"$$$$");class kp{constructor(s={}){var r;this.logger=qt.create("interpolator"),this.options=s,this.format=((r=s==null?void 0:s.interpolation)==null?void 0:r.format)||(u=>u),this.init(s)}init(s={}){s.interpolation||(s.interpolation={escapeValue:!0});const{escape:r,escapeValue:u,useRawValueToEscape:o,prefix:d,prefixEscaped:m,suffix:y,suffixEscaped:v,formatSeparator:g,unescapeSuffix:N,unescapePrefix:w,nestingPrefix:q,nestingPrefixEscaped:B,nestingSuffix:U,nestingSuffixEscaped:Z,nestingOptionsSeparator:F,maxReplaces:be,alwaysFormat:fe}=s.interpolation;this.escape=r!==void 0?r:Dp,this.escapeValue=u!==void 0?u:!0,this.useRawValueToEscape=o!==void 0?o:!1,this.prefix=d?Ul(d):m||"{{",this.suffix=y?Ul(y):v||"}}",this.formatSeparator=g||",",this.unescapePrefix=N?"":w||"-",this.unescapeSuffix=this.unescapePrefix?"":N||"",this.nestingPrefix=q?Ul(q):B||Ul("$t("),this.nestingSuffix=U?Ul(U):Z||Ul(")"),this.nestingOptionsSeparator=F||",",this.maxReplaces=be||1e3,this.alwaysFormat=fe!==void 0?fe:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const s=(r,u)=>(r==null?void 0:r.source)===u?(r.lastIndex=0,r):new RegExp(u,"g");this.regexp=s(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=s(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=s(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(s,r,u,o){var B;let d,m,y;const v=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},g=U=>{if(U.indexOf(this.formatSeparator)<0){const fe=ih(r,v,U,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(fe,void 0,u,{...o,...r,interpolationkey:U}):fe}const Z=U.split(this.formatSeparator),F=Z.shift().trim(),be=Z.join(this.formatSeparator).trim();return this.format(ih(r,v,F,this.options.keySeparator,this.options.ignoreJSONStructure),be,u,{...o,...r,interpolationkey:F})};this.resetRegExp();const N=(o==null?void 0:o.missingInterpolationHandler)||this.options.missingInterpolationHandler,w=((B=o==null?void 0:o.interpolation)==null?void 0:B.skipOnVariables)!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:U=>Mr(U)},{regex:this.regexp,safeValue:U=>this.escapeValue?Mr(this.escape(U)):Mr(U)}].forEach(U=>{for(y=0;d=U.regex.exec(s);){const Z=d[1].trim();if(m=g(Z),m===void 0)if(typeof N=="function"){const be=N(s,d,o);m=ee(be)?be:""}else if(o&&Object.prototype.hasOwnProperty.call(o,Z))m="";else if(w){m=d[0];continue}else this.logger.warn(`missed to pass in variable ${Z} for interpolating ${s}`),m="";else!ee(m)&&!this.useRawValueToEscape&&(m=$d(m));const F=U.safeValue(m);if(s=s.replace(d[0],F),w?(U.regex.lastIndex+=m.length,U.regex.lastIndex-=d[0].length):U.regex.lastIndex=0,y++,y>=this.maxReplaces)break}}),s}nest(s,r,u={}){let o,d,m;const y=(v,g)=>{const N=this.nestingOptionsSeparator;if(v.indexOf(N)<0)return v;const w=v.split(new RegExp(`${N}[ ]*{`));let q=`{${w[1]}`;v=w[0],q=this.interpolate(q,m);const B=q.match(/'/g),U=q.match(/"/g);(((B==null?void 0:B.length)??0)%2===0&&!U||U.length%2!==0)&&(q=q.replace(/'/g,'"'));try{m=JSON.parse(q),g&&(m={...g,...m})}catch(Z){return this.logger.warn(`failed parsing options string in nesting for key ${v}`,Z),`${v}${N}${q}`}return m.defaultValue&&m.defaultValue.indexOf(this.prefix)>-1&&delete m.defaultValue,v};for(;o=this.nestingRegexp.exec(s);){let v=[];m={...u},m=m.replace&&!ee(m.replace)?m.replace:m,m.applyPostProcessor=!1,delete m.defaultValue;const g=/{.*}/.test(o[1])?o[1].lastIndexOf("}")+1:o[1].indexOf(this.formatSeparator);if(g!==-1&&(v=o[1].slice(g).split(this.formatSeparator).map(N=>N.trim()).filter(Boolean),o[1]=o[1].slice(0,g)),d=r(y.call(this,o[1].trim(),m),m),d&&o[0]===s&&!ee(d))return d;ee(d)||(d=$d(d)),d||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${s}`),d=""),v.length&&(d=v.reduce((N,w)=>this.format(N,w,u.lng,{...u,interpolationkey:o[1].trim()}),d.trim())),s=s.replace(o[0],d),this.regexp.lastIndex=0}return s}}const Gp=c=>{let s=c.toLowerCase().trim();const r={};if(c.indexOf("(")>-1){const u=c.split("(");s=u[0].toLowerCase().trim();const o=u[1].substring(0,u[1].length-1);s==="currency"&&o.indexOf(":")<0?r.currency||(r.currency=o.trim()):s==="relativetime"&&o.indexOf(":")<0?r.range||(r.range=o.trim()):o.split(";").forEach(m=>{if(m){const[y,...v]=m.split(":"),g=v.join(":").trim().replace(/^'+|'+$/g,""),N=y.trim();r[N]||(r[N]=g),g==="false"&&(r[N]=!1),g==="true"&&(r[N]=!0),isNaN(g)||(r[N]=parseInt(g,10))}})}return{formatName:s,formatOptions:r}},sh=c=>{const s={};return(r,u,o)=>{let d=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(d={...d,[o.interpolationkey]:void 0});const m=u+JSON.stringify(d);let y=s[m];return y||(y=c(Xn(u),o),s[m]=y),y(r)}},Yp=c=>(s,r,u)=>c(Xn(r),u)(s);class Vp{constructor(s={}){this.logger=qt.create("formatter"),this.options=s,this.init(s)}init(s,r={interpolation:{}}){this.formatSeparator=r.interpolation.formatSeparator||",";const u=r.cacheInBuiltFormats?sh:Yp;this.formats={number:u((o,d)=>{const m=new Intl.NumberFormat(o,{...d});return y=>m.format(y)}),currency:u((o,d)=>{const m=new Intl.NumberFormat(o,{...d,style:"currency"});return y=>m.format(y)}),datetime:u((o,d)=>{const m=new Intl.DateTimeFormat(o,{...d});return y=>m.format(y)}),relativetime:u((o,d)=>{const m=new Intl.RelativeTimeFormat(o,{...d});return y=>m.format(y,d.range||"day")}),list:u((o,d)=>{const m=new Intl.ListFormat(o,{...d});return y=>m.format(y)})}}add(s,r){this.formats[s.toLowerCase().trim()]=r}addCached(s,r){this.formats[s.toLowerCase().trim()]=sh(r)}format(s,r,u,o={}){const d=r.split(this.formatSeparator);if(d.length>1&&d[0].indexOf("(")>1&&d[0].indexOf(")")<0&&d.find(y=>y.indexOf(")")>-1)){const y=d.findIndex(v=>v.indexOf(")")>-1);d[0]=[d[0],...d.splice(1,y)].join(this.formatSeparator)}return d.reduce((y,v)=>{var w;const{formatName:g,formatOptions:N}=Gp(v);if(this.formats[g]){let q=y;try{const B=((w=o==null?void 0:o.formatParams)==null?void 0:w[o.interpolationkey])||{},U=B.locale||B.lng||o.locale||o.lng||u;q=this.formats[g](y,U,{...N,...o,...B})}catch(B){this.logger.warn(B)}return q}else this.logger.warn(`there was no format function for ${g}`);return y},s)}}const Xp=(c,s)=>{c.pending[s]!==void 0&&(delete c.pending[s],c.pendingCount--)};class Qp extends ds{constructor(s,r,u,o={}){var d,m;super(),this.backend=s,this.store=r,this.services=u,this.languageUtils=u.languageUtils,this.options=o,this.logger=qt.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],(m=(d=this.backend)==null?void 0:d.init)==null||m.call(d,u,o.backend,o)}queueLoad(s,r,u,o){const d={},m={},y={},v={};return s.forEach(g=>{let N=!0;r.forEach(w=>{const q=`${g}|${w}`;!u.reload&&this.store.hasResourceBundle(g,w)?this.state[q]=2:this.state[q]<0||(this.state[q]===1?m[q]===void 0&&(m[q]=!0):(this.state[q]=1,N=!1,m[q]===void 0&&(m[q]=!0),d[q]===void 0&&(d[q]=!0),v[w]===void 0&&(v[w]=!0)))}),N||(y[g]=!0)}),(Object.keys(d).length||Object.keys(m).length)&&this.queue.push({pending:m,pendingCount:Object.keys(m).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(d),pending:Object.keys(m),toLoadLanguages:Object.keys(y),toLoadNamespaces:Object.keys(v)}}loaded(s,r,u){const o=s.split("|"),d=o[0],m=o[1];r&&this.emit("failedLoading",d,m,r),!r&&u&&this.store.addResourceBundle(d,m,u,void 0,void 0,{skipCopy:!0}),this.state[s]=r?-1:2,r&&u&&(this.state[s]=0);const y={};this.queue.forEach(v=>{jp(v.loaded,[d],m),Xp(v,s),r&&v.errors.push(r),v.pendingCount===0&&!v.done&&(Object.keys(v.loaded).forEach(g=>{y[g]||(y[g]={});const N=v.loaded[g];N.length&&N.forEach(w=>{y[g][w]===void 0&&(y[g][w]=!0)})}),v.done=!0,v.errors.length?v.callback(v.errors):v.callback())}),this.emit("loaded",y),this.queue=this.queue.filter(v=>!v.done)}read(s,r,u,o=0,d=this.retryTimeout,m){if(!s.length)return m(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:s,ns:r,fcName:u,tried:o,wait:d,callback:m});return}this.readingCalls++;const y=(g,N)=>{if(this.readingCalls--,this.waitingReads.length>0){const w=this.waitingReads.shift();this.read(w.lng,w.ns,w.fcName,w.tried,w.wait,w.callback)}if(g&&N&&o<this.maxRetries){setTimeout(()=>{this.read.call(this,s,r,u,o+1,d*2,m)},d);return}m(g,N)},v=this.backend[u].bind(this.backend);if(v.length===2){try{const g=v(s,r);g&&typeof g.then=="function"?g.then(N=>y(null,N)).catch(y):y(null,g)}catch(g){y(g)}return}return v(s,r,y)}prepareLoading(s,r,u={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();ee(s)&&(s=this.languageUtils.toResolveHierarchy(s)),ee(r)&&(r=[r]);const d=this.queueLoad(s,r,u,o);if(!d.toLoad.length)return d.pending.length||o(),null;d.toLoad.forEach(m=>{this.loadOne(m)})}load(s,r,u){this.prepareLoading(s,r,{},u)}reload(s,r,u){this.prepareLoading(s,r,{reload:!0},u)}loadOne(s,r=""){const u=s.split("|"),o=u[0],d=u[1];this.read(o,d,"read",void 0,void 0,(m,y)=>{m&&this.logger.warn(`${r}loading namespace ${d} for language ${o} failed`,m),!m&&y&&this.logger.log(`${r}loaded namespace ${d} for language ${o}`,y),this.loaded(s,m,y)})}saveMissing(s,r,u,o,d,m={},y=()=>{}){var v,g,N,w,q;if((g=(v=this.services)==null?void 0:v.utils)!=null&&g.hasLoadedNamespace&&!((w=(N=this.services)==null?void 0:N.utils)!=null&&w.hasLoadedNamespace(r))){this.logger.warn(`did not save key "${u}" as the namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(u==null||u==="")){if((q=this.backend)!=null&&q.create){const B={...m,isUpdate:d},U=this.backend.create.bind(this.backend);if(U.length<6)try{let Z;U.length===5?Z=U(s,r,u,o,B):Z=U(s,r,u,o),Z&&typeof Z.then=="function"?Z.then(F=>y(null,F)).catch(y):y(null,Z)}catch(Z){y(Z)}else U(s,r,u,o,y,B)}!s||!s[0]||this.store.addResource(s[0],r,u,o)}}}const uh=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:c=>{let s={};if(typeof c[1]=="object"&&(s=c[1]),ee(c[1])&&(s.defaultValue=c[1]),ee(c[2])&&(s.tDescription=c[2]),typeof c[2]=="object"||typeof c[3]=="object"){const r=c[3]||c[2];Object.keys(r).forEach(u=>{s[u]=r[u]})}return s},interpolation:{escapeValue:!0,format:c=>c,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),rh=c=>{var s,r;return ee(c.ns)&&(c.ns=[c.ns]),ee(c.fallbackLng)&&(c.fallbackLng=[c.fallbackLng]),ee(c.fallbackNS)&&(c.fallbackNS=[c.fallbackNS]),((r=(s=c.supportedLngs)==null?void 0:s.indexOf)==null?void 0:r.call(s,"cimode"))<0&&(c.supportedLngs=c.supportedLngs.concat(["cimode"])),typeof c.initImmediate=="boolean"&&(c.initAsync=c.initImmediate),c},ss=()=>{},Zp=c=>{Object.getOwnPropertyNames(Object.getPrototypeOf(c)).forEach(r=>{typeof c[r]=="function"&&(c[r]=c[r].bind(c))})};class Qn extends ds{constructor(s={},r){if(super(),this.options=rh(s),this.services={},this.logger=qt,this.modules={external:[]},Zp(this),r&&!this.isInitialized&&!s.isClone){if(!this.options.initAsync)return this.init(s,r),this;setTimeout(()=>{this.init(s,r)},0)}}init(s={},r){this.isInitializing=!0,typeof s=="function"&&(r=s,s={}),s.defaultNS==null&&s.ns&&(ee(s.ns)?s.defaultNS=s.ns:s.ns.indexOf("translation")<0&&(s.defaultNS=s.ns[0]));const u=uh();this.options={...u,...this.options,...rh(s)},this.options.interpolation={...u.interpolation,...this.options.interpolation},s.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=s.keySeparator),s.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=s.nsSeparator);const o=g=>g?typeof g=="function"?new g:g:null;if(!this.options.isClone){this.modules.logger?qt.init(o(this.modules.logger),this.options):qt.init(null,this.options);let g;this.modules.formatter?g=this.modules.formatter:g=Vp;const N=new ah(this.options);this.store=new Id(this.options.resources,this.options);const w=this.services;w.logger=qt,w.resourceStore=this.store,w.languageUtils=N,w.pluralResolver=new Bp(N,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==u.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),g&&(!this.options.interpolation.format||this.options.interpolation.format===u.interpolation.format)&&(w.formatter=o(g),w.formatter.init&&w.formatter.init(w,this.options),this.options.interpolation.format=w.formatter.format.bind(w.formatter)),w.interpolator=new kp(this.options),w.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},w.backendConnector=new Qp(o(this.modules.backend),w.resourceStore,w,this.options),w.backendConnector.on("*",(B,...U)=>{this.emit(B,...U)}),this.modules.languageDetector&&(w.languageDetector=o(this.modules.languageDetector),w.languageDetector.init&&w.languageDetector.init(w,this.options.detection,this.options)),this.modules.i18nFormat&&(w.i18nFormat=o(this.modules.i18nFormat),w.i18nFormat.init&&w.i18nFormat.init(this)),this.translator=new fs(this.services,this.options),this.translator.on("*",(B,...U)=>{this.emit(B,...U)}),this.modules.external.forEach(B=>{B.init&&B.init(this)})}if(this.format=this.options.interpolation.format,r||(r=ss),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const g=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);g.length>0&&g[0]!=="dev"&&(this.options.lng=g[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(g=>{this[g]=(...N)=>this.store[g](...N)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(g=>{this[g]=(...N)=>(this.store[g](...N),this)});const y=Gn(),v=()=>{const g=(N,w)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),y.resolve(w),r(N,w)};if(this.languages&&!this.isInitialized)return g(null,this.t.bind(this));this.changeLanguage(this.options.lng,g)};return this.options.resources||!this.options.initAsync?v():setTimeout(v,0),y}loadResources(s,r=ss){var d,m;let u=r;const o=ee(s)?s:this.language;if(typeof s=="function"&&(u=s),!this.options.resources||this.options.partialBundledLanguages){if((o==null?void 0:o.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return u();const y=[],v=g=>{if(!g||g==="cimode")return;this.services.languageUtils.toResolveHierarchy(g).forEach(w=>{w!=="cimode"&&y.indexOf(w)<0&&y.push(w)})};o?v(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(N=>v(N)),(m=(d=this.options.preload)==null?void 0:d.forEach)==null||m.call(d,g=>v(g)),this.services.backendConnector.load(y,this.options.ns,g=>{!g&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),u(g)})}else u(null)}reloadResources(s,r,u){const o=Gn();return typeof s=="function"&&(u=s,s=void 0),typeof r=="function"&&(u=r,r=void 0),s||(s=this.languages),r||(r=this.options.ns),u||(u=ss),this.services.backendConnector.reload(s,r,d=>{o.resolve(),u(d)}),o}use(s){if(!s)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!s.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return s.type==="backend"&&(this.modules.backend=s),(s.type==="logger"||s.log&&s.warn&&s.error)&&(this.modules.logger=s),s.type==="languageDetector"&&(this.modules.languageDetector=s),s.type==="i18nFormat"&&(this.modules.i18nFormat=s),s.type==="postProcessor"&&Oh.addPostProcessor(s),s.type==="formatter"&&(this.modules.formatter=s),s.type==="3rdParty"&&this.modules.external.push(s),this}setResolvedLanguage(s){if(!(!s||!this.languages)&&!(["cimode","dev"].indexOf(s)>-1)){for(let r=0;r<this.languages.length;r++){const u=this.languages[r];if(!(["cimode","dev"].indexOf(u)>-1)&&this.store.hasLanguageSomeTranslations(u)){this.resolvedLanguage=u;break}}!this.resolvedLanguage&&this.languages.indexOf(s)<0&&this.store.hasLanguageSomeTranslations(s)&&(this.resolvedLanguage=s,this.languages.unshift(s))}}changeLanguage(s,r){this.isLanguageChangingTo=s;const u=Gn();this.emit("languageChanging",s);const o=y=>{this.language=y,this.languages=this.services.languageUtils.toResolveHierarchy(y),this.resolvedLanguage=void 0,this.setResolvedLanguage(y)},d=(y,v)=>{v?this.isLanguageChangingTo===s&&(o(v),this.translator.changeLanguage(v),this.isLanguageChangingTo=void 0,this.emit("languageChanged",v),this.logger.log("languageChanged",v)):this.isLanguageChangingTo=void 0,u.resolve((...g)=>this.t(...g)),r&&r(y,(...g)=>this.t(...g))},m=y=>{var N,w;!s&&!y&&this.services.languageDetector&&(y=[]);const v=ee(y)?y:y&&y[0],g=this.store.hasLanguageSomeTranslations(v)?v:this.services.languageUtils.getBestMatchFromCodes(ee(y)?[y]:y);g&&(this.language||o(g),this.translator.language||this.translator.changeLanguage(g),(w=(N=this.services.languageDetector)==null?void 0:N.cacheUserLanguage)==null||w.call(N,g)),this.loadResources(g,q=>{d(q,g)})};return!s&&this.services.languageDetector&&!this.services.languageDetector.async?m(this.services.languageDetector.detect()):!s&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(m):this.services.languageDetector.detect(m):m(s),u}getFixedT(s,r,u){const o=(d,m,...y)=>{let v;typeof m!="object"?v=this.options.overloadTranslationOptionHandler([d,m].concat(y)):v={...m},v.lng=v.lng||o.lng,v.lngs=v.lngs||o.lngs,v.ns=v.ns||o.ns,v.keyPrefix!==""&&(v.keyPrefix=v.keyPrefix||u||o.keyPrefix);const g=this.options.keySeparator||".";let N;return v.keyPrefix&&Array.isArray(d)?N=d.map(w=>`${v.keyPrefix}${g}${w}`):N=v.keyPrefix?`${v.keyPrefix}${g}${d}`:d,this.t(N,v)};return ee(s)?o.lng=s:o.lngs=s,o.ns=r,o.keyPrefix=u,o}t(...s){var r;return(r=this.translator)==null?void 0:r.translate(...s)}exists(...s){var r;return(r=this.translator)==null?void 0:r.exists(...s)}setDefaultNamespace(s){this.options.defaultNS=s}hasLoadedNamespace(s,r={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const u=r.lng||this.resolvedLanguage||this.languages[0],o=this.options?this.options.fallbackLng:!1,d=this.languages[this.languages.length-1];if(u.toLowerCase()==="cimode")return!0;const m=(y,v)=>{const g=this.services.backendConnector.state[`${y}|${v}`];return g===-1||g===0||g===2};if(r.precheck){const y=r.precheck(this,m);if(y!==void 0)return y}return!!(this.hasResourceBundle(u,s)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||m(u,s)&&(!o||m(d,s)))}loadNamespaces(s,r){const u=Gn();return this.options.ns?(ee(s)&&(s=[s]),s.forEach(o=>{this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}),this.loadResources(o=>{u.resolve(),r&&r(o)}),u):(r&&r(),Promise.resolve())}loadLanguages(s,r){const u=Gn();ee(s)&&(s=[s]);const o=this.options.preload||[],d=s.filter(m=>o.indexOf(m)<0&&this.services.languageUtils.isSupportedCode(m));return d.length?(this.options.preload=o.concat(d),this.loadResources(m=>{u.resolve(),r&&r(m)}),u):(r&&r(),Promise.resolve())}dir(s){var o,d;if(s||(s=this.resolvedLanguage||(((o=this.languages)==null?void 0:o.length)>0?this.languages[0]:this.language)),!s)return"rtl";try{const m=new Intl.Locale(s);if(m&&m.getTextInfo){const y=m.getTextInfo();if(y&&y.direction)return y.direction}}catch{}const r=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],u=((d=this.services)==null?void 0:d.languageUtils)||new ah(uh());return s.toLowerCase().indexOf("-latn")>1?"ltr":r.indexOf(u.getLanguagePartFromCode(s))>-1||s.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(s={},r){return new Qn(s,r)}cloneInstance(s={},r=ss){const u=s.forkResourceStore;u&&delete s.forkResourceStore;const o={...this.options,...s,isClone:!0},d=new Qn(o);if((s.debug!==void 0||s.prefix!==void 0)&&(d.logger=d.logger.clone(s)),["store","services","language"].forEach(y=>{d[y]=this[y]}),d.services={...this.services},d.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},u){const y=Object.keys(this.store.data).reduce((v,g)=>(v[g]={...this.store.data[g]},v[g]=Object.keys(v[g]).reduce((N,w)=>(N[w]={...v[g][w]},N),v[g]),v),{});d.store=new Id(y,o),d.services.resourceStore=d.store}return d.translator=new fs(d.services,o),d.translator.on("*",(y,...v)=>{d.emit(y,...v)}),d.init(o,r),d.translator.options=o,d.translator.backendConnector.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},d}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Ie=Qn.createInstance();Ie.createInstance=Qn.createInstance;Ie.createInstance;Ie.dir;Ie.init;Ie.loadResources;Ie.reloadResources;Ie.use;Ie.changeLanguage;Ie.getFixedT;Ie.t;Ie.exists;Ie.setDefaultNamespace;Ie.hasLoadedNamespace;Ie.loadNamespaces;Ie.loadLanguages;const Kp=(c,s,r,u)=>{var d,m,y,v;const o=[r,{code:s,...u||{}}];if((m=(d=c==null?void 0:c.services)==null?void 0:d.logger)!=null&&m.forward)return c.services.logger.forward(o,"warn","react-i18next::",!0);$a(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),(v=(y=c==null?void 0:c.services)==null?void 0:y.logger)!=null&&v.warn?c.services.logger.warn(...o):console!=null&&console.warn&&console.warn(...o)},ch={},Lr=(c,s,r,u)=>{$a(r)&&ch[r]||($a(r)&&(ch[r]=new Date),Kp(c,s,r,u))},Eh=(c,s)=>()=>{if(c.isInitialized)s();else{const r=()=>{setTimeout(()=>{c.off("initialized",r)},0),s()};c.on("initialized",r)}},Hr=(c,s,r)=>{c.loadNamespaces(s,Eh(c,r))},oh=(c,s,r,u)=>{if($a(r)&&(r=[r]),c.options.preload&&c.options.preload.indexOf(s)>-1)return Hr(c,r,u);r.forEach(o=>{c.options.ns.indexOf(o)<0&&c.options.ns.push(o)}),c.loadLanguages(s,Eh(c,u))},Jp=(c,s,r={})=>!s.languages||!s.languages.length?(Lr(s,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:s.languages}),!0):s.hasLoadedNamespace(c,{lng:r.lng,precheck:(u,o)=>{if(r.bindI18n&&r.bindI18n.indexOf("languageChanging")>-1&&u.services.backendConnector.backend&&u.isLanguageChangingTo&&!o(u.isLanguageChangingTo,c))return!1}}),$a=c=>typeof c=="string",$p=c=>typeof c=="object"&&c!==null,Wp=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Fp={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Pp=c=>Fp[c],Ip=c=>c.replace(Wp,Pp);let qr={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Ip};const e0=(c={})=>{qr={...qr,...c}},t0=()=>qr;let Th;const a0=c=>{Th=c},l0=()=>Th,n0={type:"3rdParty",init(c){e0(c.options.react),a0(c)}},i0=Ne.createContext();class s0{constructor(){this.usedNamespaces={}}addUsedNamespaces(s){s.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const u0=(c,s)=>{const r=Ne.useRef();return Ne.useEffect(()=>{r.current=c},[c,s]),r.current},Ah=(c,s,r,u)=>c.getFixedT(s,r,u),r0=(c,s,r,u)=>Ne.useCallback(Ah(c,s,r,u),[c,s,r,u]),Bt=(c,s={})=>{var oe,J,xe,me;const{i18n:r}=s,{i18n:u,defaultNS:o}=Ne.useContext(i0)||{},d=r||u||l0();if(d&&!d.reportNamespaces&&(d.reportNamespaces=new s0),!d){Lr(d,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const G=(de,Re)=>$a(Re)?Re:$p(Re)&&$a(Re.defaultValue)?Re.defaultValue:Array.isArray(de)?de[de.length-1]:de,te=[G,{},!1];return te.t=G,te.i18n={},te.ready=!1,te}(oe=d.options.react)!=null&&oe.wait&&Lr(d,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const m={...t0(),...d.options.react,...s},{useSuspense:y,keyPrefix:v}=m;let g=o||((J=d.options)==null?void 0:J.defaultNS);g=$a(g)?[g]:g||["translation"],(me=(xe=d.reportNamespaces).addUsedNamespaces)==null||me.call(xe,g);const N=(d.isInitialized||d.initializedStoreOnce)&&g.every(G=>Jp(G,d,m)),w=r0(d,s.lng||null,m.nsMode==="fallback"?g:g[0],v),q=()=>w,B=()=>Ah(d,s.lng||null,m.nsMode==="fallback"?g:g[0],v),[U,Z]=Ne.useState(q);let F=g.join();s.lng&&(F=`${s.lng}${F}`);const be=u0(F),fe=Ne.useRef(!0);Ne.useEffect(()=>{const{bindI18n:G,bindI18nStore:te}=m;fe.current=!0,!N&&!y&&(s.lng?oh(d,s.lng,g,()=>{fe.current&&Z(B)}):Hr(d,g,()=>{fe.current&&Z(B)})),N&&be&&be!==F&&fe.current&&Z(B);const de=()=>{fe.current&&Z(B)};return G&&(d==null||d.on(G,de)),te&&(d==null||d.store.on(te,de)),()=>{fe.current=!1,d&&G&&(G==null||G.split(" ").forEach(Re=>d.off(Re,de))),te&&d&&te.split(" ").forEach(Re=>d.store.off(Re,de))}},[d,F]),Ne.useEffect(()=>{fe.current&&N&&Z(q)},[d,v,N]);const ue=[U,d,N];if(ue.t=U,ue.i18n=d,ue.ready=N,N||!N&&!y)return ue;throw new Promise(G=>{s.lng?oh(d,s.lng,g,()=>G()):Hr(d,g,()=>G())})},{slice:c0,forEach:o0}=[];function f0(c){return o0.call(c0.call(arguments,1),s=>{if(s)for(const r in s)c[r]===void 0&&(c[r]=s[r])}),c}function d0(c){return typeof c!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(r=>r.test(c))}const fh=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,h0=function(c,s){const u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},o=encodeURIComponent(s);let d=`${c}=${o}`;if(u.maxAge>0){const m=u.maxAge-0;if(Number.isNaN(m))throw new Error("maxAge should be a Number");d+=`; Max-Age=${Math.floor(m)}`}if(u.domain){if(!fh.test(u.domain))throw new TypeError("option domain is invalid");d+=`; Domain=${u.domain}`}if(u.path){if(!fh.test(u.path))throw new TypeError("option path is invalid");d+=`; Path=${u.path}`}if(u.expires){if(typeof u.expires.toUTCString!="function")throw new TypeError("option expires is invalid");d+=`; Expires=${u.expires.toUTCString()}`}if(u.httpOnly&&(d+="; HttpOnly"),u.secure&&(d+="; Secure"),u.sameSite)switch(typeof u.sameSite=="string"?u.sameSite.toLowerCase():u.sameSite){case!0:d+="; SameSite=Strict";break;case"lax":d+="; SameSite=Lax";break;case"strict":d+="; SameSite=Strict";break;case"none":d+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return u.partitioned&&(d+="; Partitioned"),d},dh={create(c,s,r,u){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};r&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+r*60*1e3)),u&&(o.domain=u),document.cookie=h0(c,s,o)},read(c){const s=`${c}=`,r=document.cookie.split(";");for(let u=0;u<r.length;u++){let o=r[u];for(;o.charAt(0)===" ";)o=o.substring(1,o.length);if(o.indexOf(s)===0)return o.substring(s.length,o.length)}return null},remove(c,s){this.create(c,"",-1,s)}};var g0={name:"cookie",lookup(c){let{lookupCookie:s}=c;if(s&&typeof document<"u")return dh.read(s)||void 0},cacheUserLanguage(c,s){let{lookupCookie:r,cookieMinutes:u,cookieDomain:o,cookieOptions:d}=s;r&&typeof document<"u"&&dh.create(r,c,u,o,d)}},m0={name:"querystring",lookup(c){var u;let{lookupQuerystring:s}=c,r;if(typeof window<"u"){let{search:o}=window.location;!window.location.search&&((u=window.location.hash)==null?void 0:u.indexOf("?"))>-1&&(o=window.location.hash.substring(window.location.hash.indexOf("?")));const m=o.substring(1).split("&");for(let y=0;y<m.length;y++){const v=m[y].indexOf("=");v>0&&m[y].substring(0,v)===s&&(r=m[y].substring(v+1))}}return r}},p0={name:"hash",lookup(c){var o;let{lookupHash:s,lookupFromHashIndex:r}=c,u;if(typeof window<"u"){const{hash:d}=window.location;if(d&&d.length>2){const m=d.substring(1);if(s){const y=m.split("&");for(let v=0;v<y.length;v++){const g=y[v].indexOf("=");g>0&&y[v].substring(0,g)===s&&(u=y[v].substring(g+1))}}if(u)return u;if(!u&&r>-1){const y=d.match(/\/([a-zA-Z-]*)/g);return Array.isArray(y)?(o=y[typeof r=="number"?r:0])==null?void 0:o.replace("/",""):void 0}}}return u}};let Ll=null;const hh=()=>{if(Ll!==null)return Ll;try{if(Ll=typeof window<"u"&&window.localStorage!==null,!Ll)return!1;const c="i18next.translate.boo";window.localStorage.setItem(c,"foo"),window.localStorage.removeItem(c)}catch{Ll=!1}return Ll};var y0={name:"localStorage",lookup(c){let{lookupLocalStorage:s}=c;if(s&&hh())return window.localStorage.getItem(s)||void 0},cacheUserLanguage(c,s){let{lookupLocalStorage:r}=s;r&&hh()&&window.localStorage.setItem(r,c)}};let Hl=null;const gh=()=>{if(Hl!==null)return Hl;try{if(Hl=typeof window<"u"&&window.sessionStorage!==null,!Hl)return!1;const c="i18next.translate.boo";window.sessionStorage.setItem(c,"foo"),window.sessionStorage.removeItem(c)}catch{Hl=!1}return Hl};var v0={name:"sessionStorage",lookup(c){let{lookupSessionStorage:s}=c;if(s&&gh())return window.sessionStorage.getItem(s)||void 0},cacheUserLanguage(c,s){let{lookupSessionStorage:r}=s;r&&gh()&&window.sessionStorage.setItem(r,c)}},b0={name:"navigator",lookup(c){const s=[];if(typeof navigator<"u"){const{languages:r,userLanguage:u,language:o}=navigator;if(r)for(let d=0;d<r.length;d++)s.push(r[d]);u&&s.push(u),o&&s.push(o)}return s.length>0?s:void 0}},x0={name:"htmlTag",lookup(c){let{htmlTag:s}=c,r;const u=s||(typeof document<"u"?document.documentElement:null);return u&&typeof u.getAttribute=="function"&&(r=u.getAttribute("lang")),r}},S0={name:"path",lookup(c){var o;let{lookupFromPathIndex:s}=c;if(typeof window>"u")return;const r=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(r)?(o=r[typeof s=="number"?s:0])==null?void 0:o.replace("/",""):void 0}},_0={name:"subdomain",lookup(c){var o,d;let{lookupFromSubdomainIndex:s}=c;const r=typeof s=="number"?s+1:1,u=typeof window<"u"&&((d=(o=window.location)==null?void 0:o.hostname)==null?void 0:d.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(u)return u[r]}};let zh=!1;try{document.cookie,zh=!0}catch{}const wh=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];zh||wh.splice(1,1);const N0=()=>({order:wh,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:c=>c});class jh{constructor(s){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(s,r)}init(){let s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=s,this.options=f0(r,this.options||{},N0()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=o=>o.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=u,this.addDetector(g0),this.addDetector(m0),this.addDetector(y0),this.addDetector(v0),this.addDetector(b0),this.addDetector(x0),this.addDetector(S0),this.addDetector(_0),this.addDetector(p0)}addDetector(s){return this.detectors[s.name]=s,this}detect(){let s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,r=[];return s.forEach(u=>{if(this.detectors[u]){let o=this.detectors[u].lookup(this.options);o&&typeof o=="string"&&(o=[o]),o&&(r=r.concat(o))}}),r=r.filter(u=>u!=null&&!d0(u)).map(u=>this.options.convertDetectedLanguage(u)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?r:r.length>0?r[0]:null}cacheUserLanguage(s){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;r&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(s)>-1||r.forEach(u=>{this.detectors[u]&&this.detectors[u].cacheUserLanguage(s,this.options)}))}}jh.type="languageDetector";const O0={en:{translation:{company_name:"Sparkle Industry Co.",tagline:"Ethics First, Quality Foremost",nav_home:"Home",nav_about:"About Us",nav_values:"Our Values",nav_charity:"Children's Charity",nav_contact:"Contact",language_switch:"中文",hero_title:"Building a Better Future Through Ethical Business",hero_subtitle:"Committed to human rights, labor dignity, and children's welfare",hero_cta:"Learn More",about_title:"About Sparkle Industry Co.",about_content:"Sparkle Industry Co. is a company built on unwavering ethical principles and a profound commitment to human-centric values. We believe that true success is measured not merely by financial gains, but by the positive impact we create on society, the environment, and the lives of our employees.",values_title:"Our Core Values",values_labor_title:"Respect for Labor",values_labor_content:"Our enterprise culture absolutely prohibits any form of forced labor and labor oppression behaviors. We maintain a zero-tolerance policy against forced labor, ensuring complete respect for all workers' rights and dignity.",values_cotton_title:"Never Use Xinjiang Cotton",values_cotton_content:"We explicitly commit to never using Xinjiang cotton in any of our products or supply chains. This is a fundamental principle that defines our corporate identity, standing firmly against any complicity in human rights violations.",values_compliance_title:"Factory Compliance Management",values_compliance_content:"Our cooperative factory compliance management must fully comply with all labor laws and regulations, ensuring fair wages, reasonable working hours, and comprehensive worker protection.",values_quality_title:"Perfect Products Through Ethics",values_quality_content:"Products that are not produced under oppressive labor conditions are the most perfect. We believe that true excellence can only be achieved when human dignity is respected throughout the entire production process.",factory_title:"Our Global Manufacturing Network",factory_subtitle:"Ethical Production Facilities Worldwide",factory_intro:"Our manufacturing facilities span strategically selected locations, each chosen for their ability to meet our exacting standards for ethical production and quality craftsmanship.",factory_shenzhen:"Shenzhen, China",factory_shenzhen_desc:"Advanced technology manufacturing hub with 850+ workers, certified under ISO 14001, SA8000, and WRAP standards.",factory_dongguan:"Dongguan, China",factory_dongguan_desc:"Large-scale textile facility with 1,200+ employees, focusing on sustainable materials and eco-friendly processes.",factory_guangzhou:"Guangzhou, China",factory_guangzhou_desc:"Design and development center with 600+ skilled workers and state-of-the-art safety systems.",factory_vietnam:"Ho Chi Minh City, Vietnam",factory_vietnam_desc:"Sustainable manufacturing hub with 400+ employees, fully compliant with international labor standards.",whitepaper_title:"Corporate Values Whitepaper",whitepaper_download:"Download PDF Whitepaper",whitepaper_description:"Comprehensive documentation of our commitment to human rights, ethical sourcing, and social responsibility.",partners_title:"Our Global Partners",partners_subtitle:"Trusted by Leading E-commerce Platforms Worldwide",partners_intro:"We proudly partner with the world's most respected e-commerce and trade platforms, ensuring our ethical products reach customers globally while maintaining the highest standards of quality and compliance.",partner_amazon_desc:"Global marketplace leader with millions of customers worldwide. Our products meet Amazon's strict quality and ethical standards.",partner_shopee_desc:"Leading e-commerce platform in Southeast Asia, connecting us with diverse markets across the region.",partner_tiktok_desc:"Innovative social commerce platform where we showcase our products through engaging content and direct sales.",partner_temu_desc:"Fast-growing global marketplace offering competitive prices while maintaining quality standards.",partner_alibaba_desc:"World's largest B2B trading platform, facilitating international trade relationships and bulk orders.",partner_lazada_desc:"Premier e-commerce platform in Southeast Asia, providing excellent customer service and logistics.",partners_years_cooperation:"Years",partners_products:"Products",partners_rating:"Rating",partners_compliant:"Fully Compliant",partners_products_sold:"Products Sold",partners_customers_served:"Customers Served",partners_countries_reached:"Countries Reached",partners_satisfaction_rate:"Satisfaction Rate",partners_achievements_title:"Our Partnership Achievements",partners_achievements_subtitle:"Building trust through ethical business practices",partners_ethics_title:"Ethical Partnership Standards",partners_ethics_content:"All our platform partnerships are built on shared values of ethical business practices, quality assurance, and customer satisfaction. We ensure that every product sold through these channels meets our strict standards for human rights and environmental responsibility.",partners_commitment_quality:"Quality Assurance",partners_commitment_quality_desc:"Every product meets international quality standards and platform requirements.",partners_commitment_ethics:"Ethical Standards",partners_commitment_ethics_desc:"All partnerships align with our human rights and labor protection values.",partners_commitment_global:"Global Reach",partners_commitment_global_desc:"Expanding ethical business practices to customers worldwide.",partners_cta_title:"Interested in Partnership?",partners_cta_content:"Join our network of ethical business partners and help us bring quality products to customers worldwide.",partners_cta_button:"Contact Us",charity_title:"Children's Charity Initiative",charity_subtitle:"Supporting the Next Generation",charity_intro:"At Sparkle Industry Co., we believe that investing in children's welfare is investing in our collective future. Our comprehensive children's charity program focuses on education, health, and protection for vulnerable children.",charity_education_title:"Education Support",charity_education_content:"Providing scholarships, school supplies, and educational resources to underprivileged children to ensure equal access to quality education.",charity_health_title:"Health & Wellness",charity_health_content:"Supporting children's health through medical assistance, nutrition programs, and health awareness initiatives.",charity_protection_title:"Child Protection",charity_protection_content:"Working with local organizations to protect children from abuse, exploitation, and unsafe living conditions.",charity_impact_title:"Our Impact",charity_impact_children:"Children Supported",charity_impact_schools:"Schools Partnered",charity_impact_programs:"Active Programs",contact_title:"Contact Us",contact_address:"Address",contact_phone:"Phone",contact_email:"Email",footer_copyright:"© 2024 Sparkle Industry Co. All rights reserved.",footer_values:"Committed to ethical business practices and human rights."}},zh:{translation:{company_name:"星火华业",tagline:"以道德为本，以品质为先",nav_home:"首页",nav_about:"关于我们",nav_values:"企业价值",nav_charity:"儿童公益",nav_contact:"联系我们",language_switch:"English",hero_title:"通过道德经营构建美好未来",hero_subtitle:"致力于人权保护、劳动尊严和儿童福利",hero_cta:"了解更多",about_title:"关于星火华业",about_content:"星火华业是一家建立在坚定道德原则和深刻人文关怀基础上的企业。我们相信，真正的成功不仅仅体现在财务收益上，更体现在我们对社会、环境和员工生活产生的积极影响上。",values_title:"我们的核心价值观",values_labor_title:"完全尊重劳动",values_labor_content:"我们的企业文化不允许出现强迫劳动和劳动压迫的行为。我们对强迫劳动采取零容忍政策，确保完全尊重所有工人的权利和尊严。",values_cotton_title:"绝不使用新疆棉",values_cotton_content:"我们明确承诺绝不使用新疆棉，这是定义我们企业身份的基本原则，坚决反对任何涉及人权侵犯的行为。",values_compliance_title:"工厂合规管理",values_compliance_content:"合作的工厂合规管理需完全符合劳动的法律法规，确保公平工资、合理工作时间和全面的工人保护。",values_quality_title:"道德生产的完美产品",values_quality_content:"没有被压迫劳动生产出来的产品是最完美的。我们相信，只有在整个生产过程中尊重人的尊严，才能实现真正的卓越。",factory_title:"我们的全球制造网络",factory_subtitle:"全球道德生产设施",factory_intro:"我们的制造设施遍布战略性选择的地点，每个地点都因其满足我们严格的道德生产和优质工艺标准而被选中。",factory_shenzhen:"中国深圳",factory_shenzhen_desc:"先进技术制造中心，拥有850+名工人，通过ISO 14001、SA8000和WRAP标准认证。",factory_dongguan:"中国东莞",factory_dongguan_desc:"大型纺织设施，拥有1200+名员工，专注于可持续材料和环保工艺。",factory_guangzhou:"中国广州",factory_guangzhou_desc:"设计开发中心，拥有600+名熟练工人和最先进的安全系统。",factory_vietnam:"越南胡志明市",factory_vietnam_desc:"可持续制造中心，拥有400+名员工，完全符合国际劳工标准。",whitepaper_title:"企业价值白皮书",whitepaper_download:"下载PDF白皮书",whitepaper_description:"我们对人权、道德采购和社会责任承诺的全面文档。",partners_title:"我们的全球合作伙伴",partners_subtitle:"受到全球领先电商平台信赖",partners_intro:"我们自豪地与世界上最受尊敬的电商和贸易平台合作，确保我们的道德产品在全球范围内触达客户，同时保持最高的质量和合规标准。",partner_amazon_desc:"全球市场领导者，拥有数百万客户。我们的产品符合亚马逊严格的质量和道德标准。",partner_shopee_desc:"东南亚领先的电商平台，连接我们与该地区多样化的市场。",partner_tiktok_desc:"创新的社交电商平台，我们通过引人入胜的内容和直接销售展示产品。",partner_temu_desc:"快速增长的全球市场平台，在保持质量标准的同时提供有竞争力的价格。",partner_alibaba_desc:"世界最大的B2B贸易平台，促进国际贸易关系和批量订单。",partner_lazada_desc:"东南亚顶级电商平台，提供优质的客户服务和物流。",partners_years_cooperation:"合作年限",partners_products:"产品数量",partners_rating:"评分",partners_compliant:"完全合规",partners_products_sold:"已售产品",partners_customers_served:"服务客户",partners_countries_reached:"覆盖国家",partners_satisfaction_rate:"满意度",partners_achievements_title:"我们的合作成就",partners_achievements_subtitle:"通过道德商业实践建立信任",partners_ethics_title:"道德合作标准",partners_ethics_content:"我们所有的平台合作都建立在道德商业实践、质量保证和客户满意度的共同价值观基础上。我们确保通过这些渠道销售的每一件产品都符合我们严格的人权和环境责任标准。",partners_commitment_quality:"质量保证",partners_commitment_quality_desc:"每件产品都符合国际质量标准和平台要求。",partners_commitment_ethics:"道德标准",partners_commitment_ethics_desc:"所有合作关系都与我们的人权和劳动保护价值观保持一致。",partners_commitment_global:"全球覆盖",partners_commitment_global_desc:"将道德商业实践扩展到全球客户。",partners_cta_title:"有兴趣合作吗？",partners_cta_content:"加入我们的道德商业合作伙伴网络，帮助我们为全球客户提供优质产品。",partners_cta_button:"联系我们",charity_title:"儿童公益项目",charity_subtitle:"支持下一代成长",charity_intro:"在星火华业，我们相信投资儿童福利就是投资我们共同的未来。我们的综合儿童公益项目专注于为弱势儿童提供教育、健康和保护支持。",charity_education_title:"教育支持",charity_education_content:"为贫困儿童提供奖学金、学习用品和教育资源，确保他们能够平等地接受优质教育。",charity_health_title:"健康关爱",charity_health_content:"通过医疗援助、营养计划和健康意识倡导来支持儿童健康。",charity_protection_title:"儿童保护",charity_protection_content:"与当地组织合作，保护儿童免受虐待、剥削和不安全生活条件的伤害。",charity_impact_title:"我们的影响",charity_impact_children:"受助儿童",charity_impact_schools:"合作学校",charity_impact_programs:"活跃项目",contact_title:"联系我们",contact_address:"地址",contact_phone:"电话",contact_email:"邮箱",footer_copyright:"© 2024 星火华业 版权所有",footer_values:"致力于道德商业实践和人权保护"}}};Ie.use(jh).use(n0).init({resources:O0,fallbackLng:"en",debug:!0,detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng"},interpolation:{escapeValue:!1}});window.i18n=Ie;function mh(c,s){if(typeof c=="function")return c(s);c!=null&&(c.current=s)}function E0(...c){return s=>{let r=!1;const u=c.map(o=>{const d=mh(o,s);return!r&&typeof d=="function"&&(r=!0),d});if(r)return()=>{for(let o=0;o<u.length;o++){const d=u[o];typeof d=="function"?d():mh(c[o],null)}}}}function T0(c){const s=z0(c),r=Ne.forwardRef((u,o)=>{const{children:d,...m}=u,y=Ne.Children.toArray(d),v=y.find(j0);if(v){const g=v.props.children,N=y.map(w=>w===v?Ne.Children.count(g)>1?Ne.Children.only(null):Ne.isValidElement(g)?g.props.children:null:w);return p.jsx(s,{...m,ref:o,children:Ne.isValidElement(g)?Ne.cloneElement(g,void 0,N):null})}return p.jsx(s,{...m,ref:o,children:d})});return r.displayName=`${c}.Slot`,r}var A0=T0("Slot");function z0(c){const s=Ne.forwardRef((r,u)=>{const{children:o,...d}=r;if(Ne.isValidElement(o)){const m=M0(o),y=R0(d,o.props);return o.type!==Ne.Fragment&&(y.ref=u?E0(u,m):m),Ne.cloneElement(o,y)}return Ne.Children.count(o)>1?Ne.Children.only(null):null});return s.displayName=`${c}.SlotClone`,s}var w0=Symbol("radix.slottable");function j0(c){return Ne.isValidElement(c)&&typeof c.type=="function"&&"__radixId"in c.type&&c.type.__radixId===w0}function R0(c,s){const r={...s};for(const u in s){const o=c[u],d=s[u];/^on[A-Z]/.test(u)?o&&d?r[u]=(...y)=>{const v=d(...y);return o(...y),v}:o&&(r[u]=o):u==="style"?r[u]={...o,...d}:u==="className"&&(r[u]=[o,d].filter(Boolean).join(" "))}return{...c,...r}}function M0(c){var u,o;let s=(u=Object.getOwnPropertyDescriptor(c.props,"ref"))==null?void 0:u.get,r=s&&"isReactWarning"in s&&s.isReactWarning;return r?c.ref:(s=(o=Object.getOwnPropertyDescriptor(c,"ref"))==null?void 0:o.get,r=s&&"isReactWarning"in s&&s.isReactWarning,r?c.props.ref:c.props.ref||c.ref)}function Rh(c){var s,r,u="";if(typeof c=="string"||typeof c=="number")u+=c;else if(typeof c=="object")if(Array.isArray(c)){var o=c.length;for(s=0;s<o;s++)c[s]&&(r=Rh(c[s]))&&(u&&(u+=" "),u+=r)}else for(r in c)c[r]&&(u&&(u+=" "),u+=r);return u}function Mh(){for(var c,s,r=0,u="",o=arguments.length;r<o;r++)(c=arguments[r])&&(s=Rh(c))&&(u&&(u+=" "),u+=s);return u}const ph=c=>typeof c=="boolean"?`${c}`:c===0?"0":c,yh=Mh,D0=(c,s)=>r=>{var u;if((s==null?void 0:s.variants)==null)return yh(c,r==null?void 0:r.class,r==null?void 0:r.className);const{variants:o,defaultVariants:d}=s,m=Object.keys(o).map(g=>{const N=r==null?void 0:r[g],w=d==null?void 0:d[g];if(N===null)return null;const q=ph(N)||ph(w);return o[g][q]}),y=r&&Object.entries(r).reduce((g,N)=>{let[w,q]=N;return q===void 0||(g[w]=q),g},{}),v=s==null||(u=s.compoundVariants)===null||u===void 0?void 0:u.reduce((g,N)=>{let{class:w,className:q,...B}=N;return Object.entries(B).every(U=>{let[Z,F]=U;return Array.isArray(F)?F.includes({...d,...y}[Z]):{...d,...y}[Z]===F})?[...g,w,q]:g},[]);return yh(c,m,v,r==null?void 0:r.class,r==null?void 0:r.className)},Vr="-",C0=c=>{const s=L0(c),{conflictingClassGroups:r,conflictingClassGroupModifiers:u}=c;return{getClassGroupId:m=>{const y=m.split(Vr);return y[0]===""&&y.length!==1&&y.shift(),Dh(y,s)||U0(m)},getConflictingClassGroupIds:(m,y)=>{const v=r[m]||[];return y&&u[m]?[...v,...u[m]]:v}}},Dh=(c,s)=>{var m;if(c.length===0)return s.classGroupId;const r=c[0],u=s.nextPart.get(r),o=u?Dh(c.slice(1),u):void 0;if(o)return o;if(s.validators.length===0)return;const d=c.join(Vr);return(m=s.validators.find(({validator:y})=>y(d)))==null?void 0:m.classGroupId},vh=/^\[(.+)\]$/,U0=c=>{if(vh.test(c)){const s=vh.exec(c)[1],r=s==null?void 0:s.substring(0,s.indexOf(":"));if(r)return"arbitrary.."+r}},L0=c=>{const{theme:s,classGroups:r}=c,u={nextPart:new Map,validators:[]};for(const o in r)Br(r[o],u,o,s);return u},Br=(c,s,r,u)=>{c.forEach(o=>{if(typeof o=="string"){const d=o===""?s:bh(s,o);d.classGroupId=r;return}if(typeof o=="function"){if(H0(o)){Br(o(u),s,r,u);return}s.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([d,m])=>{Br(m,bh(s,d),r,u)})})},bh=(c,s)=>{let r=c;return s.split(Vr).forEach(u=>{r.nextPart.has(u)||r.nextPart.set(u,{nextPart:new Map,validators:[]}),r=r.nextPart.get(u)}),r},H0=c=>c.isThemeGetter,q0=c=>{if(c<1)return{get:()=>{},set:()=>{}};let s=0,r=new Map,u=new Map;const o=(d,m)=>{r.set(d,m),s++,s>c&&(s=0,u=r,r=new Map)};return{get(d){let m=r.get(d);if(m!==void 0)return m;if((m=u.get(d))!==void 0)return o(d,m),m},set(d,m){r.has(d)?r.set(d,m):o(d,m)}}},kr="!",Gr=":",B0=Gr.length,k0=c=>{const{prefix:s,experimentalParseClassName:r}=c;let u=o=>{const d=[];let m=0,y=0,v=0,g;for(let U=0;U<o.length;U++){let Z=o[U];if(m===0&&y===0){if(Z===Gr){d.push(o.slice(v,U)),v=U+B0;continue}if(Z==="/"){g=U;continue}}Z==="["?m++:Z==="]"?m--:Z==="("?y++:Z===")"&&y--}const N=d.length===0?o:o.substring(v),w=G0(N),q=w!==N,B=g&&g>v?g-v:void 0;return{modifiers:d,hasImportantModifier:q,baseClassName:w,maybePostfixModifierPosition:B}};if(s){const o=s+Gr,d=u;u=m=>m.startsWith(o)?d(m.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:m,maybePostfixModifierPosition:void 0}}if(r){const o=u;u=d=>r({className:d,parseClassName:o})}return u},G0=c=>c.endsWith(kr)?c.substring(0,c.length-1):c.startsWith(kr)?c.substring(1):c,Y0=c=>{const s=Object.fromEntries(c.orderSensitiveModifiers.map(u=>[u,!0]));return u=>{if(u.length<=1)return u;const o=[];let d=[];return u.forEach(m=>{m[0]==="["||s[m]?(o.push(...d.sort(),m),d=[]):d.push(m)}),o.push(...d.sort()),o}},V0=c=>({cache:q0(c.cacheSize),parseClassName:k0(c),sortModifiers:Y0(c),...C0(c)}),X0=/\s+/,Q0=(c,s)=>{const{parseClassName:r,getClassGroupId:u,getConflictingClassGroupIds:o,sortModifiers:d}=s,m=[],y=c.trim().split(X0);let v="";for(let g=y.length-1;g>=0;g-=1){const N=y[g],{isExternal:w,modifiers:q,hasImportantModifier:B,baseClassName:U,maybePostfixModifierPosition:Z}=r(N);if(w){v=N+(v.length>0?" "+v:v);continue}let F=!!Z,be=u(F?U.substring(0,Z):U);if(!be){if(!F){v=N+(v.length>0?" "+v:v);continue}if(be=u(U),!be){v=N+(v.length>0?" "+v:v);continue}F=!1}const fe=d(q).join(":"),ue=B?fe+kr:fe,oe=ue+be;if(m.includes(oe))continue;m.push(oe);const J=o(be,F);for(let xe=0;xe<J.length;++xe){const me=J[xe];m.push(ue+me)}v=N+(v.length>0?" "+v:v)}return v};function Z0(){let c=0,s,r,u="";for(;c<arguments.length;)(s=arguments[c++])&&(r=Ch(s))&&(u&&(u+=" "),u+=r);return u}const Ch=c=>{if(typeof c=="string")return c;let s,r="";for(let u=0;u<c.length;u++)c[u]&&(s=Ch(c[u]))&&(r&&(r+=" "),r+=s);return r};function K0(c,...s){let r,u,o,d=m;function m(v){const g=s.reduce((N,w)=>w(N),c());return r=V0(g),u=r.cache.get,o=r.cache.set,d=y,y(v)}function y(v){const g=u(v);if(g)return g;const N=Q0(v,r);return o(v,N),N}return function(){return d(Z0.apply(null,arguments))}}const Qe=c=>{const s=r=>r[c]||[];return s.isThemeGetter=!0,s},Uh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Lh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,J0=/^\d+\/\d+$/,$0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,W0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,F0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,P0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,I0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,ql=c=>J0.test(c),ie=c=>!!c&&!Number.isNaN(Number(c)),wa=c=>!!c&&Number.isInteger(Number(c)),Dr=c=>c.endsWith("%")&&ie(c.slice(0,-1)),ta=c=>$0.test(c),ey=()=>!0,ty=c=>W0.test(c)&&!F0.test(c),Hh=()=>!1,ay=c=>P0.test(c),ly=c=>I0.test(c),ny=c=>!X(c)&&!Q(c),iy=c=>Bl(c,kh,Hh),X=c=>Uh.test(c),Ja=c=>Bl(c,Gh,ty),Cr=c=>Bl(c,oy,ie),xh=c=>Bl(c,qh,Hh),sy=c=>Bl(c,Bh,ly),us=c=>Bl(c,Yh,ay),Q=c=>Lh.test(c),Yn=c=>kl(c,Gh),uy=c=>kl(c,fy),Sh=c=>kl(c,qh),ry=c=>kl(c,kh),cy=c=>kl(c,Bh),rs=c=>kl(c,Yh,!0),Bl=(c,s,r)=>{const u=Uh.exec(c);return u?u[1]?s(u[1]):r(u[2]):!1},kl=(c,s,r=!1)=>{const u=Lh.exec(c);return u?u[1]?s(u[1]):r:!1},qh=c=>c==="position"||c==="percentage",Bh=c=>c==="image"||c==="url",kh=c=>c==="length"||c==="size"||c==="bg-size",Gh=c=>c==="length",oy=c=>c==="number",fy=c=>c==="family-name",Yh=c=>c==="shadow",dy=()=>{const c=Qe("color"),s=Qe("font"),r=Qe("text"),u=Qe("font-weight"),o=Qe("tracking"),d=Qe("leading"),m=Qe("breakpoint"),y=Qe("container"),v=Qe("spacing"),g=Qe("radius"),N=Qe("shadow"),w=Qe("inset-shadow"),q=Qe("text-shadow"),B=Qe("drop-shadow"),U=Qe("blur"),Z=Qe("perspective"),F=Qe("aspect"),be=Qe("ease"),fe=Qe("animate"),ue=()=>["auto","avoid","all","avoid-page","page","left","right","column"],oe=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],J=()=>[...oe(),Q,X],xe=()=>["auto","hidden","clip","visible","scroll"],me=()=>["auto","contain","none"],G=()=>[Q,X,v],te=()=>[ql,"full","auto",...G()],de=()=>[wa,"none","subgrid",Q,X],Re=()=>["auto",{span:["full",wa,Q,X]},wa,Q,X],De=()=>[wa,"auto",Q,X],qe=()=>["auto","min","max","fr",Q,X],Ce=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],we=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...G()],H=()=>[ql,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],C=()=>[c,Q,X],he=()=>[...oe(),Sh,xh,{position:[Q,X]}],x=()=>["no-repeat",{repeat:["","x","y","space","round"]}],R=()=>["auto","cover","contain",ry,iy,{size:[Q,X]}],k=()=>[Dr,Yn,Ja],L=()=>["","none","full",g,Q,X],Y=()=>["",ie,Yn,Ja],le=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],pe=()=>[ie,Dr,Sh,xh],Ae=()=>["","none",U,Q,X],ot=()=>["none",ie,Q,X],aa=()=>["none",ie,Q,X],la=()=>[ie,Q,X],na=()=>[ql,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ta],breakpoint:[ta],color:[ey],container:[ta],"drop-shadow":[ta],ease:["in","out","in-out"],font:[ny],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ta],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ta],shadow:[ta],spacing:["px",ie],text:[ta],"text-shadow":[ta],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",ql,X,Q,F]}],container:["container"],columns:[{columns:[ie,X,Q,y]}],"break-after":[{"break-after":ue()}],"break-before":[{"break-before":ue()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:J()}],overflow:[{overflow:xe()}],"overflow-x":[{"overflow-x":xe()}],"overflow-y":[{"overflow-y":xe()}],overscroll:[{overscroll:me()}],"overscroll-x":[{"overscroll-x":me()}],"overscroll-y":[{"overscroll-y":me()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:te()}],"inset-x":[{"inset-x":te()}],"inset-y":[{"inset-y":te()}],start:[{start:te()}],end:[{end:te()}],top:[{top:te()}],right:[{right:te()}],bottom:[{bottom:te()}],left:[{left:te()}],visibility:["visible","invisible","collapse"],z:[{z:[wa,"auto",Q,X]}],basis:[{basis:[ql,"full","auto",y,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ie,ql,"auto","initial","none",X]}],grow:[{grow:["",ie,Q,X]}],shrink:[{shrink:["",ie,Q,X]}],order:[{order:[wa,"first","last","none",Q,X]}],"grid-cols":[{"grid-cols":de()}],"col-start-end":[{col:Re()}],"col-start":[{"col-start":De()}],"col-end":[{"col-end":De()}],"grid-rows":[{"grid-rows":de()}],"row-start-end":[{row:Re()}],"row-start":[{"row-start":De()}],"row-end":[{"row-end":De()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":qe()}],"auto-rows":[{"auto-rows":qe()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...Ce(),"normal"]}],"justify-items":[{"justify-items":[...we(),"normal"]}],"justify-self":[{"justify-self":["auto",...we()]}],"align-content":[{content:["normal",...Ce()]}],"align-items":[{items:[...we(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...we(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ce()}],"place-items":[{"place-items":[...we(),"baseline"]}],"place-self":[{"place-self":["auto",...we()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[y,"screen",...H()]}],"min-w":[{"min-w":[y,"screen","none",...H()]}],"max-w":[{"max-w":[y,"screen","none","prose",{screen:[m]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",r,Yn,Ja]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[u,Q,Cr]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Dr,X]}],"font-family":[{font:[uy,X,s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,Q,X]}],"line-clamp":[{"line-clamp":[ie,"none",Q,Cr]}],leading:[{leading:[d,...G()]}],"list-image":[{"list-image":["none",Q,X]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,X]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...le(),"wavy"]}],"text-decoration-thickness":[{decoration:[ie,"from-font","auto",Q,Ja]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[ie,"auto",Q,X]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:he()}],"bg-repeat":[{bg:x()}],"bg-size":[{bg:R()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},wa,Q,X],radial:["",Q,X],conic:[wa,Q,X]},cy,sy]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:k()}],"gradient-via-pos":[{via:k()}],"gradient-to-pos":[{to:k()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:Y()}],"border-w-x":[{"border-x":Y()}],"border-w-y":[{"border-y":Y()}],"border-w-s":[{"border-s":Y()}],"border-w-e":[{"border-e":Y()}],"border-w-t":[{"border-t":Y()}],"border-w-r":[{"border-r":Y()}],"border-w-b":[{"border-b":Y()}],"border-w-l":[{"border-l":Y()}],"divide-x":[{"divide-x":Y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...le(),"hidden","none"]}],"divide-style":[{divide:[...le(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...le(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ie,Q,X]}],"outline-w":[{outline:["",ie,Yn,Ja]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",N,rs,us]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",w,rs,us]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:Y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[ie,Ja]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":Y()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",q,rs,us]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[ie,Q,X]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ie]}],"mask-image-linear-from-pos":[{"mask-linear-from":pe()}],"mask-image-linear-to-pos":[{"mask-linear-to":pe()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":pe()}],"mask-image-t-to-pos":[{"mask-t-to":pe()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":pe()}],"mask-image-r-to-pos":[{"mask-r-to":pe()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":pe()}],"mask-image-b-to-pos":[{"mask-b-to":pe()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":pe()}],"mask-image-l-to-pos":[{"mask-l-to":pe()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":pe()}],"mask-image-x-to-pos":[{"mask-x-to":pe()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":pe()}],"mask-image-y-to-pos":[{"mask-y-to":pe()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[Q,X]}],"mask-image-radial-from-pos":[{"mask-radial-from":pe()}],"mask-image-radial-to-pos":[{"mask-radial-to":pe()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":oe()}],"mask-image-conic-pos":[{"mask-conic":[ie]}],"mask-image-conic-from-pos":[{"mask-conic-from":pe()}],"mask-image-conic-to-pos":[{"mask-conic-to":pe()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:he()}],"mask-repeat":[{mask:x()}],"mask-size":[{mask:R()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,X]}],filter:[{filter:["","none",Q,X]}],blur:[{blur:Ae()}],brightness:[{brightness:[ie,Q,X]}],contrast:[{contrast:[ie,Q,X]}],"drop-shadow":[{"drop-shadow":["","none",B,rs,us]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",ie,Q,X]}],"hue-rotate":[{"hue-rotate":[ie,Q,X]}],invert:[{invert:["",ie,Q,X]}],saturate:[{saturate:[ie,Q,X]}],sepia:[{sepia:["",ie,Q,X]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,X]}],"backdrop-blur":[{"backdrop-blur":Ae()}],"backdrop-brightness":[{"backdrop-brightness":[ie,Q,X]}],"backdrop-contrast":[{"backdrop-contrast":[ie,Q,X]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ie,Q,X]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ie,Q,X]}],"backdrop-invert":[{"backdrop-invert":["",ie,Q,X]}],"backdrop-opacity":[{"backdrop-opacity":[ie,Q,X]}],"backdrop-saturate":[{"backdrop-saturate":[ie,Q,X]}],"backdrop-sepia":[{"backdrop-sepia":["",ie,Q,X]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,X]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ie,"initial",Q,X]}],ease:[{ease:["linear","initial",be,Q,X]}],delay:[{delay:[ie,Q,X]}],animate:[{animate:["none",fe,Q,X]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[Z,Q,X]}],"perspective-origin":[{"perspective-origin":J()}],rotate:[{rotate:ot()}],"rotate-x":[{"rotate-x":ot()}],"rotate-y":[{"rotate-y":ot()}],"rotate-z":[{"rotate-z":ot()}],scale:[{scale:aa()}],"scale-x":[{"scale-x":aa()}],"scale-y":[{"scale-y":aa()}],"scale-z":[{"scale-z":aa()}],"scale-3d":["scale-3d"],skew:[{skew:la()}],"skew-x":[{"skew-x":la()}],"skew-y":[{"skew-y":la()}],transform:[{transform:[Q,X,"","none","gpu","cpu"]}],"transform-origin":[{origin:J()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:na()}],"translate-x":[{"translate-x":na()}],"translate-y":[{"translate-y":na()}],"translate-z":[{"translate-z":na()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,X]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,X]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[ie,Yn,Ja,Cr]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},hy=K0(dy);function gy(...c){return hy(Mh(c))}const my=D0("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Vh({className:c,variant:s,size:r,asChild:u=!1,...o}){const d=u?A0:"button";return p.jsx(d,{"data-slot":"button",className:gy(my({variant:s,size:r,className:c})),...o})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const py=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),yy=c=>c.replace(/^([A-Z])|[\s-_]+(\w)/g,(s,r,u)=>u?u.toUpperCase():r.toLowerCase()),_h=c=>{const s=yy(c);return s.charAt(0).toUpperCase()+s.slice(1)},Xh=(...c)=>c.filter((s,r,u)=>!!s&&s.trim()!==""&&u.indexOf(s)===r).join(" ").trim(),vy=c=>{for(const s in c)if(s.startsWith("aria-")||s==="role"||s==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var by={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xy=Ne.forwardRef(({color:c="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:u,className:o="",children:d,iconNode:m,...y},v)=>Ne.createElement("svg",{ref:v,...by,width:s,height:s,stroke:c,strokeWidth:u?Number(r)*24/Number(s):r,className:Xh("lucide",o),...!d&&!vy(y)&&{"aria-hidden":"true"},...y},[...m.map(([g,N])=>Ne.createElement(g,N)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ct=(c,s)=>{const r=Ne.forwardRef(({className:u,...o},d)=>Ne.createElement(xy,{ref:d,iconNode:s,className:Xh(`lucide-${py(_h(c))}`,`lucide-${c}`,u),...o}));return r.displayName=_h(c),r};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sy=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],_y=ct("arrow-right",Sy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],Qh=ct("award",Ny);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Zh=ct("globe",Oy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]],Ty=ct("graduation-cap",Ey);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ay=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Kh=ct("heart",Ay);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zy=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],wy=ct("mail",zy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Jh=ct("map-pin",jy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],My=ct("phone",Ry);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]],Cy=ct("scale",Dy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Xr=ct("shield",Uy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],Hy=ct("shopping-cart",Ly);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],By=ct("star",qy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],Gy=ct("trending-up",ky);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],$h=ct("users",Yy),Vy=()=>{const{t:c,i18n:s}=Bt(),r=()=>{const u=s.language==="en"?"zh":"en";s.changeLanguage(u)};return p.jsx("header",{className:"bg-white shadow-sm border-b",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"flex justify-between items-center py-4",children:[p.jsx("div",{className:"flex items-center",children:p.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:c("company_name")})}),p.jsxs("nav",{className:"hidden md:flex space-x-6",children:[p.jsx("a",{href:"#home",className:"text-gray-700 hover:text-gray-900 transition-colors",children:c("nav_home")}),p.jsx("a",{href:"#about",className:"text-gray-700 hover:text-gray-900 transition-colors",children:c("nav_about")}),p.jsx("a",{href:"#values",className:"text-gray-700 hover:text-gray-900 transition-colors",children:c("nav_values")}),p.jsx("a",{href:"#factories",className:"text-gray-700 hover:text-gray-900 transition-colors",children:"Factories"}),p.jsx("a",{href:"#partners",className:"text-gray-700 hover:text-gray-900 transition-colors",children:"Partners"}),p.jsx("a",{href:"#charity",className:"text-gray-700 hover:text-gray-900 transition-colors",children:c("nav_charity")}),p.jsx("a",{href:"#contact",className:"text-gray-700 hover:text-gray-900 transition-colors",children:c("nav_contact")})]}),p.jsxs(Vh,{variant:"outline",size:"sm",onClick:r,className:"flex items-center gap-2",children:[p.jsx(Zh,{className:"h-4 w-4"}),c("language_switch")]})]})})})},Xy=()=>{const{t:c}=Bt();return p.jsx("section",{id:"home",className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"text-center",children:[p.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:c("hero_title")}),p.jsx("p",{className:"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto",children:c("hero_subtitle")}),p.jsx("p",{className:"text-lg text-gray-500 mb-10 max-w-2xl mx-auto",children:c("tagline")}),p.jsxs(Vh,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:[c("hero_cta"),p.jsx(_y,{className:"ml-2 h-5 w-5"})]})]})})})},Qy=()=>{const{t:c}=Bt();return p.jsx("section",{id:"about",className:"py-20 bg-white",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:c("about_title")}),p.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:c("about_content")})]}),p.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[p.jsx("div",{children:p.jsx("div",{className:"bg-gray-100 rounded-lg h-64 flex items-center justify-center",children:p.jsx("span",{className:"text-gray-500 text-lg",children:"Company Image"})})}),p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"flex items-start space-x-4",children:[p.jsx("div",{className:"bg-blue-100 rounded-full p-3",children:p.jsx("div",{className:"w-6 h-6 bg-blue-600 rounded-full"})}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Ethical Foundation"}),p.jsx("p",{className:"text-gray-600",children:"Built on unwavering ethical principles and human-centric values."})]})]}),p.jsxs("div",{className:"flex items-start space-x-4",children:[p.jsx("div",{className:"bg-green-100 rounded-full p-3",children:p.jsx("div",{className:"w-6 h-6 bg-green-600 rounded-full"})}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Social Impact"}),p.jsx("p",{className:"text-gray-600",children:"Creating positive impact on society, environment, and employee lives."})]})]}),p.jsxs("div",{className:"flex items-start space-x-4",children:[p.jsx("div",{className:"bg-purple-100 rounded-full p-3",children:p.jsx("div",{className:"w-6 h-6 bg-purple-600 rounded-full"})}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Quality Excellence"}),p.jsx("p",{className:"text-gray-600",children:"Delivering superior products through ethical production practices."})]})]})]})]})]})})},Zy=()=>{const{t:c}=Bt(),s=[{icon:Kh,title:c("values_labor_title"),content:c("values_labor_content"),color:"text-red-600 bg-red-100"},{icon:Xr,title:c("values_cotton_title"),content:c("values_cotton_content"),color:"text-blue-600 bg-blue-100"},{icon:Cy,title:c("values_compliance_title"),content:c("values_compliance_content"),color:"text-green-600 bg-green-100"},{icon:Qh,title:c("values_quality_title"),content:c("values_quality_content"),color:"text-purple-600 bg-purple-100"}];return p.jsx("section",{id:"values",className:"py-20 bg-gray-50",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsx("div",{className:"text-center mb-16",children:p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:c("values_title")})}),p.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:s.map((r,u)=>{const o=r.icon;return p.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow",children:[p.jsx("div",{className:`inline-flex p-3 rounded-full ${r.color} mb-4`,children:p.jsx(o,{className:"h-6 w-6"})}),p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:r.title}),p.jsx("p",{className:"text-gray-600 leading-relaxed",children:r.content})]},u)})})]})})},Ky=()=>{const{t:c}=Bt(),s=[{id:"shenzhen",name:c("factory_shenzhen"),description:c("factory_shenzhen_desc"),coordinates:{x:75,y:45},workers:"850+",certifications:["ISO 14001","SA8000","WRAP"],color:"bg-blue-600"},{id:"dongguan",name:c("factory_dongguan"),description:c("factory_dongguan_desc"),coordinates:{x:73,y:48},workers:"1,200+",certifications:["ISO 14001","OEKO-TEX"],color:"bg-green-600"},{id:"guangzhou",name:c("factory_guangzhou"),description:c("factory_guangzhou_desc"),coordinates:{x:70,y:50},workers:"600+",certifications:["ISO 9001","SA8000"],color:"bg-purple-600"},{id:"vietnam",name:c("factory_vietnam"),description:c("factory_vietnam_desc"),coordinates:{x:65,y:70},workers:"400+",certifications:["WRAP","BSCI"],color:"bg-red-600"}];return p.jsx("section",{id:"factories",className:"py-20 bg-gray-50",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:c("factory_title")}),p.jsx("p",{className:"text-xl text-gray-600 mb-6",children:c("factory_subtitle")}),p.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:c("factory_intro")})]}),p.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-12",children:p.jsx("div",{className:"relative",children:p.jsxs("svg",{viewBox:"0 0 400 300",className:"w-full h-96 bg-blue-50 rounded-lg",style:{maxHeight:"400px"},children:[p.jsx("path",{d:"M80 40 L180 35 L190 50 L185 80 L170 90 L150 95 L120 90 L100 85 L85 70 L80 40 Z",fill:"#e5e7eb",stroke:"#9ca3af",strokeWidth:"1"}),p.jsx("path",{d:"M140 120 L150 115 L155 140 L150 180 L145 185 L140 180 L135 150 L140 120 Z",fill:"#e5e7eb",stroke:"#9ca3af",strokeWidth:"1"}),s.map(r=>p.jsxs("g",{children:[p.jsx("circle",{cx:r.coordinates.x*2,cy:r.coordinates.y*2,r:"8",className:`${r.color.replace("bg-","fill-")} cursor-pointer hover:opacity-80 transition-opacity`,stroke:"white",strokeWidth:"2"}),p.jsx("text",{x:r.coordinates.x*2,y:r.coordinates.y*2-15,textAnchor:"middle",className:"text-xs font-semibold fill-gray-700",children:r.name.split(",")[0]})]},r.id)),p.jsxs("g",{transform:"translate(20, 20)",children:[p.jsx("rect",{x:"0",y:"0",width:"120",height:"80",fill:"white",stroke:"#e5e7eb",rx:"5"}),p.jsx("text",{x:"10",y:"15",className:"text-xs font-semibold fill-gray-700",children:"Factory Locations"}),p.jsx("circle",{cx:"15",cy:"30",r:"4",className:"fill-blue-600"}),p.jsx("text",{x:"25",y:"35",className:"text-xs fill-gray-600",children:"Technology Hub"}),p.jsx("circle",{cx:"15",cy:"45",r:"4",className:"fill-green-600"}),p.jsx("text",{x:"25",y:"50",className:"text-xs fill-gray-600",children:"Textile Manufacturing"}),p.jsx("circle",{cx:"15",cy:"60",r:"4",className:"fill-purple-600"}),p.jsx("text",{x:"25",y:"65",className:"text-xs fill-gray-600",children:"Design Center"})]})]})})}),p.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:s.map(r=>p.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[p.jsxs("div",{className:"flex items-center mb-4",children:[p.jsx("div",{className:`p-3 rounded-full ${r.color} text-white mr-4`,children:p.jsx(Jh,{className:"h-6 w-6"})}),p.jsx("div",{children:p.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r.name})})]}),p.jsx("p",{className:"text-gray-600 mb-4 text-sm leading-relaxed",children:r.description}),p.jsxs("div",{className:"space-y-3",children:[p.jsxs("div",{className:"flex items-center text-sm",children:[p.jsx($h,{className:"h-4 w-4 text-gray-500 mr-2"}),p.jsxs("span",{className:"text-gray-700",children:[r.workers," Workers"]})]}),p.jsxs("div",{className:"flex items-start text-sm",children:[p.jsx(Qh,{className:"h-4 w-4 text-gray-500 mr-2 mt-0.5"}),p.jsxs("div",{children:[p.jsx("span",{className:"text-gray-700 block",children:"Certifications:"}),p.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:r.certifications.map((u,o)=>p.jsx("span",{className:"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs",children:u},o))})]})]}),p.jsxs("div",{className:"flex items-center text-sm",children:[p.jsx(Xr,{className:"h-4 w-4 text-green-500 mr-2"}),p.jsx("span",{className:"text-green-600 font-medium",children:"Fully Compliant"})]})]})]},r.id))}),p.jsxs("div",{className:"mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center",children:[p.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Our Commitment to Ethical Manufacturing"}),p.jsx("p",{className:"text-lg mb-6 max-w-4xl mx-auto",children:"Every facility in our global network operates under the strictest ethical standards, ensuring worker dignity, fair compensation, and safe working conditions."}),p.jsxs("div",{className:"grid md:grid-cols-3 gap-6 text-center",children:[p.jsxs("div",{children:[p.jsx("div",{className:"text-3xl font-bold mb-2",children:"3,050+"}),p.jsx("div",{className:"text-blue-100",children:"Total Workers Protected"})]}),p.jsxs("div",{children:[p.jsx("div",{className:"text-3xl font-bold mb-2",children:"100%"}),p.jsx("div",{className:"text-blue-100",children:"Compliance Rate"})]}),p.jsxs("div",{children:[p.jsx("div",{className:"text-3xl font-bold mb-2",children:"0"}),p.jsx("div",{className:"text-blue-100",children:"Forced Labor Incidents"})]})]})]}),p.jsx("div",{className:"mt-12 text-center",children:p.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto",children:[p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:c("whitepaper_title")}),p.jsx("p",{className:"text-gray-600 mb-6",children:c("whitepaper_description")}),p.jsxs("a",{href:"/sparkle_industry_whitepaper.html",target:"_blank",className:"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[c("whitepaper_download"),p.jsx("svg",{className:"ml-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:p.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})]})]})})]})})},Jy=()=>{const{t:c}=Bt(),s=[{id:"amazon",name:"Amazon",logo:"🛒",description:c("partner_amazon_desc"),region:"Global",category:"E-commerce",stats:{years:"5+",products:"10,000+",rating:"4.8/5"},color:"bg-orange-500",bgColor:"bg-orange-50"},{id:"shopee",name:"Shopee",logo:"🛍️",description:c("partner_shopee_desc"),region:"Southeast Asia",category:"E-commerce",stats:{years:"4+",products:"8,000+",rating:"4.9/5"},color:"bg-red-500",bgColor:"bg-red-50"},{id:"tiktok",name:"TikTok Shop",logo:"🎵",description:c("partner_tiktok_desc"),region:"Global",category:"Social Commerce",stats:{years:"2+",products:"5,000+",rating:"4.7/5"},color:"bg-black",bgColor:"bg-gray-50"},{id:"temu",name:"Temu",logo:"🏪",description:c("partner_temu_desc"),region:"Global",category:"Marketplace",stats:{years:"2+",products:"6,000+",rating:"4.6/5"},color:"bg-blue-600",bgColor:"bg-blue-50"},{id:"alibaba",name:"Alibaba",logo:"🌐",description:c("partner_alibaba_desc"),region:"Global B2B",category:"B2B Platform",stats:{years:"8+",products:"15,000+",rating:"4.8/5"},color:"bg-orange-600",bgColor:"bg-orange-50"},{id:"lazada",name:"Lazada",logo:"🛒",description:c("partner_lazada_desc"),region:"Southeast Asia",category:"E-commerce",stats:{years:"3+",products:"4,000+",rating:"4.5/5"},color:"bg-purple-600",bgColor:"bg-purple-50"}],r=[{icon:Hy,number:"50,000+",label:c("partners_products_sold"),color:"text-blue-600"},{icon:$h,number:"2M+",label:c("partners_customers_served"),color:"text-green-600"},{icon:Zh,number:"50+",label:c("partners_countries_reached"),color:"text-purple-600"},{icon:Gy,number:"98%",label:c("partners_satisfaction_rate"),color:"text-orange-600"}];return p.jsx("section",{id:"partners",className:"py-20 bg-white",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:c("partners_title")}),p.jsx("p",{className:"text-xl text-gray-600 mb-6",children:c("partners_subtitle")}),p.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:c("partners_intro")})]}),p.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:s.map(u=>p.jsxs("div",{className:`${u.bgColor} rounded-lg p-6 hover:shadow-lg transition-all duration-300 border border-gray-200`,children:[p.jsxs("div",{className:"flex items-center mb-4",children:[p.jsx("div",{className:"text-4xl mr-4",children:u.logo}),p.jsxs("div",{children:[p.jsx("h3",{className:"text-xl font-bold text-gray-900",children:u.name}),p.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[p.jsx("span",{className:`px-2 py-1 rounded-full text-white text-xs ${u.color}`,children:u.category}),p.jsx("span",{children:"•"}),p.jsx("span",{children:u.region})]})]})]}),p.jsx("p",{className:"text-gray-700 mb-4 text-sm leading-relaxed",children:u.description}),p.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[p.jsxs("div",{children:[p.jsx("div",{className:"font-bold text-lg text-gray-900",children:u.stats.years}),p.jsx("div",{className:"text-xs text-gray-600",children:c("partners_years_cooperation")})]}),p.jsxs("div",{children:[p.jsx("div",{className:"font-bold text-lg text-gray-900",children:u.stats.products}),p.jsx("div",{className:"text-xs text-gray-600",children:c("partners_products")})]}),p.jsxs("div",{children:[p.jsxs("div",{className:"font-bold text-lg text-gray-900 flex items-center justify-center",children:[p.jsx(By,{className:"h-4 w-4 text-yellow-500 mr-1"}),u.stats.rating]}),p.jsx("div",{className:"text-xs text-gray-600",children:c("partners_rating")})]})]}),p.jsx("div",{className:"mt-4 text-center",children:p.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["✓ ",c("partners_compliant")]})})]},u.id))}),p.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white mb-12",children:[p.jsxs("div",{className:"text-center mb-8",children:[p.jsx("h3",{className:"text-2xl font-bold mb-2",children:c("partners_achievements_title")}),p.jsx("p",{className:"text-blue-100",children:c("partners_achievements_subtitle")})]}),p.jsx("div",{className:"grid md:grid-cols-4 gap-8 text-center",children:r.map((u,o)=>{const d=u.icon;return p.jsxs("div",{className:"flex flex-col items-center",children:[p.jsx("div",{className:"bg-white bg-opacity-20 rounded-full p-4 mb-4",children:p.jsx(d,{className:"h-8 w-8 text-white"})}),p.jsx("div",{className:"text-3xl font-bold mb-2",children:u.number}),p.jsx("div",{className:"text-blue-100 text-sm",children:u.label})]},o)})})]}),p.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[p.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:c("partners_ethics_title")}),p.jsx("p",{className:"text-lg text-gray-700 mb-6 max-w-4xl mx-auto",children:c("partners_ethics_content")}),p.jsxs("div",{className:"grid md:grid-cols-3 gap-6 mt-8",children:[p.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[p.jsx("div",{className:"text-green-600 text-2xl mb-3",children:"🛡️"}),p.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:c("partners_commitment_quality")}),p.jsx("p",{className:"text-sm text-gray-600",children:c("partners_commitment_quality_desc")})]}),p.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[p.jsx("div",{className:"text-blue-600 text-2xl mb-3",children:"🤝"}),p.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:c("partners_commitment_ethics")}),p.jsx("p",{className:"text-sm text-gray-600",children:c("partners_commitment_ethics_desc")})]}),p.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[p.jsx("div",{className:"text-purple-600 text-2xl mb-3",children:"🌍"}),p.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:c("partners_commitment_global")}),p.jsx("p",{className:"text-sm text-gray-600",children:c("partners_commitment_global_desc")})]})]})]}),p.jsxs("div",{className:"text-center mt-12",children:[p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:c("partners_cta_title")}),p.jsx("p",{className:"text-gray-600 mb-6",children:c("partners_cta_content")}),p.jsx("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors",children:c("partners_cta_button")})]})]})})},$y=()=>{const{t:c}=Bt(),s=[{icon:Ty,title:c("charity_education_title"),content:c("charity_education_content"),color:"text-blue-600 bg-blue-100"},{icon:Kh,title:c("charity_health_title"),content:c("charity_health_content"),color:"text-red-600 bg-red-100"},{icon:Xr,title:c("charity_protection_title"),content:c("charity_protection_content"),color:"text-green-600 bg-green-100"}],r=[{number:"2,500+",label:c("charity_impact_children")},{number:"50+",label:c("charity_impact_schools")},{number:"15",label:c("charity_impact_programs")}];return p.jsx("section",{id:"charity",className:"py-20 bg-white",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsxs("div",{className:"text-center mb-16",children:[p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:c("charity_title")}),p.jsx("p",{className:"text-xl text-gray-600 mb-6",children:c("charity_subtitle")}),p.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:c("charity_intro")})]}),p.jsx("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:s.map((u,o)=>{const d=u.icon;return p.jsxs("div",{className:"text-center",children:[p.jsx("div",{className:`inline-flex p-4 rounded-full ${u.color} mb-6`,children:p.jsx(d,{className:"h-8 w-8"})}),p.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:u.title}),p.jsx("p",{className:"text-gray-600 leading-relaxed",children:u.content})]},o)})}),p.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white",children:[p.jsx("div",{className:"text-center mb-8",children:p.jsx("h3",{className:"text-2xl font-bold mb-2",children:c("charity_impact_title")})}),p.jsx("div",{className:"grid md:grid-cols-3 gap-8 text-center",children:r.map((u,o)=>p.jsxs("div",{children:[p.jsx("div",{className:"text-3xl md:text-4xl font-bold mb-2",children:u.number}),p.jsx("div",{className:"text-blue-100",children:u.label})]},o))})]})]})})},Wy=()=>{const{t:c}=Bt();return p.jsx("section",{id:"contact",className:"py-20 bg-gray-50",children:p.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[p.jsx("div",{className:"text-center mb-16",children:p.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:c("contact_title")})}),p.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[p.jsxs("div",{className:"text-center",children:[p.jsx("div",{className:"inline-flex p-4 rounded-full bg-blue-100 text-blue-600 mb-4",children:p.jsx(Jh,{className:"h-6 w-6"})}),p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:c("contact_address")}),p.jsxs("p",{className:"text-gray-600",children:["123 Business District",p.jsx("br",{}),"Shanghai, China 200000"]})]}),p.jsxs("div",{className:"text-center",children:[p.jsx("div",{className:"inline-flex p-4 rounded-full bg-green-100 text-green-600 mb-4",children:p.jsx(My,{className:"h-6 w-6"})}),p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:c("contact_phone")}),p.jsx("p",{className:"text-gray-600",children:"+86 21 1234 5678"})]}),p.jsxs("div",{className:"text-center",children:[p.jsx("div",{className:"inline-flex p-4 rounded-full bg-purple-100 text-purple-600 mb-4",children:p.jsx(wy,{className:"h-6 w-6"})}),p.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:c("contact_email")}),p.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]})]})})},Fy=()=>{const{t:c}=Bt();return p.jsx("footer",{className:"bg-gray-900 text-white py-12",children:p.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:p.jsxs("div",{className:"text-center",children:[p.jsx("h3",{className:"text-2xl font-bold mb-4",children:c("company_name")}),p.jsx("p",{className:"text-gray-400 mb-6 max-w-2xl mx-auto",children:c("footer_values")}),p.jsx("div",{className:"border-t border-gray-800 pt-6",children:p.jsx("p",{className:"text-gray-400",children:c("footer_copyright")})})]})})})};function Py(){const{i18n:c}=Bt();return p.jsxs("div",{className:"min-h-screen",children:[p.jsx(Vy,{}),p.jsx(Xy,{}),p.jsx(Qy,{}),p.jsx(Zy,{}),p.jsx(Ky,{}),p.jsx(Jy,{}),p.jsx($y,{}),p.jsx(Wy,{}),p.jsx(Fy,{})]})}Ap.createRoot(document.getElementById("root")).render(p.jsx(Ne.StrictMode,{children:p.jsx(Py,{})}));
