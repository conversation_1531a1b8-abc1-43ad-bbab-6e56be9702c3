import { useTranslation } from 'react-i18next';
import { MapPin, Users, Award, Shield } from 'lucide-react';

const FactoryMap = () => {
  const { t } = useTranslation();

  const factories = [
    {
      id: 'shenzhen',
      name: t('factory_shenzhen'),
      description: t('factory_shenzhen_desc'),
      coordinates: { x: 75, y: 45 }, // Approximate position on China map
      workers: '850+',
      certifications: ['ISO 14001', 'SA8000', 'WRAP'],
      color: 'bg-blue-600'
    },
    {
      id: 'dongguan',
      name: t('factory_dongguan'),
      description: t('factory_dongguan_desc'),
      coordinates: { x: 73, y: 48 },
      workers: '1,200+',
      certifications: ['ISO 14001', 'OEKO-TEX'],
      color: 'bg-green-600'
    },
    {
      id: 'guangzhou',
      name: t('factory_guangzhou'),
      description: t('factory_guangzhou_desc'),
      coordinates: { x: 70, y: 50 },
      workers: '600+',
      certifications: ['ISO 9001', 'SA8000'],
      color: 'bg-purple-600'
    },
    {
      id: 'vietnam',
      name: t('factory_vietnam'),
      description: t('factory_vietnam_desc'),
      coordinates: { x: 65, y: 70 },
      workers: '400+',
      certifications: ['WRAP', 'BSCI'],
      color: 'bg-red-600'
    }
  ];

  return (
    <section id="factories" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('factory_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-6">
            {t('factory_subtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('factory_intro')}
          </p>
        </div>

        {/* Interactive Map */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-12">
          <div className="relative">
            {/* Simplified Asia Map SVG */}
            <svg 
              viewBox="0 0 400 300" 
              className="w-full h-96 bg-blue-50 rounded-lg"
              style={{ maxHeight: '400px' }}
            >
              {/* China outline (simplified) */}
              <path
                d="M80 40 L180 35 L190 50 L185 80 L170 90 L150 95 L120 90 L100 85 L85 70 L80 40 Z"
                fill="#e5e7eb"
                stroke="#9ca3af"
                strokeWidth="1"
              />
              
              {/* Vietnam outline (simplified) */}
              <path
                d="M140 120 L150 115 L155 140 L150 180 L145 185 L140 180 L135 150 L140 120 Z"
                fill="#e5e7eb"
                stroke="#9ca3af"
                strokeWidth="1"
              />

              {/* Factory markers */}
              {factories.map((factory) => (
                <g key={factory.id}>
                  {/* Factory marker */}
                  <circle
                    cx={factory.coordinates.x * 2}
                    cy={factory.coordinates.y * 2}
                    r="8"
                    className={`${factory.color.replace('bg-', 'fill-')} cursor-pointer hover:opacity-80 transition-opacity`}
                    stroke="white"
                    strokeWidth="2"
                  />
                  {/* Factory label */}
                  <text
                    x={factory.coordinates.x * 2}
                    y={factory.coordinates.y * 2 - 15}
                    textAnchor="middle"
                    className="text-xs font-semibold fill-gray-700"
                  >
                    {factory.name.split(',')[0]}
                  </text>
                </g>
              ))}

              {/* Legend */}
              <g transform="translate(20, 20)">
                <rect x="0" y="0" width="120" height="80" fill="white" stroke="#e5e7eb" rx="5"/>
                <text x="10" y="15" className="text-xs font-semibold fill-gray-700">Factory Locations</text>
                <circle cx="15" cy="30" r="4" className="fill-blue-600"/>
                <text x="25" y="35" className="text-xs fill-gray-600">Technology Hub</text>
                <circle cx="15" cy="45" r="4" className="fill-green-600"/>
                <text x="25" y="50" className="text-xs fill-gray-600">Textile Manufacturing</text>
                <circle cx="15" cy="60" r="4" className="fill-purple-600"/>
                <text x="25" y="65" className="text-xs fill-gray-600">Design Center</text>
              </g>
            </svg>
          </div>
        </div>

        {/* Factory Details Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {factories.map((factory) => (
            <div key={factory.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-full ${factory.color} text-white mr-4`}>
                  <MapPin className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{factory.name}</h3>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                {factory.description}
              </p>
              
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Users className="h-4 w-4 text-gray-500 mr-2" />
                  <span className="text-gray-700">{factory.workers} Workers</span>
                </div>
                
                <div className="flex items-start text-sm">
                  <Award className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <span className="text-gray-700 block">Certifications:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {factory.certifications.map((cert, index) => (
                        <span 
                          key={index}
                          className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                        >
                          {cert}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center text-sm">
                  <Shield className="h-4 w-4 text-green-500 mr-2" />
                  <span className="text-green-600 font-medium">Fully Compliant</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Compliance Statement */}
        <div className="mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">Our Commitment to Ethical Manufacturing</h3>
          <p className="text-lg mb-6 max-w-4xl mx-auto">
            Every facility in our global network operates under the strictest ethical standards, 
            ensuring worker dignity, fair compensation, and safe working conditions.
          </p>
          <div className="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">3,050+</div>
              <div className="text-blue-100">Total Workers Protected</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">100%</div>
              <div className="text-blue-100">Compliance Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">0</div>
              <div className="text-blue-100">Forced Labor Incidents</div>
            </div>
          </div>
        </div>

        {/* Whitepaper Download */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('whitepaper_title')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('whitepaper_description')}
            </p>
            <a 
              href="/sparkle_industry_whitepaper.html" 
              target="_blank"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              {t('whitepaper_download')}
              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FactoryMap;
