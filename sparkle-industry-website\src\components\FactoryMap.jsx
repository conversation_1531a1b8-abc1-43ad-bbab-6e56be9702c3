import { useTranslation } from 'react-i18next';
import { MapPin, Users, Award, Shield } from 'lucide-react';

const FactoryMap = () => {
  const { t } = useTranslation();

  const factories = [
    {
      id: 'shenzhen',
      name: t('factory_shenzhen'),
      description: t('factory_shenzhen_desc'),
      coordinates: { x: 75, y: 45 }, // Approximate position on China map
      workers: '850+',
      certifications: ['ISO 14001', 'SA8000', 'WRAP'],
      color: 'bg-blue-600'
    },
    {
      id: 'dongguan',
      name: t('factory_dongguan'),
      description: t('factory_dongguan_desc'),
      coordinates: { x: 73, y: 48 },
      workers: '1,200+',
      certifications: ['ISO 14001', 'OEKO-TEX'],
      color: 'bg-green-600'
    },
    {
      id: 'guangzhou',
      name: t('factory_guangzhou'),
      description: t('factory_guangzhou_desc'),
      coordinates: { x: 70, y: 50 },
      workers: '600+',
      certifications: ['ISO 9001', 'SA8000'],
      color: 'bg-purple-600'
    },
    {
      id: 'vietnam',
      name: t('factory_vietnam'),
      description: t('factory_vietnam_desc'),
      coordinates: { x: 65, y: 70 },
      workers: '400+',
      certifications: ['WRAP', 'BSCI'],
      color: 'bg-red-600'
    }
  ];

  return (
    <section id="factories" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('factory_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-6">
            {t('factory_subtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('factory_intro')}
          </p>
        </div>

        {/* Interactive World Map with Factory Locations */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg p-8 mb-12">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Global Manufacturing Network
          </h3>

          {/* World Map Container */}
          <div className="relative bg-white rounded-lg p-6 shadow-inner">
            {/* Background World Map */}
            <div className="relative w-full h-96 bg-gradient-to-b from-blue-100 to-blue-200 rounded-lg overflow-hidden">
              {/* Continents Background */}
              <div className="absolute inset-0">
                {/* Asia Continent */}
                <div className="absolute top-16 right-20 w-64 h-48 bg-green-200 rounded-3xl transform rotate-12 opacity-60"></div>
                <div className="absolute top-20 right-24 w-48 h-32 bg-green-300 rounded-2xl transform -rotate-6 opacity-70"></div>

                {/* Europe */}
                <div className="absolute top-12 right-80 w-32 h-24 bg-yellow-200 rounded-2xl opacity-60"></div>

                {/* Americas */}
                <div className="absolute top-20 left-16 w-24 h-56 bg-orange-200 rounded-3xl transform -rotate-12 opacity-60"></div>
                <div className="absolute top-32 left-32 w-20 h-40 bg-orange-300 rounded-2xl transform rotate-6 opacity-70"></div>
              </div>

              {/* Factory Markers with Animation */}
              {factories.map((factory, index) => {
                const positions = {
                  'shenzhen': { top: '35%', left: '75%' },
                  'dongguan': { top: '40%', left: '73%' },
                  'guangzhou': { top: '42%', left: '71%' },
                  'vietnam': { top: '55%', left: '68%' }
                };

                const position = positions[factory.id] || { top: '50%', left: '50%' };

                return (
                  <div
                    key={factory.id}
                    className="absolute transform -translate-x-1/2 -translate-y-1/2 group cursor-pointer"
                    style={{ top: position.top, left: position.left }}
                  >
                    {/* Pulsing Ring Animation */}
                    <div className={`absolute inset-0 ${factory.color} rounded-full animate-ping opacity-30`}></div>
                    <div className={`absolute inset-0 ${factory.color} rounded-full animate-pulse opacity-50`}></div>

                    {/* Factory Marker */}
                    <div className={`relative w-6 h-6 ${factory.color} rounded-full border-2 border-white shadow-lg group-hover:scale-125 transition-transform duration-300`}>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    </div>

                    {/* Factory Label */}
                    <div className="absolute top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-gray-900 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap shadow-lg">
                        {factory.name}
                        <div className="text-xs text-gray-300">{factory.workers} Workers</div>
                      </div>
                      {/* Arrow */}
                      <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 rotate-45"></div>
                    </div>
                  </div>
                );
              })}

              {/* Connection Lines */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                <defs>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3"/>
                    <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.5"/>
                    <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.3"/>
                  </linearGradient>
                </defs>

                {/* Animated connection lines between factories */}
                <path
                  d="M 75% 35% Q 70% 30% 73% 40%"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  className="animate-pulse"
                />
                <path
                  d="M 73% 40% Q 68% 45% 71% 42%"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  className="animate-pulse"
                />
                <path
                  d="M 71% 42% Q 65% 50% 68% 55%"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="5,5"
                  className="animate-pulse"
                />
              </svg>

              {/* Legend */}
              <div className="absolute bottom-4 left-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Factory Types</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                    <span className="text-xs text-gray-700">Technology Hub</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                    <span className="text-xs text-gray-700">Textile Manufacturing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-600 rounded-full"></div>
                    <span className="text-xs text-gray-700">Design Center</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-600 rounded-full"></div>
                    <span className="text-xs text-gray-700">Assembly Hub</span>
                  </div>
                </div>
              </div>

              {/* Network Stats */}
              <div className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-lg p-4 shadow-lg">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Network Overview</h4>
                <div className="space-y-1 text-xs text-gray-700">
                  <div>🏭 4 Manufacturing Sites</div>
                  <div>👥 3,050+ Total Workers</div>
                  <div>🌍 2 Countries</div>
                  <div>✅ 100% Compliant</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Factory Details Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {factories.map((factory, index) => (
            <div
              key={factory.id}
              className="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-gray-200"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Factory Header with Gradient */}
              <div className={`relative h-24 ${factory.color} bg-gradient-to-br from-current to-opacity-80`}>
                <div className="absolute inset-0 bg-black bg-opacity-10"></div>
                <div className="relative p-4 flex items-center justify-between text-white">
                  <div>
                    <h3 className="text-lg font-bold">{factory.name.split(',')[0]}</h3>
                    <p className="text-sm opacity-90">{factory.name.split(',')[1]}</p>
                  </div>
                  <div className="bg-white bg-opacity-20 rounded-full p-2">
                    <MapPin className="h-6 w-6" />
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-10 rounded-full"></div>
                <div className="absolute bottom-2 left-2 w-4 h-4 bg-white bg-opacity-10 rounded-full"></div>
              </div>

              {/* Factory Content */}
              <div className="p-6">
                <p className="text-gray-600 mb-6 text-sm leading-relaxed line-clamp-3">
                  {factory.description}
                </p>

                {/* Key Metrics */}
                <div className="space-y-4 mb-6">
                  {/* Workers Count */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2 rounded-lg mr-3">
                        <Users className="h-4 w-4 text-blue-600" />
                      </div>
                      <span className="text-sm text-gray-700">Workforce</span>
                    </div>
                    <span className="font-bold text-gray-900">{factory.workers}</span>
                  </div>

                  {/* Compliance Status */}
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="bg-green-100 p-2 rounded-lg mr-3">
                        <Shield className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-sm text-gray-700">Compliance</span>
                    </div>
                    <span className="font-bold text-green-600">100%</span>
                  </div>
                </div>

                {/* Certifications */}
                <div className="mb-4">
                  <div className="flex items-center mb-3">
                    <Award className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Certifications</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {factory.certifications.map((cert, certIndex) => (
                      <span
                        key={certIndex}
                        className="bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium border border-blue-200"
                      >
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Status Badge */}
                <div className="flex justify-center">
                  <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full text-sm font-medium shadow-lg">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    Fully Operational
                  </div>
                </div>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        {/* Enhanced Compliance & Statistics Section */}
        <div className="mt-12 space-y-8">
          {/* Main Statistics Dashboard */}
          <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-2xl p-8 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400 to-purple-600"></div>
              <div className="absolute top-4 left-4 w-32 h-32 bg-white rounded-full opacity-5"></div>
              <div className="absolute bottom-4 right-4 w-24 h-24 bg-white rounded-full opacity-5"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-white rounded-full opacity-3"></div>
            </div>

            <div className="relative z-10">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold mb-4">Our Commitment to Ethical Manufacturing</h3>
                <p className="text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
                  Every facility in our global network operates under the strictest ethical standards,
                  ensuring worker dignity, fair compensation, and safe working conditions.
                </p>
              </div>

              {/* Statistics Grid */}
              <div className="grid md:grid-cols-4 gap-8">
                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                      3,050+
                    </div>
                    <div className="text-blue-100 font-medium">Total Workers Protected</div>
                    <div className="mt-2 text-xs text-blue-200">Across all facilities</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                      100%
                    </div>
                    <div className="text-blue-100 font-medium">Compliance Rate</div>
                    <div className="mt-2 text-xs text-blue-200">Zero violations</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">
                      0
                    </div>
                    <div className="text-blue-100 font-medium">Forced Labor Incidents</div>
                    <div className="mt-2 text-xs text-blue-200">Lifetime record</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                      24/7
                    </div>
                    <div className="text-blue-100 font-medium">Monitoring System</div>
                    <div className="mt-2 text-xs text-blue-200">Continuous oversight</div>
                  </div>
                </div>
              </div>

              {/* Commitment Badges */}
              <div className="mt-8 flex flex-wrap justify-center gap-4">
                <div className="bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-6 py-2 text-green-100 text-sm font-medium">
                  ✓ ISO 14001 Certified
                </div>
                <div className="bg-blue-500 bg-opacity-20 border border-blue-400 rounded-full px-6 py-2 text-blue-100 text-sm font-medium">
                  ✓ SA8000 Compliant
                </div>
                <div className="bg-purple-500 bg-opacity-20 border border-purple-400 rounded-full px-6 py-2 text-purple-100 text-sm font-medium">
                  ✓ WRAP Certified
                </div>
                <div className="bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-full px-6 py-2 text-yellow-100 text-sm font-medium">
                  ✓ Zero Xinjiang Cotton
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Whitepaper Download */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('whitepaper_title')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('whitepaper_description')}
            </p>
            <a 
              href="/sparkle_industry_whitepaper.html" 
              target="_blank"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              {t('whitepaper_download')}
              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FactoryMap;
