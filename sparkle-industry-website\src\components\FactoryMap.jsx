import { useTranslation } from 'react-i18next';
import { MapPin, Users, Award, Shield } from 'lucide-react';

const FactoryMap = () => {
  const { t } = useTranslation();

  const factories = [
    {
      id: 'shenzhen',
      name: t('factory_shenzhen'),
      description: t('factory_shenzhen_desc'),
      coordinates: { x: 75, y: 45 }, // Approximate position on China map
      workers: '850+',
      certifications: ['ISO 14001', 'SA8000', 'WRAP'],
      color: 'bg-blue-600'
    },
    {
      id: 'dongguan',
      name: t('factory_dongguan'),
      description: t('factory_dongguan_desc'),
      coordinates: { x: 73, y: 48 },
      workers: '1,200+',
      certifications: ['ISO 14001', 'OEKO-TEX'],
      color: 'bg-green-600'
    },
    {
      id: 'guangzhou',
      name: t('factory_guangzhou'),
      description: t('factory_guangzhou_desc'),
      coordinates: { x: 70, y: 50 },
      workers: '600+',
      certifications: ['ISO 9001', 'SA8000'],
      color: 'bg-purple-600'
    },
    {
      id: 'vietnam',
      name: t('factory_vietnam'),
      description: t('factory_vietnam_desc'),
      coordinates: { x: 65, y: 70 },
      workers: '400+',
      certifications: ['WRAP', 'BSCI'],
      color: 'bg-red-600'
    }
  ];

  return (
    <section id="factories" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {t('factory_title')}
          </h2>
          <p className="text-xl text-gray-600 mb-6">
            {t('factory_subtitle')}
          </p>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('factory_intro')}
          </p>
        </div>

        {/* Real World Map with Factory Locations */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg p-8 mb-12">
          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">
            Global Manufacturing Network
          </h3>

          {/* World Map Container */}
          <div className="relative bg-white rounded-lg p-6 shadow-inner">
            <div className="relative w-full h-96 bg-gradient-to-b from-blue-100 to-blue-200 rounded-lg overflow-hidden">

              {/* Real World Map SVG */}
              <svg
                viewBox="0 0 1000 500"
                className="w-full h-full"
                style={{ background: 'linear-gradient(to bottom, #dbeafe, #bfdbfe)' }}
              >
                {/* Ocean background with wave pattern */}
                <defs>
                  <pattern id="waves" x="0" y="0" width="40" height="20" patternUnits="userSpaceOnUse">
                    <path d="M0 10 Q10 5 20 10 T40 10" stroke="#93c5fd" strokeWidth="0.5" fill="none" opacity="0.3"/>
                  </pattern>

                  {/* Gradient definitions for continents */}
                  <linearGradient id="asiaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#10b981" stopOpacity="0.8"/>
                    <stop offset="100%" stopColor="#059669" stopOpacity="0.9"/>
                  </linearGradient>

                  <linearGradient id="europeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#fbbf24" stopOpacity="0.7"/>
                    <stop offset="100%" stopColor="#f59e0b" stopOpacity="0.8"/>
                  </linearGradient>

                  <linearGradient id="americaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#f97316" stopOpacity="0.7"/>
                    <stop offset="100%" stopColor="#ea580c" stopOpacity="0.8"/>
                  </linearGradient>
                </defs>

                {/* Ocean background */}
                <rect x="0" y="0" width="1000" height="500" fill="url(#waves)"/>

                {/* World Map Paths - More detailed and accurate */}

                {/* Asia Continent */}
                <path
                  d="M 550 120 L 850 110 L 880 140 L 890 180 L 885 220 L 870 260 L 840 300 L 800 330 L 750 350 L 700 360 L 650 355 L 600 340 L 560 320 L 530 290 L 520 250 L 525 210 L 540 170 L 550 140 Z"
                  fill="url(#asiaGradient)"
                  stroke="#059669"
                  strokeWidth="1.5"
                />

                {/* China (highlighted) */}
                <path
                  d="M 650 160 L 750 155 L 780 170 L 790 190 L 795 210 L 790 230 L 775 250 L 750 260 L 720 265 L 690 260 L 665 250 L 650 235 L 645 215 L 648 195 L 650 175 Z"
                  fill="#22c55e"
                  stroke="#16a34a"
                  strokeWidth="2"
                  opacity="0.9"
                />

                {/* Vietnam (highlighted) */}
                <path
                  d="M 680 260 L 695 255 L 700 270 L 698 290 L 695 310 L 690 330 L 685 340 L 680 345 L 675 340 L 678 320 L 680 300 L 682 280 Z"
                  fill="#22c55e"
                  stroke="#16a34a"
                  strokeWidth="2"
                  opacity="0.9"
                />

                {/* Europe */}
                <path
                  d="M 450 120 L 550 115 L 570 130 L 575 150 L 570 170 L 555 185 L 530 195 L 500 200 L 470 195 L 445 180 L 435 160 L 440 140 Z"
                  fill="url(#europeGradient)"
                  stroke="#f59e0b"
                  strokeWidth="1.5"
                />

                {/* North America */}
                <path
                  d="M 100 100 L 350 90 L 380 110 L 390 140 L 385 180 L 370 220 L 340 250 L 300 270 L 250 280 L 200 275 L 150 260 L 110 240 L 90 210 L 95 170 L 105 130 Z"
                  fill="url(#americaGradient)"
                  stroke="#ea580c"
                  strokeWidth="1.5"
                />

                {/* South America */}
                <path
                  d="M 250 280 L 320 275 L 340 300 L 350 340 L 345 380 L 335 420 L 320 450 L 300 470 L 275 475 L 250 470 L 225 450 L 210 420 L 205 380 L 210 340 L 220 310 Z"
                  fill="url(#americaGradient)"
                  stroke="#ea580c"
                  strokeWidth="1.5"
                />

                {/* Africa */}
                <path
                  d="M 450 200 L 580 195 L 600 220 L 605 260 L 600 300 L 590 340 L 575 370 L 550 390 L 520 400 L 490 395 L 465 380 L 450 360 L 445 330 L 450 300 L 455 270 L 460 240 Z"
                  fill="#8b5cf6"
                  stroke="#7c3aed"
                  strokeWidth="1.5"
                  opacity="0.7"
                />

                {/* Australia */}
                <path
                  d="M 750 370 L 850 365 L 870 380 L 875 400 L 865 420 L 845 435 L 815 440 L 780 435 L 755 420 L 745 400 L 748 385 Z"
                  fill="#06b6d4"
                  stroke="#0891b2"
                  strokeWidth="1.5"
                  opacity="0.7"
                />

                {/* Grid lines for reference */}
                <defs>
                  <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                    <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e5e7eb" strokeWidth="0.5" opacity="0.3"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />

                {/* Factory Markers with precise geographical coordinates */}
                {factories.map((factory, index) => {
                  // Real geographical coordinates converted to SVG coordinates
                  // Based on actual latitude/longitude positions
                  const coordinates = {
                    'shenzhen': { x: 720, y: 200 },    // Shenzhen: 22.3193°N, 114.1694°E
                    'dongguan': { x: 715, y: 205 },    // Dongguan: 23.0489°N, 113.7447°E
                    'guangzhou': { x: 710, y: 198 },   // Guangzhou: 23.1291°N, 113.2644°E
                    'vietnam': { x: 690, y: 290 }      // Ho Chi Minh City: 10.8231°N, 106.6297°E
                  };

                  const coord = coordinates[factory.id] || { x: 500, y: 250 };

                  return (
                    <g key={factory.id} className="group cursor-pointer">
                      {/* Pulsing rings with factory color */}
                      <circle
                        cx={coord.x}
                        cy={coord.y}
                        r="20"
                        fill={factory.color.replace('bg-', '#').replace('blue-600', '3b82f6').replace('green-600', '16a34a').replace('purple-600', '9333ea').replace('red-600', 'dc2626')}
                        opacity="0.15"
                        className="animate-ping"
                      />
                      <circle
                        cx={coord.x}
                        cy={coord.y}
                        r="12"
                        fill={factory.color.replace('bg-', '#').replace('blue-600', '3b82f6').replace('green-600', '16a34a').replace('purple-600', '9333ea').replace('red-600', 'dc2626')}
                        opacity="0.3"
                        className="animate-pulse"
                      />

                      {/* Main marker with shadow */}
                      <circle
                        cx={coord.x + 1}
                        cy={coord.y + 1}
                        r="10"
                        fill="rgba(0,0,0,0.2)"
                      />
                      <circle
                        cx={coord.x}
                        cy={coord.y}
                        r="10"
                        fill={factory.color.replace('bg-', '#').replace('blue-600', '3b82f6').replace('green-600', '16a34a').replace('purple-600', '9333ea').replace('red-600', 'dc2626')}
                        stroke="white"
                        strokeWidth="3"
                        className="group-hover:r-12 transition-all duration-300"
                      />

                      {/* Inner dot */}
                      <circle
                        cx={coord.x}
                        cy={coord.y}
                        r="4"
                        fill="white"
                        opacity="0.9"
                      />

                      {/* Factory icon */}
                      <text
                        x={coord.x}
                        y={coord.y - 20}
                        textAnchor="middle"
                        className="text-sm"
                        style={{ fontSize: '16px' }}
                      >
                        🏭
                      </text>

                      {/* City label */}
                      <text
                        x={coord.x}
                        y={coord.y + 30}
                        textAnchor="middle"
                        className="text-xs font-bold fill-gray-800"
                        style={{ textShadow: '1px 1px 2px rgba(255,255,255,0.8)' }}
                      >
                        {factory.name.split(',')[0]}
                      </text>

                      {/* Enhanced hover info box */}
                      <g className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {/* Info box shadow */}
                        <rect
                          x={coord.x - 75}
                          y={coord.y - 75}
                          width="150"
                          height="50"
                          fill="rgba(0,0,0,0.1)"
                          rx="8"
                        />
                        {/* Info box */}
                        <rect
                          x={coord.x - 75}
                          y={coord.y - 75}
                          width="150"
                          height="50"
                          fill="rgba(0,0,0,0.9)"
                          stroke="rgba(255,255,255,0.2)"
                          strokeWidth="1"
                          rx="8"
                        />
                        {/* Factory name */}
                        <text
                          x={coord.x}
                          y={coord.y - 55}
                          textAnchor="middle"
                          className="text-sm font-bold fill-white"
                        >
                          {factory.name}
                        </text>
                        {/* Workers count */}
                        <text
                          x={coord.x}
                          y={coord.y - 40}
                          textAnchor="middle"
                          className="text-xs fill-gray-300"
                        >
                          👥 {factory.workers} Workers
                        </text>
                        {/* Status */}
                        <text
                          x={coord.x}
                          y={coord.y - 28}
                          textAnchor="middle"
                          className="text-xs fill-green-400"
                        >
                          ✅ Fully Operational
                        </text>
                      </g>
                    </g>
                  );
                })}

                {/* Enhanced connection lines between factories */}
                <defs>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.6"/>
                    <stop offset="25%" stopColor="#8b5cf6" stopOpacity="0.8"/>
                    <stop offset="75%" stopColor="#06b6d4" stopOpacity="0.8"/>
                    <stop offset="100%" stopColor="#10b981" stopOpacity="0.6"/>
                  </linearGradient>

                  <filter id="glow">
                    <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                    <feMerge>
                      <feMergeNode in="coloredBlur"/>
                      <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                  </filter>
                </defs>

                {/* Network connection lines with realistic curves */}
                {/* Shenzhen to Dongguan */}
                <path
                  d="M 720 200 Q 717 202 715 205"
                  stroke="url(#connectionGradient)"
                  strokeWidth="3"
                  fill="none"
                  strokeDasharray="8,4"
                  className="animate-pulse"
                  filter="url(#glow)"
                  opacity="0.7"
                />

                {/* Dongguan to Guangzhou */}
                <path
                  d="M 715 205 Q 712 201 710 198"
                  stroke="url(#connectionGradient)"
                  strokeWidth="3"
                  fill="none"
                  strokeDasharray="8,4"
                  className="animate-pulse"
                  filter="url(#glow)"
                  opacity="0.7"
                />

                {/* Guangzhou to Vietnam (longer connection) */}
                <path
                  d="M 710 198 Q 700 240 690 290"
                  stroke="url(#connectionGradient)"
                  strokeWidth="3"
                  fill="none"
                  strokeDasharray="12,6"
                  className="animate-pulse"
                  filter="url(#glow)"
                  opacity="0.7"
                />

                {/* Shenzhen to Vietnam (direct connection) */}
                <path
                  d="M 720 200 Q 705 245 690 290"
                  stroke="url(#connectionGradient)"
                  strokeWidth="2"
                  fill="none"
                  strokeDasharray="6,8"
                  className="animate-pulse"
                  filter="url(#glow)"
                  opacity="0.5"
                />

                {/* Data flow indicators */}
                <circle r="3" fill="#3b82f6" opacity="0.8">
                  <animateMotion dur="4s" repeatCount="indefinite">
                    <path d="M 720 200 Q 717 202 715 205"/>
                  </animateMotion>
                </circle>

                <circle r="3" fill="#8b5cf6" opacity="0.8">
                  <animateMotion dur="5s" repeatCount="indefinite">
                    <path d="M 715 205 Q 712 201 710 198"/>
                  </animateMotion>
                </circle>

                <circle r="3" fill="#10b981" opacity="0.8">
                  <animateMotion dur="6s" repeatCount="indefinite">
                    <path d="M 710 198 Q 700 240 690 290"/>
                  </animateMotion>
                </circle>

                {/* Legend */}
                <g transform="translate(50, 400)">
                  <rect x="0" y="0" width="200" height="80" fill="white" fillOpacity="0.9" stroke="#e5e7eb" rx="5"/>
                  <text x="10" y="15" className="text-xs font-semibold fill-gray-900">Factory Locations</text>

                  <circle cx="15" cy="30" r="4" fill="#3b82f6"/>
                  <text x="25" y="35" className="text-xs fill-gray-700">Technology Hub</text>

                  <circle cx="15" cy="45" r="4" fill="#10b981"/>
                  <text x="25" y="50" className="text-xs fill-gray-700">Manufacturing</text>

                  <circle cx="15" cy="60" r="4" fill="#8b5cf6"/>
                  <text x="25" y="65" className="text-xs fill-gray-700">Design Center</text>

                  <circle cx="120" cy="30" r="4" fill="#ef4444"/>
                  <text x="130" y="35" className="text-xs fill-gray-700">Assembly Hub</text>
                </g>

                {/* Network Stats */}
                <g transform="translate(750, 50)">
                  <rect x="0" y="0" width="180" height="100" fill="white" fillOpacity="0.9" stroke="#e5e7eb" rx="5"/>
                  <text x="10" y="15" className="text-xs font-semibold fill-gray-900">Network Overview</text>
                  <text x="10" y="30" className="text-xs fill-gray-700">🏭 4 Manufacturing Sites</text>
                  <text x="10" y="45" className="text-xs fill-gray-700">👥 3,050+ Total Workers</text>
                  <text x="10" y="60" className="text-xs fill-gray-700">🌍 2 Countries</text>
                  <text x="10" y="75" className="text-xs fill-gray-700">✅ 100% Compliant</text>
                  <text x="10" y="90" className="text-xs fill-gray-700">🔗 Fully Connected</text>
                </g>
              </svg>
            </div>
          </div>
        </div>

        {/* Enhanced Factory Details Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {factories.map((factory, index) => (
            <div
              key={factory.id}
              className="group bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-gray-200"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Factory Header with Gradient */}
              <div className={`relative h-24 ${factory.color} bg-gradient-to-br from-current to-opacity-80`}>
                <div className="absolute inset-0 bg-black bg-opacity-10"></div>
                <div className="relative p-4 flex items-center justify-between text-white">
                  <div>
                    <h3 className="text-lg font-bold">{factory.name.split(',')[0]}</h3>
                    <p className="text-sm opacity-90">{factory.name.split(',')[1]}</p>
                  </div>
                  <div className="bg-white bg-opacity-20 rounded-full p-2">
                    <MapPin className="h-6 w-6" />
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-2 right-2 w-8 h-8 bg-white bg-opacity-10 rounded-full"></div>
                <div className="absolute bottom-2 left-2 w-4 h-4 bg-white bg-opacity-10 rounded-full"></div>
              </div>

              {/* Factory Content */}
              <div className="p-6">
                <p className="text-gray-600 mb-6 text-sm leading-relaxed line-clamp-3">
                  {factory.description}
                </p>

                {/* Key Metrics */}
                <div className="space-y-4 mb-6">
                  {/* Workers Count */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="bg-blue-100 p-2 rounded-lg mr-3">
                        <Users className="h-4 w-4 text-blue-600" />
                      </div>
                      <span className="text-sm text-gray-700">Workforce</span>
                    </div>
                    <span className="font-bold text-gray-900">{factory.workers}</span>
                  </div>

                  {/* Compliance Status */}
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="bg-green-100 p-2 rounded-lg mr-3">
                        <Shield className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-sm text-gray-700">Compliance</span>
                    </div>
                    <span className="font-bold text-green-600">100%</span>
                  </div>
                </div>

                {/* Certifications */}
                <div className="mb-4">
                  <div className="flex items-center mb-3">
                    <Award className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm font-medium text-gray-700">Certifications</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {factory.certifications.map((cert, certIndex) => (
                      <span
                        key={certIndex}
                        className="bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 px-3 py-1 rounded-full text-xs font-medium border border-blue-200"
                      >
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Status Badge */}
                <div className="flex justify-center">
                  <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full text-sm font-medium shadow-lg">
                    <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                    Fully Operational
                  </div>
                </div>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-transparent to-white opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        {/* Enhanced Compliance & Statistics Section */}
        <div className="mt-12 space-y-8">
          {/* Main Statistics Dashboard */}
          <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 rounded-2xl p-8 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-400 to-purple-600"></div>
              <div className="absolute top-4 left-4 w-32 h-32 bg-white rounded-full opacity-5"></div>
              <div className="absolute bottom-4 right-4 w-24 h-24 bg-white rounded-full opacity-5"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-white rounded-full opacity-3"></div>
            </div>

            <div className="relative z-10">
              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold mb-4">Our Commitment to Ethical Manufacturing</h3>
                <p className="text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
                  Every facility in our global network operates under the strictest ethical standards,
                  ensuring worker dignity, fair compensation, and safe working conditions.
                </p>
              </div>

              {/* Statistics Grid */}
              <div className="grid md:grid-cols-4 gap-8">
                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                      3,050+
                    </div>
                    <div className="text-blue-100 font-medium">Total Workers Protected</div>
                    <div className="mt-2 text-xs text-blue-200">Across all facilities</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">
                      100%
                    </div>
                    <div className="text-blue-100 font-medium">Compliance Rate</div>
                    <div className="mt-2 text-xs text-blue-200">Zero violations</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-red-400 to-pink-400 bg-clip-text text-transparent">
                      0
                    </div>
                    <div className="text-blue-100 font-medium">Forced Labor Incidents</div>
                    <div className="mt-2 text-xs text-blue-200">Lifetime record</div>
                  </div>
                </div>

                <div className="text-center group">
                  <div className="bg-white bg-opacity-10 rounded-2xl p-6 backdrop-blur-sm border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                    <div className="text-4xl font-bold mb-2 bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                      24/7
                    </div>
                    <div className="text-blue-100 font-medium">Monitoring System</div>
                    <div className="mt-2 text-xs text-blue-200">Continuous oversight</div>
                  </div>
                </div>
              </div>

              {/* Commitment Badges */}
              <div className="mt-8 flex flex-wrap justify-center gap-4">
                <div className="bg-green-500 bg-opacity-20 border border-green-400 rounded-full px-6 py-2 text-green-100 text-sm font-medium">
                  ✓ ISO 14001 Certified
                </div>
                <div className="bg-blue-500 bg-opacity-20 border border-blue-400 rounded-full px-6 py-2 text-blue-100 text-sm font-medium">
                  ✓ SA8000 Compliant
                </div>
                <div className="bg-purple-500 bg-opacity-20 border border-purple-400 rounded-full px-6 py-2 text-purple-100 text-sm font-medium">
                  ✓ WRAP Certified
                </div>
                <div className="bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-full px-6 py-2 text-yellow-100 text-sm font-medium">
                  ✓ Zero Xinjiang Cotton
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Whitepaper Download */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t('whitepaper_title')}
            </h3>
            <p className="text-gray-600 mb-6">
              {t('whitepaper_description')}
            </p>
            <a 
              href="/sparkle_industry_whitepaper.html" 
              target="_blank"
              className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              {t('whitepaper_download')}
              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FactoryMap;
