import { useTranslation } from 'react-i18next';

const Footer = () => {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h3 className="text-2xl font-bold mb-4">{t('company_name')}</h3>
          <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
            {t('footer_values')}
          </p>
          <div className="border-t border-gray-800 pt-6">
            <p className="text-gray-400">
              {t('footer_copyright')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

