"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.coerce = exports.iso = exports.ZodISODuration = exports.ZodISOTime = exports.ZodISODate = exports.ZodISODateTime = exports.locales = exports.NEVER = exports.TimePrecision = exports.toJSONSchema = exports.flattenError = exports.formatError = exports.prettifyError = exports.treeifyError = exports.regexes = exports.clone = exports.$brand = exports.$input = exports.$output = exports.function = exports.config = exports.registry = exports.globalRegistry = exports.core = void 0;
exports.core = __importStar(require("../core/index.cjs"));
__exportStar(require("./schemas.cjs"), exports);
__exportStar(require("./checks.cjs"), exports);
__exportStar(require("./errors.cjs"), exports);
__exportStar(require("./parse.cjs"), exports);
__exportStar(require("./compat.cjs"), exports);
// zod-specified
const index_js_1 = require("../core/index.cjs");
const en_js_1 = __importDefault(require("../locales/en.cjs"));
(0, index_js_1.config)((0, en_js_1.default)());
var index_js_2 = require("../core/index.cjs");
Object.defineProperty(exports, "globalRegistry", { enumerable: true, get: function () { return index_js_2.globalRegistry; } });
Object.defineProperty(exports, "registry", { enumerable: true, get: function () { return index_js_2.registry; } });
Object.defineProperty(exports, "config", { enumerable: true, get: function () { return index_js_2.config; } });
Object.defineProperty(exports, "function", { enumerable: true, get: function () { return index_js_2.function; } });
Object.defineProperty(exports, "$output", { enumerable: true, get: function () { return index_js_2.$output; } });
Object.defineProperty(exports, "$input", { enumerable: true, get: function () { return index_js_2.$input; } });
Object.defineProperty(exports, "$brand", { enumerable: true, get: function () { return index_js_2.$brand; } });
Object.defineProperty(exports, "clone", { enumerable: true, get: function () { return index_js_2.clone; } });
Object.defineProperty(exports, "regexes", { enumerable: true, get: function () { return index_js_2.regexes; } });
Object.defineProperty(exports, "treeifyError", { enumerable: true, get: function () { return index_js_2.treeifyError; } });
Object.defineProperty(exports, "prettifyError", { enumerable: true, get: function () { return index_js_2.prettifyError; } });
Object.defineProperty(exports, "formatError", { enumerable: true, get: function () { return index_js_2.formatError; } });
Object.defineProperty(exports, "flattenError", { enumerable: true, get: function () { return index_js_2.flattenError; } });
Object.defineProperty(exports, "toJSONSchema", { enumerable: true, get: function () { return index_js_2.toJSONSchema; } });
Object.defineProperty(exports, "TimePrecision", { enumerable: true, get: function () { return index_js_2.TimePrecision; } });
Object.defineProperty(exports, "NEVER", { enumerable: true, get: function () { return index_js_2.NEVER; } });
exports.locales = __importStar(require("../locales/index.cjs"));
// iso
// must be exported from top-level
// https://github.com/colinhacks/zod/issues/4491
var iso_js_1 = require("./iso.cjs");
Object.defineProperty(exports, "ZodISODateTime", { enumerable: true, get: function () { return iso_js_1.ZodISODateTime; } });
Object.defineProperty(exports, "ZodISODate", { enumerable: true, get: function () { return iso_js_1.ZodISODate; } });
Object.defineProperty(exports, "ZodISOTime", { enumerable: true, get: function () { return iso_js_1.ZodISOTime; } });
Object.defineProperty(exports, "ZodISODuration", { enumerable: true, get: function () { return iso_js_1.ZodISODuration; } });
exports.iso = __importStar(require("./iso.cjs"));
exports.coerce = __importStar(require("./coerce.cjs"));
