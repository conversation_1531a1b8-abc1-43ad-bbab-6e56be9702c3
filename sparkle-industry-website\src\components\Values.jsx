import { useTranslation } from 'react-i18next';
import { Shield, Heart, Scale, Award } from 'lucide-react';

const Values = () => {
  const { t } = useTranslation();

  const values = [
    {
      icon: Heart,
      title: t('values_labor_title'),
      content: t('values_labor_content'),
      color: 'text-red-600 bg-red-100'
    },
    {
      icon: Shield,
      title: t('values_cotton_title'),
      content: t('values_cotton_content'),
      color: 'text-blue-600 bg-blue-100'
    },
    {
      icon: Scale,
      title: t('values_compliance_title'),
      content: t('values_compliance_content'),
      color: 'text-green-600 bg-green-100'
    },
    {
      icon: Award,
      title: t('values_quality_title'),
      content: t('values_quality_content'),
      color: 'text-purple-600 bg-purple-100'
    }
  ];

  return (
    <section id="values" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            {t('values_title')}
          </h2>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => {
            const IconComponent = value.icon;
            return (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className={`inline-flex p-3 rounded-full ${value.color} mb-4`}>
                  <IconComponent className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.content}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Values;

