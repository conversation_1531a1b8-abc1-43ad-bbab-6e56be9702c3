import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';

const Header = () => {
  const { t, i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'zh' : 'en';
    i18n.changeLanguage(newLang);
  };

  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900">
              {t('company_name')}
            </h1>
          </div>
          
          <nav className="hidden md:flex space-x-6">
            <a href="#home" className="text-gray-700 hover:text-gray-900 transition-colors">
              {t('nav_home')}
            </a>
            <a href="#about" className="text-gray-700 hover:text-gray-900 transition-colors">
              {t('nav_about')}
            </a>
            <a href="#values" className="text-gray-700 hover:text-gray-900 transition-colors">
              {t('nav_values')}
            </a>
            <a href="#factories" className="text-gray-700 hover:text-gray-900 transition-colors">
              Factories
            </a>
            <a href="#partners" className="text-gray-700 hover:text-gray-900 transition-colors">
              Partners
            </a>
            <a href="#charity" className="text-gray-700 hover:text-gray-900 transition-colors">
              {t('nav_charity')}
            </a>
            <a href="#contact" className="text-gray-700 hover:text-gray-900 transition-colors">
              {t('nav_contact')}
            </a>
          </nav>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleLanguage}
            className="flex items-center gap-2"
          >
            <Globe className="h-4 w-4" />
            {t('language_switch')}
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;

