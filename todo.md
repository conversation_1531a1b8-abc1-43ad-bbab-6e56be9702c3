## 任务列表

- [x] 阶段1：分析企业理念并制定内容框架
  - [x] 仔细阅读用户提供的企业理念和价值观
  - [x] 提炼核心关键词和概念
  - [x] 构思文档的整体结构，包括引言、核心价值观、使命、承诺等部分
  - [x] 确定每个部分的重点内容

- [x] 阶段2：撰写企业价值和介绍文档
  - [x] 根据内容框架，撰写英文的企业价值声明
  - [x] 确保所有用户提及的理念都得到体现，如“绝不使用新疆棉”、“完全尊重劳动”、“关注人权”、“支持公益”等
  - [x] 强调公司对劳工权益、休息时间、合法合规的承诺
  - [x] 润色语言，使其专业、流畅且具有说服力

- [x] 阶段3：完善文档格式并交付给用户
  - [x] 将撰写好的内容整理成一个完整的文档
  - [x] 检查语法、拼写和标点错误
  - [x] 确保文档排版清晰、易读
  - [x] 以Markdown格式保存文档，并作为附件发送给用户

- [x] 阶段1：收集并整理公司主页和公益介绍内容
  - [x] 编写公司主页的中文和英文内容大纲
  - [x] 编写儿童公益介绍的中文和英文内容大纲
  - [x] 考虑如何将企业价值融入主页内容

- [x] 阶段2：创建React项目并配置多语言环境
  - [x] 使用React创建新的项目（已使用Vite + React）
  - [x] 安装并配置多语言库（如`react-i18next`）
  - [x] 设置语言检测逻辑（基于UA）

- [x] 阶段3：开发主页内容和语言切换功能
  - [x] 创建主页组件和公益介绍组件
  - [x] 实现中英文内容的显示
  - [x] 添加语言切换按钮
  - [x] 整合企业价值内容

- [x] 阶段4：本地测试并部署前端项目
  - [x] 在本地运行项目进行测试
  - [x] 确保多语言切换和UA检测功能正常
  - [x] 项目构建成功，可以部署

