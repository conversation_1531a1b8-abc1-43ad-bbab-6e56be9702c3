(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const d of o)if(d.type==="childList")for(const m of d.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&s(m)}).observe(document,{childList:!0,subtree:!0});function c(o){const d={};return o.integrity&&(d.integrity=o.integrity),o.referrerPolicy&&(d.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?d.credentials="include":o.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function s(o){if(o.ep)return;o.ep=!0;const d=c(o);fetch(o.href,d)}})();var Tc={exports:{}},qn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qd;function pp(){if(qd)return qn;qd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function c(s,o,d){var m=null;if(d!==void 0&&(m=""+d),o.key!==void 0&&(m=""+o.key),"key"in o){d={};for(var p in o)p!=="key"&&(d[p]=o[p])}else d=o;return o=d.ref,{$$typeof:r,type:s,key:m,ref:o!==void 0?o:null,props:d}}return qn.Fragment=u,qn.jsx=c,qn.jsxs=c,qn}var kd;function yp(){return kd||(kd=1,Tc.exports=pp()),Tc.exports}var S=yp(),Ac={exports:{}},at={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function vp(){if(Gd)return at;Gd=1;var r=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),m=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),O=Symbol.for("react.lazy"),R=Symbol.iterator;function B(b){return b===null||typeof b!="object"?null:(b=R&&b[R]||b["@@iterator"],typeof b=="function"?b:null)}var q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},U=Object.assign,Z={};function F(b,w,k){this.props=b,this.context=w,this.refs=Z,this.updater=k||q}F.prototype.isReactComponent={},F.prototype.setState=function(b,w){if(typeof b!="object"&&typeof b!="function"&&b!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,b,w,"setState")},F.prototype.forceUpdate=function(b){this.updater.enqueueForceUpdate(this,b,"forceUpdate")};function bt(){}bt.prototype=F.prototype;function ft(b,w,k){this.props=b,this.context=w,this.refs=Z,this.updater=k||q}var st=ft.prototype=new bt;st.constructor=ft,U(st,F.prototype),st.isPureReactComponent=!0;var ot=Array.isArray,J={H:null,A:null,T:null,S:null,V:null},xt=Object.prototype.hasOwnProperty;function mt(b,w,k,L,Y,lt){return k=lt.ref,{$$typeof:r,type:b,key:w,ref:k!==void 0?k:null,props:lt}}function G(b,w){return mt(b.type,w,void 0,void 0,void 0,b.props)}function et(b){return typeof b=="object"&&b!==null&&b.$$typeof===r}function dt(b){var w={"=":"=0",":":"=2"};return"$"+b.replace(/[=:]/g,function(k){return w[k]})}var wt=/\/+/g;function Dt(b,w){return typeof b=="object"&&b!==null&&b.key!=null?dt(""+b.key):w.toString(36)}function Bt(){}function Ct(b){switch(b.status){case"fulfilled":return b.value;case"rejected":throw b.reason;default:switch(typeof b.status=="string"?b.then(Bt,Bt):(b.status="pending",b.then(function(w){b.status==="pending"&&(b.status="fulfilled",b.value=w)},function(w){b.status==="pending"&&(b.status="rejected",b.reason=w)})),b.status){case"fulfilled":return b.value;case"rejected":throw b.reason}}throw b}function Rt(b,w,k,L,Y){var lt=typeof b;(lt==="undefined"||lt==="boolean")&&(b=null);var I=!1;if(b===null)I=!0;else switch(lt){case"bigint":case"string":case"number":I=!0;break;case"object":switch(b.$$typeof){case r:case u:I=!0;break;case O:return I=b._init,Rt(I(b._payload),w,k,L,Y)}}if(I)return Y=Y(b),I=L===""?"."+Dt(b,0):L,ot(Y)?(k="",I!=null&&(k=I.replace(wt,"$&/")+"/"),Rt(Y,w,k,"",function(re){return re})):Y!=null&&(et(Y)&&(Y=G(Y,k+(Y.key==null||b&&b.key===Y.key?"":(""+Y.key).replace(wt,"$&/")+"/")+I)),w.push(Y)),1;I=0;var pt=L===""?".":L+":";if(ot(b))for(var At=0;At<b.length;At++)L=b[At],lt=pt+Dt(L,At),I+=Rt(L,w,k,lt,Y);else if(At=B(b),typeof At=="function")for(b=At.call(b),At=0;!(L=b.next()).done;)L=L.value,lt=pt+Dt(L,At++),I+=Rt(L,w,k,lt,Y);else if(lt==="object"){if(typeof b.then=="function")return Rt(Ct(b),w,k,L,Y);throw w=String(b),Error("Objects are not valid as a React child (found: "+(w==="[object Object]"?"object with keys {"+Object.keys(b).join(", ")+"}":w)+"). If you meant to render a collection of children, use an array instead.")}return I}function z(b,w,k){if(b==null)return b;var L=[],Y=0;return Rt(b,L,"","",function(lt){return w.call(k,lt,Y++)}),L}function H(b){if(b._status===-1){var w=b._result;w=w(),w.then(function(k){(b._status===0||b._status===-1)&&(b._status=1,b._result=k)},function(k){(b._status===0||b._status===-1)&&(b._status=2,b._result=k)}),b._status===-1&&(b._status=0,b._result=w)}if(b._status===1)return b._result.default;throw b._result}var C=typeof reportError=="function"?reportError:function(b){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var w=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof b=="object"&&b!==null&&typeof b.message=="string"?String(b.message):String(b),error:b});if(!window.dispatchEvent(w))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",b);return}console.error(b)};function ht(){}return at.Children={map:z,forEach:function(b,w,k){z(b,function(){w.apply(this,arguments)},k)},count:function(b){var w=0;return z(b,function(){w++}),w},toArray:function(b){return z(b,function(w){return w})||[]},only:function(b){if(!et(b))throw Error("React.Children.only expected to receive a single React element child.");return b}},at.Component=F,at.Fragment=c,at.Profiler=o,at.PureComponent=ft,at.StrictMode=s,at.Suspense=y,at.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=J,at.__COMPILER_RUNTIME={__proto__:null,c:function(b){return J.H.useMemoCache(b)}},at.cache=function(b){return function(){return b.apply(null,arguments)}},at.cloneElement=function(b,w,k){if(b==null)throw Error("The argument must be a React element, but you passed "+b+".");var L=U({},b.props),Y=b.key,lt=void 0;if(w!=null)for(I in w.ref!==void 0&&(lt=void 0),w.key!==void 0&&(Y=""+w.key),w)!xt.call(w,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&w.ref===void 0||(L[I]=w[I]);var I=arguments.length-2;if(I===1)L.children=k;else if(1<I){for(var pt=Array(I),At=0;At<I;At++)pt[At]=arguments[At+2];L.children=pt}return mt(b.type,Y,void 0,void 0,lt,L)},at.createContext=function(b){return b={$$typeof:m,_currentValue:b,_currentValue2:b,_threadCount:0,Provider:null,Consumer:null},b.Provider=b,b.Consumer={$$typeof:d,_context:b},b},at.createElement=function(b,w,k){var L,Y={},lt=null;if(w!=null)for(L in w.key!==void 0&&(lt=""+w.key),w)xt.call(w,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&(Y[L]=w[L]);var I=arguments.length-2;if(I===1)Y.children=k;else if(1<I){for(var pt=Array(I),At=0;At<I;At++)pt[At]=arguments[At+2];Y.children=pt}if(b&&b.defaultProps)for(L in I=b.defaultProps,I)Y[L]===void 0&&(Y[L]=I[L]);return mt(b,lt,void 0,void 0,null,Y)},at.createRef=function(){return{current:null}},at.forwardRef=function(b){return{$$typeof:p,render:b}},at.isValidElement=et,at.lazy=function(b){return{$$typeof:O,_payload:{_status:-1,_result:b},_init:H}},at.memo=function(b,w){return{$$typeof:g,type:b,compare:w===void 0?null:w}},at.startTransition=function(b){var w=J.T,k={};J.T=k;try{var L=b(),Y=J.S;Y!==null&&Y(k,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(ht,C)}catch(lt){C(lt)}finally{J.T=w}},at.unstable_useCacheRefresh=function(){return J.H.useCacheRefresh()},at.use=function(b){return J.H.use(b)},at.useActionState=function(b,w,k){return J.H.useActionState(b,w,k)},at.useCallback=function(b,w){return J.H.useCallback(b,w)},at.useContext=function(b){return J.H.useContext(b)},at.useDebugValue=function(){},at.useDeferredValue=function(b,w){return J.H.useDeferredValue(b,w)},at.useEffect=function(b,w,k){var L=J.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(b,w)},at.useId=function(){return J.H.useId()},at.useImperativeHandle=function(b,w,k){return J.H.useImperativeHandle(b,w,k)},at.useInsertionEffect=function(b,w){return J.H.useInsertionEffect(b,w)},at.useLayoutEffect=function(b,w){return J.H.useLayoutEffect(b,w)},at.useMemo=function(b,w){return J.H.useMemo(b,w)},at.useOptimistic=function(b,w){return J.H.useOptimistic(b,w)},at.useReducer=function(b,w,k){return J.H.useReducer(b,w,k)},at.useRef=function(b){return J.H.useRef(b)},at.useState=function(b){return J.H.useState(b)},at.useSyncExternalStore=function(b,w,k){return J.H.useSyncExternalStore(b,w,k)},at.useTransition=function(){return J.H.useTransition()},at.version="19.1.1",at}var Yd;function Yc(){return Yd||(Yd=1,Ac.exports=vp()),Ac.exports}var Ot=Yc(),zc={exports:{}},kn={},Rc={exports:{}},Mc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function bp(){return Vd||(Vd=1,function(r){function u(z,H){var C=z.length;z.push(H);t:for(;0<C;){var ht=C-1>>>1,b=z[ht];if(0<o(b,H))z[ht]=H,z[C]=b,C=ht;else break t}}function c(z){return z.length===0?null:z[0]}function s(z){if(z.length===0)return null;var H=z[0],C=z.pop();if(C!==H){z[0]=C;t:for(var ht=0,b=z.length,w=b>>>1;ht<w;){var k=2*(ht+1)-1,L=z[k],Y=k+1,lt=z[Y];if(0>o(L,C))Y<b&&0>o(lt,L)?(z[ht]=lt,z[Y]=C,ht=Y):(z[ht]=L,z[k]=C,ht=k);else if(Y<b&&0>o(lt,C))z[ht]=lt,z[Y]=C,ht=Y;else break t}}return H}function o(z,H){var C=z.sortIndex-H.sortIndex;return C!==0?C:z.id-H.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;r.unstable_now=function(){return d.now()}}else{var m=Date,p=m.now();r.unstable_now=function(){return m.now()-p}}var y=[],g=[],O=1,R=null,B=3,q=!1,U=!1,Z=!1,F=!1,bt=typeof setTimeout=="function"?setTimeout:null,ft=typeof clearTimeout=="function"?clearTimeout:null,st=typeof setImmediate<"u"?setImmediate:null;function ot(z){for(var H=c(g);H!==null;){if(H.callback===null)s(g);else if(H.startTime<=z)s(g),H.sortIndex=H.expirationTime,u(y,H);else break;H=c(g)}}function J(z){if(Z=!1,ot(z),!U)if(c(y)!==null)U=!0,xt||(xt=!0,Dt());else{var H=c(g);H!==null&&Rt(J,H.startTime-z)}}var xt=!1,mt=-1,G=5,et=-1;function dt(){return F?!0:!(r.unstable_now()-et<G)}function wt(){if(F=!1,xt){var z=r.unstable_now();et=z;var H=!0;try{t:{U=!1,Z&&(Z=!1,ft(mt),mt=-1),q=!0;var C=B;try{e:{for(ot(z),R=c(y);R!==null&&!(R.expirationTime>z&&dt());){var ht=R.callback;if(typeof ht=="function"){R.callback=null,B=R.priorityLevel;var b=ht(R.expirationTime<=z);if(z=r.unstable_now(),typeof b=="function"){R.callback=b,ot(z),H=!0;break e}R===c(y)&&s(y),ot(z)}else s(y);R=c(y)}if(R!==null)H=!0;else{var w=c(g);w!==null&&Rt(J,w.startTime-z),H=!1}}break t}finally{R=null,B=C,q=!1}H=void 0}}finally{H?Dt():xt=!1}}}var Dt;if(typeof st=="function")Dt=function(){st(wt)};else if(typeof MessageChannel<"u"){var Bt=new MessageChannel,Ct=Bt.port2;Bt.port1.onmessage=wt,Dt=function(){Ct.postMessage(null)}}else Dt=function(){bt(wt,0)};function Rt(z,H){mt=bt(function(){z(r.unstable_now())},H)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(z){z.callback=null},r.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<z?Math.floor(1e3/z):5},r.unstable_getCurrentPriorityLevel=function(){return B},r.unstable_next=function(z){switch(B){case 1:case 2:case 3:var H=3;break;default:H=B}var C=B;B=H;try{return z()}finally{B=C}},r.unstable_requestPaint=function(){F=!0},r.unstable_runWithPriority=function(z,H){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var C=B;B=z;try{return H()}finally{B=C}},r.unstable_scheduleCallback=function(z,H,C){var ht=r.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?ht+C:ht):C=ht,z){case 1:var b=-1;break;case 2:b=250;break;case 5:b=1073741823;break;case 4:b=1e4;break;default:b=5e3}return b=C+b,z={id:O++,callback:H,priorityLevel:z,startTime:C,expirationTime:b,sortIndex:-1},C>ht?(z.sortIndex=C,u(g,z),c(y)===null&&z===c(g)&&(Z?(ft(mt),mt=-1):Z=!0,Rt(J,C-ht))):(z.sortIndex=b,u(y,z),U||q||(U=!0,xt||(xt=!0,Dt()))),z},r.unstable_shouldYield=dt,r.unstable_wrapCallback=function(z){var H=B;return function(){var C=B;B=H;try{return z.apply(this,arguments)}finally{B=C}}}}(Mc)),Mc}var Xd;function xp(){return Xd||(Xd=1,Rc.exports=bp()),Rc.exports}var wc={exports:{}},ae={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function Sp(){if(Qd)return ae;Qd=1;var r=Yc();function u(y){var g="https://react.dev/errors/"+y;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var O=2;O<arguments.length;O++)g+="&args[]="+encodeURIComponent(arguments[O])}return"Minified React error #"+y+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(u(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function d(y,g,O){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:R==null?null:""+R,children:y,containerInfo:g,implementation:O}}var m=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function p(y,g){if(y==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return ae.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,ae.createPortal=function(y,g){var O=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(u(299));return d(y,g,null,O)},ae.flushSync=function(y){var g=m.T,O=s.p;try{if(m.T=null,s.p=2,y)return y()}finally{m.T=g,s.p=O,s.d.f()}},ae.preconnect=function(y,g){typeof y=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,s.d.C(y,g))},ae.prefetchDNS=function(y){typeof y=="string"&&s.d.D(y)},ae.preinit=function(y,g){if(typeof y=="string"&&g&&typeof g.as=="string"){var O=g.as,R=p(O,g.crossOrigin),B=typeof g.integrity=="string"?g.integrity:void 0,q=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;O==="style"?s.d.S(y,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:R,integrity:B,fetchPriority:q}):O==="script"&&s.d.X(y,{crossOrigin:R,integrity:B,fetchPriority:q,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},ae.preinitModule=function(y,g){if(typeof y=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var O=p(g.as,g.crossOrigin);s.d.M(y,{crossOrigin:O,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&s.d.M(y)},ae.preload=function(y,g){if(typeof y=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var O=g.as,R=p(O,g.crossOrigin);s.d.L(y,O,{crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},ae.preloadModule=function(y,g){if(typeof y=="string")if(g){var O=p(g.as,g.crossOrigin);s.d.m(y,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:O,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else s.d.m(y)},ae.requestFormReset=function(y){s.d.r(y)},ae.unstable_batchedUpdates=function(y,g){return y(g)},ae.useFormState=function(y,g,O){return m.H.useFormState(y,g,O)},ae.useFormStatus=function(){return m.H.useHostTransitionStatus()},ae.version="19.1.1",ae}var Zd;function _p(){if(Zd)return wc.exports;Zd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),wc.exports=Sp(),wc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kd;function Op(){if(Kd)return kn;Kd=1;var r=xp(),u=Yc(),c=_p();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function m(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function p(t){if(d(t)!==t)throw Error(s(188))}function y(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(s(188));return e!==t?null:t}for(var a=t,l=e;;){var n=a.return;if(n===null)break;var i=n.alternate;if(i===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===a)return p(n),t;if(i===l)return p(n),e;i=i.sibling}throw Error(s(188))}if(a.return!==l.return)a=n,l=i;else{for(var f=!1,h=n.child;h;){if(h===a){f=!0,a=n,l=i;break}if(h===l){f=!0,l=n,a=i;break}h=h.sibling}if(!f){for(h=i.child;h;){if(h===a){f=!0,a=i,l=n;break}if(h===l){f=!0,l=i,a=n;break}h=h.sibling}if(!f)throw Error(s(189))}}if(a.alternate!==l)throw Error(s(190))}if(a.tag!==3)throw Error(s(188));return a.stateNode.current===a?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var O=Object.assign,R=Symbol.for("react.element"),B=Symbol.for("react.transitional.element"),q=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),Z=Symbol.for("react.strict_mode"),F=Symbol.for("react.profiler"),bt=Symbol.for("react.provider"),ft=Symbol.for("react.consumer"),st=Symbol.for("react.context"),ot=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),xt=Symbol.for("react.suspense_list"),mt=Symbol.for("react.memo"),G=Symbol.for("react.lazy"),et=Symbol.for("react.activity"),dt=Symbol.for("react.memo_cache_sentinel"),wt=Symbol.iterator;function Dt(t){return t===null||typeof t!="object"?null:(t=wt&&t[wt]||t["@@iterator"],typeof t=="function"?t:null)}var Bt=Symbol.for("react.client.reference");function Ct(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Bt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case U:return"Fragment";case F:return"Profiler";case Z:return"StrictMode";case J:return"Suspense";case xt:return"SuspenseList";case et:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case q:return"Portal";case st:return(t.displayName||"Context")+".Provider";case ft:return(t._context.displayName||"Context")+".Consumer";case ot:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case mt:return e=t.displayName||null,e!==null?e:Ct(t.type)||"Memo";case G:e=t._payload,t=t._init;try{return Ct(t(e))}catch{}}return null}var Rt=Array.isArray,z=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,C={pending:!1,data:null,method:null,action:null},ht=[],b=-1;function w(t){return{current:t}}function k(t){0>b||(t.current=ht[b],ht[b]=null,b--)}function L(t,e){b++,ht[b]=t.current,t.current=e}var Y=w(null),lt=w(null),I=w(null),pt=w(null);function At(t,e){switch(L(I,e),L(lt,t),L(Y,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?hd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=hd(e),t=gd(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}k(Y),L(Y,t)}function re(){k(Y),k(lt),k(I)}function aa(t){t.memoizedState!==null&&L(pt,t);var e=Y.current,a=gd(e,t.type);e!==a&&(L(lt,t),L(Y,a))}function la(t){lt.current===t&&(k(Y),k(lt)),pt.current===t&&(k(pt),Cn._currentValue=C)}var na=Object.prototype.hasOwnProperty,hu=r.unstable_scheduleCallback,gu=r.unstable_cancelCallback,Jh=r.unstable_shouldYield,$h=r.unstable_requestPaint,je=r.unstable_now,Wh=r.unstable_getCurrentPriorityLevel,Qc=r.unstable_ImmediatePriority,Zc=r.unstable_UserBlockingPriority,Zn=r.unstable_NormalPriority,Fh=r.unstable_LowPriority,Kc=r.unstable_IdlePriority,Ph=r.log,Ih=r.unstable_setDisableYieldValue,Gl=null,oe=null;function ia(t){if(typeof Ph=="function"&&Ih(t),oe&&typeof oe.setStrictMode=="function")try{oe.setStrictMode(Gl,t)}catch{}}var fe=Math.clz32?Math.clz32:ag,tg=Math.log,eg=Math.LN2;function ag(t){return t>>>=0,t===0?32:31-(tg(t)/eg|0)|0}var Kn=256,Jn=4194304;function Ma(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function $n(t,e,a){var l=t.pendingLanes;if(l===0)return 0;var n=0,i=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var h=l&134217727;return h!==0?(l=h&~i,l!==0?n=Ma(l):(f&=h,f!==0?n=Ma(f):a||(a=h&~t,a!==0&&(n=Ma(a))))):(h=l&~i,h!==0?n=Ma(h):f!==0?n=Ma(f):a||(a=l&~t,a!==0&&(n=Ma(a)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,a=e&-e,i>=a||i===32&&(a&4194048)!==0)?e:n}function Yl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function lg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jc(){var t=Kn;return Kn<<=1,(Kn&4194048)===0&&(Kn=256),t}function $c(){var t=Jn;return Jn<<=1,(Jn&62914560)===0&&(Jn=4194304),t}function mu(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function Vl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function ng(t,e,a,l,n,i){var f=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var h=t.entanglements,v=t.expirationTimes,E=t.hiddenUpdates;for(a=f&~a;0<a;){var M=31-fe(a),D=1<<M;h[M]=0,v[M]=-1;var T=E[M];if(T!==null)for(E[M]=null,M=0;M<T.length;M++){var A=T[M];A!==null&&(A.lane&=-536870913)}a&=~D}l!==0&&Wc(t,l,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(f&~e))}function Wc(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-fe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|a&4194090}function Fc(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var l=31-fe(a),n=1<<l;n&e|t[l]&e&&(t[l]|=e),a&=~n}}function pu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function yu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Pc(){var t=H.p;return t!==0?t:(t=window.event,t===void 0?32:Dd(t.type))}function ig(t,e){var a=H.p;try{return H.p=t,e()}finally{H.p=a}}var ua=Math.random().toString(36).slice(2),te="__reactFiber$"+ua,ne="__reactProps$"+ua,Wa="__reactContainer$"+ua,vu="__reactEvents$"+ua,ug="__reactListeners$"+ua,sg="__reactHandles$"+ua,Ic="__reactResources$"+ua,Xl="__reactMarker$"+ua;function bu(t){delete t[te],delete t[ne],delete t[vu],delete t[ug],delete t[sg]}function Fa(t){var e=t[te];if(e)return e;for(var a=t.parentNode;a;){if(e=a[Wa]||a[te]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=vd(t);t!==null;){if(a=t[te])return a;t=vd(t)}return e}t=a,a=t.parentNode}return null}function Pa(t){if(t=t[te]||t[Wa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ql(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function Ia(t){var e=t[Ic];return e||(e=t[Ic]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[Xl]=!0}var tr=new Set,er={};function wa(t,e){tl(t,e),tl(t+"Capture",e)}function tl(t,e){for(er[t]=e,t=0;t<e.length;t++)tr.add(e[t])}var cg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ar={},lr={};function rg(t){return na.call(lr,t)?!0:na.call(ar,t)?!1:cg.test(t)?lr[t]=!0:(ar[t]=!0,!1)}function Wn(t,e,a){if(rg(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function Fn(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function qe(t,e,a,l){if(l===null)t.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+l)}}var xu,nr;function el(t){if(xu===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);xu=e&&e[1]||"",nr=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+xu+t+nr}var Su=!1;function _u(t,e){if(!t||Su)return"";Su=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var D=function(){throw Error()};if(Object.defineProperty(D.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(D,[])}catch(A){var T=A}Reflect.construct(t,[],D)}else{try{D.call()}catch(A){T=A}t.call(D.prototype)}}else{try{throw Error()}catch(A){T=A}(D=t())&&typeof D.catch=="function"&&D.catch(function(){})}}catch(A){if(A&&T&&typeof A.stack=="string")return[A.stack,T.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=l.DetermineComponentFrameRoot(),f=i[0],h=i[1];if(f&&h){var v=f.split(`
`),E=h.split(`
`);for(n=l=0;l<v.length&&!v[l].includes("DetermineComponentFrameRoot");)l++;for(;n<E.length&&!E[n].includes("DetermineComponentFrameRoot");)n++;if(l===v.length||n===E.length)for(l=v.length-1,n=E.length-1;1<=l&&0<=n&&v[l]!==E[n];)n--;for(;1<=l&&0<=n;l--,n--)if(v[l]!==E[n]){if(l!==1||n!==1)do if(l--,n--,0>n||v[l]!==E[n]){var M=`
`+v[l].replace(" at new "," at ");return t.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",t.displayName)),M}while(1<=l&&0<=n);break}}}finally{Su=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?el(a):""}function og(t){switch(t.tag){case 26:case 27:case 5:return el(t.type);case 16:return el("Lazy");case 13:return el("Suspense");case 19:return el("SuspenseList");case 0:case 15:return _u(t.type,!1);case 11:return _u(t.type.render,!1);case 1:return _u(t.type,!0);case 31:return el("Activity");default:return""}}function ir(t){try{var e="";do e+=og(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ur(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function fg(t){var e=ur(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,i=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,i.call(this,f)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Pn(t){t._valueTracker||(t._valueTracker=fg(t))}function sr(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),l="";return t&&(l=ur(t)?t.checked?"true":"false":t.value),t=l,t!==a?(e.setValue(t),!0):!1}function In(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var dg=/[\n"\\]/g;function xe(t){return t.replace(dg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ou(t,e,a,l,n,i,f,h){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?Nu(t,f,be(e)):a!=null?Nu(t,f,be(a)):l!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+be(h):t.removeAttribute("name")}function cr(t,e,a,l,n,i,f,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||a!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;a=a!=null?""+be(a):"",e=e!=null?""+be(e):a,h||e===t.value||(t.value=e),t.defaultValue=e}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=h?t.checked:!!l,t.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function Nu(t,e,a){e==="number"&&In(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function al(t,e,a,l){if(t=t.options,e){e={};for(var n=0;n<a.length;n++)e["$"+a[n]]=!0;for(a=0;a<t.length;a++)n=e.hasOwnProperty("$"+t[a].value),t[a].selected!==n&&(t[a].selected=n),n&&l&&(t[a].defaultSelected=!0)}else{for(a=""+be(a),e=null,n=0;n<t.length;n++){if(t[n].value===a){t[n].selected=!0,l&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function rr(t,e,a){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+be(a):""}function or(t,e,a,l){if(e==null){if(l!=null){if(a!=null)throw Error(s(92));if(Rt(l)){if(1<l.length)throw Error(s(93));l=l[0]}a=l}a==null&&(a=""),e=a}a=be(e),t.defaultValue=a,l=t.textContent,l===a&&l!==""&&l!==null&&(t.value=l)}function ll(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var hg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function fr(t,e,a){var l=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,a):typeof a!="number"||a===0||hg.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function dr(t,e,a){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var n in e)l=e[n],e.hasOwnProperty(n)&&a[n]!==l&&fr(t,n,l)}else for(var i in e)e.hasOwnProperty(i)&&fr(t,i,e[i])}function Eu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),mg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ti(t){return mg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Tu=null;function Au(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var nl=null,il=null;function hr(t){var e=Pa(t);if(e&&(t=e.stateNode)){var a=t[ne]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ou(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+xe(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var l=a[e];if(l!==t&&l.form===t.form){var n=l[ne]||null;if(!n)throw Error(s(90));Ou(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<a.length;e++)l=a[e],l.form===t.form&&sr(l)}break t;case"textarea":rr(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&al(t,!!a.multiple,e,!1)}}}var zu=!1;function gr(t,e,a){if(zu)return t(e,a);zu=!0;try{var l=t(e);return l}finally{if(zu=!1,(nl!==null||il!==null)&&(qi(),nl&&(e=nl,t=il,il=nl=null,hr(e),t)))for(e=0;e<t.length;e++)hr(t[e])}}function Zl(t,e){var a=t.stateNode;if(a===null)return null;var l=a[ne]||null;if(l===null)return null;a=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(s(231,e,typeof a));return a}var ke=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ru=!1;if(ke)try{var Kl={};Object.defineProperty(Kl,"passive",{get:function(){Ru=!0}}),window.addEventListener("test",Kl,Kl),window.removeEventListener("test",Kl,Kl)}catch{Ru=!1}var sa=null,Mu=null,ei=null;function mr(){if(ei)return ei;var t,e=Mu,a=e.length,l,n="value"in sa?sa.value:sa.textContent,i=n.length;for(t=0;t<a&&e[t]===n[t];t++);var f=a-t;for(l=1;l<=f&&e[a-l]===n[i-l];l++);return ei=n.slice(t,1<l?1-l:void 0)}function ai(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function li(){return!0}function pr(){return!1}function ie(t){function e(a,l,n,i,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=i,this.target=f,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(a=t[h],this[h]=a?a(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?li:pr,this.isPropagationStopped=pr,this}return O(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=li)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=li)},persist:function(){},isPersistent:li}),e}var ja={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ni=ie(ja),Jl=O({},ja,{view:0,detail:0}),pg=ie(Jl),wu,ju,$l,ii=O({},Jl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==$l&&($l&&t.type==="mousemove"?(wu=t.screenX-$l.screenX,ju=t.screenY-$l.screenY):ju=wu=0,$l=t),wu)},movementY:function(t){return"movementY"in t?t.movementY:ju}}),yr=ie(ii),yg=O({},ii,{dataTransfer:0}),vg=ie(yg),bg=O({},Jl,{relatedTarget:0}),Du=ie(bg),xg=O({},ja,{animationName:0,elapsedTime:0,pseudoElement:0}),Sg=ie(xg),_g=O({},ja,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Og=ie(_g),Ng=O({},ja,{data:0}),vr=ie(Ng),Eg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ag={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function zg(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Ag[t])?!!e[t]:!1}function Cu(){return zg}var Rg=O({},Jl,{key:function(t){if(t.key){var e=Eg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ai(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Tg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cu,charCode:function(t){return t.type==="keypress"?ai(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ai(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Mg=ie(Rg),wg=O({},ii,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),br=ie(wg),jg=O({},Jl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cu}),Dg=ie(jg),Cg=O({},ja,{propertyName:0,elapsedTime:0,pseudoElement:0}),Ug=ie(Cg),Lg=O({},ii,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Hg=ie(Lg),Bg=O({},ja,{newState:0,oldState:0}),qg=ie(Bg),kg=[9,13,27,32],Uu=ke&&"CompositionEvent"in window,Wl=null;ke&&"documentMode"in document&&(Wl=document.documentMode);var Gg=ke&&"TextEvent"in window&&!Wl,xr=ke&&(!Uu||Wl&&8<Wl&&11>=Wl),Sr=" ",_r=!1;function Or(t,e){switch(t){case"keyup":return kg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nr(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ul=!1;function Yg(t,e){switch(t){case"compositionend":return Nr(e);case"keypress":return e.which!==32?null:(_r=!0,Sr);case"textInput":return t=e.data,t===Sr&&_r?null:t;default:return null}}function Vg(t,e){if(ul)return t==="compositionend"||!Uu&&Or(t,e)?(t=mr(),ei=Mu=sa=null,ul=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return xr&&e.locale!=="ko"?null:e.data;default:return null}}var Xg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Er(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Xg[t.type]:e==="textarea"}function Tr(t,e,a,l){nl?il?il.push(l):il=[l]:nl=l,e=Qi(e,"onChange"),0<e.length&&(a=new ni("onChange","change",null,a,l),t.push({event:a,listeners:e}))}var Fl=null,Pl=null;function Qg(t){cd(t,0)}function ui(t){var e=Ql(t);if(sr(e))return t}function Ar(t,e){if(t==="change")return e}var zr=!1;if(ke){var Lu;if(ke){var Hu="oninput"in document;if(!Hu){var Rr=document.createElement("div");Rr.setAttribute("oninput","return;"),Hu=typeof Rr.oninput=="function"}Lu=Hu}else Lu=!1;zr=Lu&&(!document.documentMode||9<document.documentMode)}function Mr(){Fl&&(Fl.detachEvent("onpropertychange",wr),Pl=Fl=null)}function wr(t){if(t.propertyName==="value"&&ui(Pl)){var e=[];Tr(e,Pl,t,Au(t)),gr(Qg,e)}}function Zg(t,e,a){t==="focusin"?(Mr(),Fl=e,Pl=a,Fl.attachEvent("onpropertychange",wr)):t==="focusout"&&Mr()}function Kg(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ui(Pl)}function Jg(t,e){if(t==="click")return ui(e)}function $g(t,e){if(t==="input"||t==="change")return ui(e)}function Wg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var de=typeof Object.is=="function"?Object.is:Wg;function Il(t,e){if(de(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),l=Object.keys(e);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!na.call(e,n)||!de(t[n],e[n]))return!1}return!0}function jr(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Dr(t,e){var a=jr(t);t=0;for(var l;a;){if(a.nodeType===3){if(l=t+a.textContent.length,t<=e&&l>=e)return{node:a,offset:e-t};t=l}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=jr(a)}}function Cr(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Cr(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ur(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=In(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=In(t.document)}return e}function Bu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Fg=ke&&"documentMode"in document&&11>=document.documentMode,sl=null,qu=null,tn=null,ku=!1;function Lr(t,e,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;ku||sl==null||sl!==In(l)||(l=sl,"selectionStart"in l&&Bu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),tn&&Il(tn,l)||(tn=l,l=Qi(qu,"onSelect"),0<l.length&&(e=new ni("onSelect","select",null,e,a),t.push({event:e,listeners:l}),e.target=sl)))}function Da(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var cl={animationend:Da("Animation","AnimationEnd"),animationiteration:Da("Animation","AnimationIteration"),animationstart:Da("Animation","AnimationStart"),transitionrun:Da("Transition","TransitionRun"),transitionstart:Da("Transition","TransitionStart"),transitioncancel:Da("Transition","TransitionCancel"),transitionend:Da("Transition","TransitionEnd")},Gu={},Hr={};ke&&(Hr=document.createElement("div").style,"AnimationEvent"in window||(delete cl.animationend.animation,delete cl.animationiteration.animation,delete cl.animationstart.animation),"TransitionEvent"in window||delete cl.transitionend.transition);function Ca(t){if(Gu[t])return Gu[t];if(!cl[t])return t;var e=cl[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in Hr)return Gu[t]=e[a];return t}var Br=Ca("animationend"),qr=Ca("animationiteration"),kr=Ca("animationstart"),Pg=Ca("transitionrun"),Ig=Ca("transitionstart"),tm=Ca("transitioncancel"),Gr=Ca("transitionend"),Yr=new Map,Yu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Yu.push("scrollEnd");function ze(t,e){Yr.set(t,e),wa(e,[t])}var Vr=new WeakMap;function Se(t,e){if(typeof t=="object"&&t!==null){var a=Vr.get(t);return a!==void 0?a:(e={value:t,source:e,stack:ir(e)},Vr.set(t,e),e)}return{value:t,source:e,stack:ir(e)}}var _e=[],rl=0,Vu=0;function si(){for(var t=rl,e=Vu=rl=0;e<t;){var a=_e[e];_e[e++]=null;var l=_e[e];_e[e++]=null;var n=_e[e];_e[e++]=null;var i=_e[e];if(_e[e++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}i!==0&&Xr(a,n,i)}}function ci(t,e,a,l){_e[rl++]=t,_e[rl++]=e,_e[rl++]=a,_e[rl++]=l,Vu|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Xu(t,e,a,l){return ci(t,e,a,l),ri(t)}function ol(t,e){return ci(t,null,null,e),ri(t)}function Xr(t,e,a){t.lanes|=a;var l=t.alternate;l!==null&&(l.lanes|=a);for(var n=!1,i=t.return;i!==null;)i.childLanes|=a,l=i.alternate,l!==null&&(l.childLanes|=a),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-fe(a),t=i.hiddenUpdates,l=t[n],l===null?t[n]=[e]:l.push(e),e.lane=a|536870912),i):null}function ri(t){if(50<Tn)throw Tn=0,Ws=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var fl={};function em(t,e,a,l){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function he(t,e,a,l){return new em(t,e,a,l)}function Qu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ge(t,e){var a=t.alternate;return a===null?(a=he(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function Qr(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function oi(t,e,a,l,n,i){var f=0;if(l=t,typeof t=="function")Qu(t)&&(f=1);else if(typeof t=="string")f=lp(t,a,Y.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case et:return t=he(31,a,e,n),t.elementType=et,t.lanes=i,t;case U:return Ua(a.children,n,i,e);case Z:f=8,n|=24;break;case F:return t=he(12,a,e,n|2),t.elementType=F,t.lanes=i,t;case J:return t=he(13,a,e,n),t.elementType=J,t.lanes=i,t;case xt:return t=he(19,a,e,n),t.elementType=xt,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case bt:case st:f=10;break t;case ft:f=9;break t;case ot:f=11;break t;case mt:f=14;break t;case G:f=16,l=null;break t}f=29,a=Error(s(130,t===null?"null":typeof t,"")),l=null}return e=he(f,a,e,n),e.elementType=t,e.type=l,e.lanes=i,e}function Ua(t,e,a,l){return t=he(7,t,l,e),t.lanes=a,t}function Zu(t,e,a){return t=he(6,t,null,e),t.lanes=a,t}function Ku(t,e,a){return e=he(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var dl=[],hl=0,fi=null,di=0,Oe=[],Ne=0,La=null,Ye=1,Ve="";function Ha(t,e){dl[hl++]=di,dl[hl++]=fi,fi=t,di=e}function Zr(t,e,a){Oe[Ne++]=Ye,Oe[Ne++]=Ve,Oe[Ne++]=La,La=t;var l=Ye;t=Ve;var n=32-fe(l)-1;l&=~(1<<n),a+=1;var i=32-fe(e)+n;if(30<i){var f=n-n%5;i=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Ye=1<<32-fe(e)+n|a<<n|l,Ve=i+t}else Ye=1<<i|a<<n|l,Ve=t}function Ju(t){t.return!==null&&(Ha(t,1),Zr(t,1,0))}function $u(t){for(;t===fi;)fi=dl[--hl],dl[hl]=null,di=dl[--hl],dl[hl]=null;for(;t===La;)La=Oe[--Ne],Oe[Ne]=null,Ve=Oe[--Ne],Oe[Ne]=null,Ye=Oe[--Ne],Oe[Ne]=null}var le=null,Lt=null,vt=!1,Ba=null,De=!1,Wu=Error(s(519));function qa(t){var e=Error(s(418,""));throw ln(Se(e,t)),Wu}function Kr(t){var e=t.stateNode,a=t.type,l=t.memoizedProps;switch(e[te]=t,e[ne]=l,a){case"dialog":rt("cancel",e),rt("close",e);break;case"iframe":case"object":case"embed":rt("load",e);break;case"video":case"audio":for(a=0;a<zn.length;a++)rt(zn[a],e);break;case"source":rt("error",e);break;case"img":case"image":case"link":rt("error",e),rt("load",e);break;case"details":rt("toggle",e);break;case"input":rt("invalid",e),cr(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Pn(e);break;case"select":rt("invalid",e);break;case"textarea":rt("invalid",e),or(e,l.value,l.defaultValue,l.children),Pn(e)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||l.suppressHydrationWarning===!0||dd(e.textContent,a)?(l.popover!=null&&(rt("beforetoggle",e),rt("toggle",e)),l.onScroll!=null&&rt("scroll",e),l.onScrollEnd!=null&&rt("scrollend",e),l.onClick!=null&&(e.onclick=Zi),e=!0):e=!1,e||qa(t)}function Jr(t){for(le=t.return;le;)switch(le.tag){case 5:case 13:De=!1;return;case 27:case 3:De=!0;return;default:le=le.return}}function en(t){if(t!==le)return!1;if(!vt)return Jr(t),vt=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||dc(t.type,t.memoizedProps)),a=!a),a&&Lt&&qa(t),Jr(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){Lt=Me(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}Lt=null}}else e===27?(e=Lt,Oa(t.type)?(t=pc,pc=null,Lt=t):Lt=e):Lt=le?Me(t.stateNode.nextSibling):null;return!0}function an(){Lt=le=null,vt=!1}function $r(){var t=Ba;return t!==null&&(ce===null?ce=t:ce.push.apply(ce,t),Ba=null),t}function ln(t){Ba===null?Ba=[t]:Ba.push(t)}var Fu=w(null),ka=null,Xe=null;function ca(t,e,a){L(Fu,e._currentValue),e._currentValue=a}function Qe(t){t._currentValue=Fu.current,k(Fu)}function Pu(t,e,a){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===a)break;t=t.return}}function Iu(t,e,a,l){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var f=n.child;i=i.firstContext;t:for(;i!==null;){var h=i;i=n;for(var v=0;v<e.length;v++)if(h.context===e[v]){i.lanes|=a,h=i.alternate,h!==null&&(h.lanes|=a),Pu(i.return,a,t),l||(f=null);break t}i=h.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(s(341));f.lanes|=a,i=f.alternate,i!==null&&(i.lanes|=a),Pu(f,a,t),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===t){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function nn(t,e,a,l){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var h=n.type;de(n.pendingProps.value,f.value)||(t!==null?t.push(h):t=[h])}}else if(n===pt.current){if(f=n.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Cn):t=[Cn])}n=n.return}t!==null&&Iu(e,t,a,l),e.flags|=262144}function hi(t){for(t=t.firstContext;t!==null;){if(!de(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ga(t){ka=t,Xe=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ee(t){return Wr(ka,t)}function gi(t,e){return ka===null&&Ga(t),Wr(t,e)}function Wr(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},Xe===null){if(t===null)throw Error(s(308));Xe=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Xe=Xe.next=e;return a}var am=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},lm=r.unstable_scheduleCallback,nm=r.unstable_NormalPriority,Vt={$$typeof:st,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ts(){return{controller:new am,data:new Map,refCount:0}}function un(t){t.refCount--,t.refCount===0&&lm(nm,function(){t.controller.abort()})}var sn=null,es=0,gl=0,ml=null;function im(t,e){if(sn===null){var a=sn=[];es=0,gl=lc(),ml={status:"pending",value:void 0,then:function(l){a.push(l)}}}return es++,e.then(Fr,Fr),e}function Fr(){if(--es===0&&sn!==null){ml!==null&&(ml.status="fulfilled");var t=sn;sn=null,gl=0,ml=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function um(t,e){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var n=0;n<a.length;n++)(0,a[n])(e)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Pr=z.S;z.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&im(t,e),Pr!==null&&Pr(t,e)};var Ya=w(null);function as(){var t=Ya.current;return t!==null?t:Mt.pooledCache}function mi(t,e){e===null?L(Ya,Ya.current):L(Ya,e.pool)}function Ir(){var t=as();return t===null?null:{parent:Vt._currentValue,pool:t}}var cn=Error(s(460)),to=Error(s(474)),pi=Error(s(542)),ls={then:function(){}};function eo(t){return t=t.status,t==="fulfilled"||t==="rejected"}function yi(){}function ao(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(yi,yi),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,no(t),t;default:if(typeof e.status=="string")e.then(yi,yi);else{if(t=Mt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=l}},function(l){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,no(t),t}throw rn=e,cn}}var rn=null;function lo(){if(rn===null)throw Error(s(459));var t=rn;return rn=null,t}function no(t){if(t===cn||t===pi)throw Error(s(483))}var ra=!1;function ns(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function is(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function oa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function fa(t,e,a){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(St&2)!==0){var n=l.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),l.pending=e,e=ri(t),Xr(t,null,a),e}return ci(t,l,e,a),ri(t)}function on(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,Fc(t,a)}}function us(t,e){var a=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,i=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};i===null?n=i=f:i=i.next=f,a=a.next}while(a!==null);i===null?n=i=e:i=i.next=e}else n=i=e;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:l.shared,callbacks:l.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var ss=!1;function fn(){if(ss){var t=ml;if(t!==null)throw t}}function dn(t,e,a,l){ss=!1;var n=t.updateQueue;ra=!1;var i=n.firstBaseUpdate,f=n.lastBaseUpdate,h=n.shared.pending;if(h!==null){n.shared.pending=null;var v=h,E=v.next;v.next=null,f===null?i=E:f.next=E,f=v;var M=t.alternate;M!==null&&(M=M.updateQueue,h=M.lastBaseUpdate,h!==f&&(h===null?M.firstBaseUpdate=E:h.next=E,M.lastBaseUpdate=v))}if(i!==null){var D=n.baseState;f=0,M=E=v=null,h=i;do{var T=h.lane&-536870913,A=T!==h.lane;if(A?(gt&T)===T:(l&T)===T){T!==0&&T===gl&&(ss=!0),M!==null&&(M=M.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var P=t,$=h;T=e;var Tt=a;switch($.tag){case 1:if(P=$.payload,typeof P=="function"){D=P.call(Tt,D,T);break t}D=P;break t;case 3:P.flags=P.flags&-65537|128;case 0:if(P=$.payload,T=typeof P=="function"?P.call(Tt,D,T):P,T==null)break t;D=O({},D,T);break t;case 2:ra=!0}}T=h.callback,T!==null&&(t.flags|=64,A&&(t.flags|=8192),A=n.callbacks,A===null?n.callbacks=[T]:A.push(T))}else A={lane:T,tag:h.tag,payload:h.payload,callback:h.callback,next:null},M===null?(E=M=A,v=D):M=M.next=A,f|=T;if(h=h.next,h===null){if(h=n.shared.pending,h===null)break;A=h,h=A.next,A.next=null,n.lastBaseUpdate=A,n.shared.pending=null}}while(!0);M===null&&(v=D),n.baseState=v,n.firstBaseUpdate=E,n.lastBaseUpdate=M,i===null&&(n.shared.lanes=0),ba|=f,t.lanes=f,t.memoizedState=D}}function io(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function uo(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)io(a[t],e)}var pl=w(null),vi=w(0);function so(t,e){t=Pe,L(vi,t),L(pl,e),Pe=t|e.baseLanes}function cs(){L(vi,Pe),L(pl,pl.current)}function rs(){Pe=vi.current,k(pl),k(vi)}var da=0,nt=null,Nt=null,Gt=null,bi=!1,yl=!1,Va=!1,xi=0,hn=0,vl=null,sm=0;function qt(){throw Error(s(321))}function os(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!de(t[a],e[a]))return!1;return!0}function fs(t,e,a,l,n,i){return da=i,nt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,z.H=t===null||t.memoizedState===null?Qo:Zo,Va=!1,i=a(l,n),Va=!1,yl&&(i=ro(e,a,l,n)),co(t),i}function co(t){z.H=Ti;var e=Nt!==null&&Nt.next!==null;if(da=0,Gt=Nt=nt=null,bi=!1,hn=0,vl=null,e)throw Error(s(300));t===null||Kt||(t=t.dependencies,t!==null&&hi(t)&&(Kt=!0))}function ro(t,e,a,l){nt=t;var n=0;do{if(yl&&(vl=null),hn=0,yl=!1,25<=n)throw Error(s(301));if(n+=1,Gt=Nt=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}z.H=gm,i=e(a,l)}while(yl);return i}function cm(){var t=z.H,e=t.useState()[0];return e=typeof e.then=="function"?gn(e):e,t=t.useState()[0],(Nt!==null?Nt.memoizedState:null)!==t&&(nt.flags|=1024),e}function ds(){var t=xi!==0;return xi=0,t}function hs(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function gs(t){if(bi){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}bi=!1}da=0,Gt=Nt=nt=null,yl=!1,hn=xi=0,vl=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Gt===null?nt.memoizedState=Gt=t:Gt=Gt.next=t,Gt}function Yt(){if(Nt===null){var t=nt.alternate;t=t!==null?t.memoizedState:null}else t=Nt.next;var e=Gt===null?nt.memoizedState:Gt.next;if(e!==null)Gt=e,Nt=t;else{if(t===null)throw nt.alternate===null?Error(s(467)):Error(s(310));Nt=t,t={memoizedState:Nt.memoizedState,baseState:Nt.baseState,baseQueue:Nt.baseQueue,queue:Nt.queue,next:null},Gt===null?nt.memoizedState=Gt=t:Gt=Gt.next=t}return Gt}function ms(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function gn(t){var e=hn;return hn+=1,vl===null&&(vl=[]),t=ao(vl,t,e),e=nt,(Gt===null?e.memoizedState:Gt.next)===null&&(e=e.alternate,z.H=e===null||e.memoizedState===null?Qo:Zo),t}function Si(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return gn(t);if(t.$$typeof===st)return ee(t)}throw Error(s(438,String(t)))}function ps(t){var e=null,a=nt.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var l=nt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=ms(),nt.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),l=0;l<t;l++)a[l]=dt;return e.index++,a}function Ze(t,e){return typeof e=="function"?e(t):e}function _i(t){var e=Yt();return ys(e,Nt,t)}function ys(t,e,a){var l=t.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=a;var n=t.baseQueue,i=l.pending;if(i!==null){if(n!==null){var f=n.next;n.next=i.next,i.next=f}e.baseQueue=n=i,l.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var h=f=null,v=null,E=e,M=!1;do{var D=E.lane&-536870913;if(D!==E.lane?(gt&D)===D:(da&D)===D){var T=E.revertLane;if(T===0)v!==null&&(v=v.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),D===gl&&(M=!0);else if((da&T)===T){E=E.next,T===gl&&(M=!0);continue}else D={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},v===null?(h=v=D,f=i):v=v.next=D,nt.lanes|=T,ba|=T;D=E.action,Va&&a(i,D),i=E.hasEagerState?E.eagerState:a(i,D)}else T={lane:D,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},v===null?(h=v=T,f=i):v=v.next=T,nt.lanes|=D,ba|=D;E=E.next}while(E!==null&&E!==e);if(v===null?f=i:v.next=h,!de(i,t.memoizedState)&&(Kt=!0,M&&(a=ml,a!==null)))throw a;t.memoizedState=i,t.baseState=f,t.baseQueue=v,l.lastRenderedState=i}return n===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function vs(t){var e=Yt(),a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=t;var l=a.dispatch,n=a.pending,i=e.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do i=t(i,f.action),f=f.next;while(f!==n);de(i,e.memoizedState)||(Kt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),a.lastRenderedState=i}return[i,l]}function oo(t,e,a){var l=nt,n=Yt(),i=vt;if(i){if(a===void 0)throw Error(s(407));a=a()}else a=e();var f=!de((Nt||n).memoizedState,a);f&&(n.memoizedState=a,Kt=!0),n=n.queue;var h=go.bind(null,l,n,t);if(mn(2048,8,h,[t]),n.getSnapshot!==e||f||Gt!==null&&Gt.memoizedState.tag&1){if(l.flags|=2048,bl(9,Oi(),ho.bind(null,l,n,a,e),null),Mt===null)throw Error(s(349));i||(da&124)!==0||fo(l,e,a)}return a}function fo(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=nt.updateQueue,e===null?(e=ms(),nt.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function ho(t,e,a,l){e.value=a,e.getSnapshot=l,mo(e)&&po(t)}function go(t,e,a){return a(function(){mo(e)&&po(t)})}function mo(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!de(t,a)}catch{return!0}}function po(t){var e=ol(t,2);e!==null&&ve(e,t,2)}function bs(t){var e=ue();if(typeof t=="function"){var a=t;if(t=a(),Va){ia(!0);try{a()}finally{ia(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:t},e}function yo(t,e,a,l){return t.baseState=a,ys(t,Nt,typeof l=="function"?l:Ze)}function rm(t,e,a,l,n){if(Ei(t))throw Error(s(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){i.listeners.push(f)}};z.T!==null?a(!0):i.isTransition=!1,l(i),a=e.pending,a===null?(i.next=e.pending=i,vo(e,i)):(i.next=a.next,e.pending=a.next=i)}}function vo(t,e){var a=e.action,l=e.payload,n=t.state;if(e.isTransition){var i=z.T,f={};z.T=f;try{var h=a(n,l),v=z.S;v!==null&&v(f,h),bo(t,e,h)}catch(E){xs(t,e,E)}finally{z.T=i}}else try{i=a(n,l),bo(t,e,i)}catch(E){xs(t,e,E)}}function bo(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){xo(t,e,l)},function(l){return xs(t,e,l)}):xo(t,e,a)}function xo(t,e,a){e.status="fulfilled",e.value=a,So(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,vo(t,a)))}function xs(t,e,a){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=a,So(e),e=e.next;while(e!==l)}t.action=null}function So(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function _o(t,e){return e}function Oo(t,e){if(vt){var a=Mt.formState;if(a!==null){t:{var l=nt;if(vt){if(Lt){e:{for(var n=Lt,i=De;n.nodeType!==8;){if(!i){n=null;break e}if(n=Me(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Lt=Me(n.nextSibling),l=n.data==="F!";break t}}qa(l)}l=!1}l&&(e=a[0])}}return a=ue(),a.memoizedState=a.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:_o,lastRenderedState:e},a.queue=l,a=Yo.bind(null,nt,l),l.dispatch=a,l=bs(!1),i=Es.bind(null,nt,!1,l.queue),l=ue(),n={state:e,dispatch:null,action:t,pending:null},l.queue=n,a=rm.bind(null,nt,n,i,a),n.dispatch=a,l.memoizedState=t,[e,a,!1]}function No(t){var e=Yt();return Eo(e,Nt,t)}function Eo(t,e,a){if(e=ys(t,e,_o)[0],t=_i(Ze)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=gn(e)}catch(f){throw f===cn?pi:f}else l=e;e=Yt();var n=e.queue,i=n.dispatch;return a!==e.memoizedState&&(nt.flags|=2048,bl(9,Oi(),om.bind(null,n,a),null)),[l,i,t]}function om(t,e){t.action=e}function To(t){var e=Yt(),a=Nt;if(a!==null)return Eo(e,a,t);Yt(),e=e.memoizedState,a=Yt();var l=a.queue.dispatch;return a.memoizedState=t,[e,l,!1]}function bl(t,e,a,l){return t={tag:t,create:a,deps:l,inst:e,next:null},e=nt.updateQueue,e===null&&(e=ms(),nt.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(l=a.next,a.next=t,t.next=l,e.lastEffect=t),t}function Oi(){return{destroy:void 0,resource:void 0}}function Ao(){return Yt().memoizedState}function Ni(t,e,a,l){var n=ue();l=l===void 0?null:l,nt.flags|=t,n.memoizedState=bl(1|e,Oi(),a,l)}function mn(t,e,a,l){var n=Yt();l=l===void 0?null:l;var i=n.memoizedState.inst;Nt!==null&&l!==null&&os(l,Nt.memoizedState.deps)?n.memoizedState=bl(e,i,a,l):(nt.flags|=t,n.memoizedState=bl(1|e,i,a,l))}function zo(t,e){Ni(8390656,8,t,e)}function Ro(t,e){mn(2048,8,t,e)}function Mo(t,e){return mn(4,2,t,e)}function wo(t,e){return mn(4,4,t,e)}function jo(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Do(t,e,a){a=a!=null?a.concat([t]):null,mn(4,4,jo.bind(null,e,t),a)}function Ss(){}function Co(t,e){var a=Yt();e=e===void 0?null:e;var l=a.memoizedState;return e!==null&&os(e,l[1])?l[0]:(a.memoizedState=[t,e],t)}function Uo(t,e){var a=Yt();e=e===void 0?null:e;var l=a.memoizedState;if(e!==null&&os(e,l[1]))return l[0];if(l=t(),Va){ia(!0);try{t()}finally{ia(!1)}}return a.memoizedState=[l,e],l}function _s(t,e,a){return a===void 0||(da&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=qf(),nt.lanes|=t,ba|=t,a)}function Lo(t,e,a,l){return de(a,e)?a:pl.current!==null?(t=_s(t,a,l),de(t,e)||(Kt=!0),t):(da&42)===0?(Kt=!0,t.memoizedState=a):(t=qf(),nt.lanes|=t,ba|=t,e)}function Ho(t,e,a,l,n){var i=H.p;H.p=i!==0&&8>i?i:8;var f=z.T,h={};z.T=h,Es(t,!1,e,a);try{var v=n(),E=z.S;if(E!==null&&E(h,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var M=um(v,l);pn(t,e,M,ye(t))}else pn(t,e,l,ye(t))}catch(D){pn(t,e,{then:function(){},status:"rejected",reason:D},ye())}finally{H.p=i,z.T=f}}function fm(){}function Os(t,e,a,l){if(t.tag!==5)throw Error(s(476));var n=Bo(t).queue;Ho(t,n,e,C,a===null?fm:function(){return qo(t),a(l)})}function Bo(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:C,baseState:C,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:C},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ze,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function qo(t){var e=Bo(t).next.queue;pn(t,e,{},ye())}function Ns(){return ee(Cn)}function ko(){return Yt().memoizedState}function Go(){return Yt().memoizedState}function dm(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=ye();t=oa(a);var l=fa(e,t,a);l!==null&&(ve(l,e,a),on(l,e,a)),e={cache:ts()},t.payload=e;return}e=e.return}}function hm(t,e,a){var l=ye();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Ei(t)?Vo(e,a):(a=Xu(t,e,a,l),a!==null&&(ve(a,t,l),Xo(a,e,l)))}function Yo(t,e,a){var l=ye();pn(t,e,a,l)}function pn(t,e,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Ei(t))Vo(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var f=e.lastRenderedState,h=i(f,a);if(n.hasEagerState=!0,n.eagerState=h,de(h,f))return ci(t,e,n,0),Mt===null&&si(),!1}catch{}finally{}if(a=Xu(t,e,n,l),a!==null)return ve(a,t,l),Xo(a,e,l),!0}return!1}function Es(t,e,a,l){if(l={lane:2,revertLane:lc(),action:l,hasEagerState:!1,eagerState:null,next:null},Ei(t)){if(e)throw Error(s(479))}else e=Xu(t,a,l,2),e!==null&&ve(e,t,2)}function Ei(t){var e=t.alternate;return t===nt||e!==null&&e===nt}function Vo(t,e){yl=bi=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function Xo(t,e,a){if((a&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,Fc(t,a)}}var Ti={readContext:ee,use:Si,useCallback:qt,useContext:qt,useEffect:qt,useImperativeHandle:qt,useLayoutEffect:qt,useInsertionEffect:qt,useMemo:qt,useReducer:qt,useRef:qt,useState:qt,useDebugValue:qt,useDeferredValue:qt,useTransition:qt,useSyncExternalStore:qt,useId:qt,useHostTransitionStatus:qt,useFormState:qt,useActionState:qt,useOptimistic:qt,useMemoCache:qt,useCacheRefresh:qt},Qo={readContext:ee,use:Si,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:ee,useEffect:zo,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,Ni(4194308,4,jo.bind(null,e,t),a)},useLayoutEffect:function(t,e){return Ni(4194308,4,t,e)},useInsertionEffect:function(t,e){Ni(4,2,t,e)},useMemo:function(t,e){var a=ue();e=e===void 0?null:e;var l=t();if(Va){ia(!0);try{t()}finally{ia(!1)}}return a.memoizedState=[l,e],l},useReducer:function(t,e,a){var l=ue();if(a!==void 0){var n=a(e);if(Va){ia(!0);try{a(e)}finally{ia(!1)}}}else n=e;return l.memoizedState=l.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},l.queue=t,t=t.dispatch=hm.bind(null,nt,t),[l.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=bs(t);var e=t.queue,a=Yo.bind(null,nt,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:Ss,useDeferredValue:function(t,e){var a=ue();return _s(a,t,e)},useTransition:function(){var t=bs(!1);return t=Ho.bind(null,nt,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var l=nt,n=ue();if(vt){if(a===void 0)throw Error(s(407));a=a()}else{if(a=e(),Mt===null)throw Error(s(349));(gt&124)!==0||fo(l,e,a)}n.memoizedState=a;var i={value:a,getSnapshot:e};return n.queue=i,zo(go.bind(null,l,i,t),[t]),l.flags|=2048,bl(9,Oi(),ho.bind(null,l,i,a,e),null),a},useId:function(){var t=ue(),e=Mt.identifierPrefix;if(vt){var a=Ve,l=Ye;a=(l&~(1<<32-fe(l)-1)).toString(32)+a,e="«"+e+"R"+a,a=xi++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=sm++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ns,useFormState:Oo,useActionState:Oo,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=Es.bind(null,nt,!0,a),a.dispatch=e,[t,e]},useMemoCache:ps,useCacheRefresh:function(){return ue().memoizedState=dm.bind(null,nt)}},Zo={readContext:ee,use:Si,useCallback:Co,useContext:ee,useEffect:Ro,useImperativeHandle:Do,useInsertionEffect:Mo,useLayoutEffect:wo,useMemo:Uo,useReducer:_i,useRef:Ao,useState:function(){return _i(Ze)},useDebugValue:Ss,useDeferredValue:function(t,e){var a=Yt();return Lo(a,Nt.memoizedState,t,e)},useTransition:function(){var t=_i(Ze)[0],e=Yt().memoizedState;return[typeof t=="boolean"?t:gn(t),e]},useSyncExternalStore:oo,useId:ko,useHostTransitionStatus:Ns,useFormState:No,useActionState:No,useOptimistic:function(t,e){var a=Yt();return yo(a,Nt,t,e)},useMemoCache:ps,useCacheRefresh:Go},gm={readContext:ee,use:Si,useCallback:Co,useContext:ee,useEffect:Ro,useImperativeHandle:Do,useInsertionEffect:Mo,useLayoutEffect:wo,useMemo:Uo,useReducer:vs,useRef:Ao,useState:function(){return vs(Ze)},useDebugValue:Ss,useDeferredValue:function(t,e){var a=Yt();return Nt===null?_s(a,t,e):Lo(a,Nt.memoizedState,t,e)},useTransition:function(){var t=vs(Ze)[0],e=Yt().memoizedState;return[typeof t=="boolean"?t:gn(t),e]},useSyncExternalStore:oo,useId:ko,useHostTransitionStatus:Ns,useFormState:To,useActionState:To,useOptimistic:function(t,e){var a=Yt();return Nt!==null?yo(a,Nt,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:ps,useCacheRefresh:Go},xl=null,yn=0;function Ai(t){var e=yn;return yn+=1,xl===null&&(xl=[]),ao(xl,t,e)}function vn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function zi(t,e){throw e.$$typeof===R?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Ko(t){var e=t._init;return e(t._payload)}function Jo(t){function e(_,x){if(t){var N=_.deletions;N===null?(_.deletions=[x],_.flags|=16):N.push(x)}}function a(_,x){if(!t)return null;for(;x!==null;)e(_,x),x=x.sibling;return null}function l(_){for(var x=new Map;_!==null;)_.key!==null?x.set(_.key,_):x.set(_.index,_),_=_.sibling;return x}function n(_,x){return _=Ge(_,x),_.index=0,_.sibling=null,_}function i(_,x,N){return _.index=N,t?(N=_.alternate,N!==null?(N=N.index,N<x?(_.flags|=67108866,x):N):(_.flags|=67108866,x)):(_.flags|=1048576,x)}function f(_){return t&&_.alternate===null&&(_.flags|=67108866),_}function h(_,x,N,j){return x===null||x.tag!==6?(x=Zu(N,_.mode,j),x.return=_,x):(x=n(x,N),x.return=_,x)}function v(_,x,N,j){var V=N.type;return V===U?M(_,x,N.props.children,j,N.key):x!==null&&(x.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===G&&Ko(V)===x.type)?(x=n(x,N.props),vn(x,N),x.return=_,x):(x=oi(N.type,N.key,N.props,null,_.mode,j),vn(x,N),x.return=_,x)}function E(_,x,N,j){return x===null||x.tag!==4||x.stateNode.containerInfo!==N.containerInfo||x.stateNode.implementation!==N.implementation?(x=Ku(N,_.mode,j),x.return=_,x):(x=n(x,N.children||[]),x.return=_,x)}function M(_,x,N,j,V){return x===null||x.tag!==7?(x=Ua(N,_.mode,j,V),x.return=_,x):(x=n(x,N),x.return=_,x)}function D(_,x,N){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=Zu(""+x,_.mode,N),x.return=_,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case B:return N=oi(x.type,x.key,x.props,null,_.mode,N),vn(N,x),N.return=_,N;case q:return x=Ku(x,_.mode,N),x.return=_,x;case G:var j=x._init;return x=j(x._payload),D(_,x,N)}if(Rt(x)||Dt(x))return x=Ua(x,_.mode,N,null),x.return=_,x;if(typeof x.then=="function")return D(_,Ai(x),N);if(x.$$typeof===st)return D(_,gi(_,x),N);zi(_,x)}return null}function T(_,x,N,j){var V=x!==null?x.key:null;if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return V!==null?null:h(_,x,""+N,j);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case B:return N.key===V?v(_,x,N,j):null;case q:return N.key===V?E(_,x,N,j):null;case G:return V=N._init,N=V(N._payload),T(_,x,N,j)}if(Rt(N)||Dt(N))return V!==null?null:M(_,x,N,j,null);if(typeof N.then=="function")return T(_,x,Ai(N),j);if(N.$$typeof===st)return T(_,x,gi(_,N),j);zi(_,N)}return null}function A(_,x,N,j,V){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return _=_.get(N)||null,h(x,_,""+j,V);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case B:return _=_.get(j.key===null?N:j.key)||null,v(x,_,j,V);case q:return _=_.get(j.key===null?N:j.key)||null,E(x,_,j,V);case G:var ut=j._init;return j=ut(j._payload),A(_,x,N,j,V)}if(Rt(j)||Dt(j))return _=_.get(N)||null,M(x,_,j,V,null);if(typeof j.then=="function")return A(_,x,N,Ai(j),V);if(j.$$typeof===st)return A(_,x,N,gi(x,j),V);zi(x,j)}return null}function P(_,x,N,j){for(var V=null,ut=null,K=x,W=x=0,$t=null;K!==null&&W<N.length;W++){K.index>W?($t=K,K=null):$t=K.sibling;var yt=T(_,K,N[W],j);if(yt===null){K===null&&(K=$t);break}t&&K&&yt.alternate===null&&e(_,K),x=i(yt,x,W),ut===null?V=yt:ut.sibling=yt,ut=yt,K=$t}if(W===N.length)return a(_,K),vt&&Ha(_,W),V;if(K===null){for(;W<N.length;W++)K=D(_,N[W],j),K!==null&&(x=i(K,x,W),ut===null?V=K:ut.sibling=K,ut=K);return vt&&Ha(_,W),V}for(K=l(K);W<N.length;W++)$t=A(K,_,W,N[W],j),$t!==null&&(t&&$t.alternate!==null&&K.delete($t.key===null?W:$t.key),x=i($t,x,W),ut===null?V=$t:ut.sibling=$t,ut=$t);return t&&K.forEach(function(za){return e(_,za)}),vt&&Ha(_,W),V}function $(_,x,N,j){if(N==null)throw Error(s(151));for(var V=null,ut=null,K=x,W=x=0,$t=null,yt=N.next();K!==null&&!yt.done;W++,yt=N.next()){K.index>W?($t=K,K=null):$t=K.sibling;var za=T(_,K,yt.value,j);if(za===null){K===null&&(K=$t);break}t&&K&&za.alternate===null&&e(_,K),x=i(za,x,W),ut===null?V=za:ut.sibling=za,ut=za,K=$t}if(yt.done)return a(_,K),vt&&Ha(_,W),V;if(K===null){for(;!yt.done;W++,yt=N.next())yt=D(_,yt.value,j),yt!==null&&(x=i(yt,x,W),ut===null?V=yt:ut.sibling=yt,ut=yt);return vt&&Ha(_,W),V}for(K=l(K);!yt.done;W++,yt=N.next())yt=A(K,_,W,yt.value,j),yt!==null&&(t&&yt.alternate!==null&&K.delete(yt.key===null?W:yt.key),x=i(yt,x,W),ut===null?V=yt:ut.sibling=yt,ut=yt);return t&&K.forEach(function(mp){return e(_,mp)}),vt&&Ha(_,W),V}function Tt(_,x,N,j){if(typeof N=="object"&&N!==null&&N.type===U&&N.key===null&&(N=N.props.children),typeof N=="object"&&N!==null){switch(N.$$typeof){case B:t:{for(var V=N.key;x!==null;){if(x.key===V){if(V=N.type,V===U){if(x.tag===7){a(_,x.sibling),j=n(x,N.props.children),j.return=_,_=j;break t}}else if(x.elementType===V||typeof V=="object"&&V!==null&&V.$$typeof===G&&Ko(V)===x.type){a(_,x.sibling),j=n(x,N.props),vn(j,N),j.return=_,_=j;break t}a(_,x);break}else e(_,x);x=x.sibling}N.type===U?(j=Ua(N.props.children,_.mode,j,N.key),j.return=_,_=j):(j=oi(N.type,N.key,N.props,null,_.mode,j),vn(j,N),j.return=_,_=j)}return f(_);case q:t:{for(V=N.key;x!==null;){if(x.key===V)if(x.tag===4&&x.stateNode.containerInfo===N.containerInfo&&x.stateNode.implementation===N.implementation){a(_,x.sibling),j=n(x,N.children||[]),j.return=_,_=j;break t}else{a(_,x);break}else e(_,x);x=x.sibling}j=Ku(N,_.mode,j),j.return=_,_=j}return f(_);case G:return V=N._init,N=V(N._payload),Tt(_,x,N,j)}if(Rt(N))return P(_,x,N,j);if(Dt(N)){if(V=Dt(N),typeof V!="function")throw Error(s(150));return N=V.call(N),$(_,x,N,j)}if(typeof N.then=="function")return Tt(_,x,Ai(N),j);if(N.$$typeof===st)return Tt(_,x,gi(_,N),j);zi(_,N)}return typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint"?(N=""+N,x!==null&&x.tag===6?(a(_,x.sibling),j=n(x,N),j.return=_,_=j):(a(_,x),j=Zu(N,_.mode,j),j.return=_,_=j),f(_)):a(_,x)}return function(_,x,N,j){try{yn=0;var V=Tt(_,x,N,j);return xl=null,V}catch(K){if(K===cn||K===pi)throw K;var ut=he(29,K,null,_.mode);return ut.lanes=j,ut.return=_,ut}finally{}}}var Sl=Jo(!0),$o=Jo(!1),Ee=w(null),Ce=null;function ha(t){var e=t.alternate;L(Xt,Xt.current&1),L(Ee,t),Ce===null&&(e===null||pl.current!==null||e.memoizedState!==null)&&(Ce=t)}function Wo(t){if(t.tag===22){if(L(Xt,Xt.current),L(Ee,t),Ce===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ce=t)}}else ga()}function ga(){L(Xt,Xt.current),L(Ee,Ee.current)}function Ke(t){k(Ee),Ce===t&&(Ce=null),k(Xt)}var Xt=w(0);function Ri(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||mc(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ts(t,e,a,l){e=t.memoizedState,a=a(l,e),a=a==null?e:O({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var As={enqueueSetState:function(t,e,a){t=t._reactInternals;var l=ye(),n=oa(l);n.payload=e,a!=null&&(n.callback=a),e=fa(t,n,l),e!==null&&(ve(e,t,l),on(e,t,l))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var l=ye(),n=oa(l);n.tag=1,n.payload=e,a!=null&&(n.callback=a),e=fa(t,n,l),e!==null&&(ve(e,t,l),on(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=ye(),l=oa(a);l.tag=2,e!=null&&(l.callback=e),e=fa(t,l,a),e!==null&&(ve(e,t,a),on(e,t,a))}};function Fo(t,e,a,l,n,i,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,i,f):e.prototype&&e.prototype.isPureReactComponent?!Il(a,l)||!Il(n,i):!0}function Po(t,e,a,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,l),e.state!==t&&As.enqueueReplaceState(e,e.state,null)}function Xa(t,e){var a=e;if("ref"in e){a={};for(var l in e)l!=="ref"&&(a[l]=e[l])}if(t=t.defaultProps){a===e&&(a=O({},a));for(var n in t)a[n]===void 0&&(a[n]=t[n])}return a}var Mi=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Io(t){Mi(t)}function tf(t){console.error(t)}function ef(t){Mi(t)}function wi(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function af(t,e,a){try{var l=t.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function zs(t,e,a){return a=oa(a),a.tag=3,a.payload={element:null},a.callback=function(){wi(t,e)},a}function lf(t){return t=oa(t),t.tag=3,t}function nf(t,e,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var i=l.value;t.payload=function(){return n(i)},t.callback=function(){af(e,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){af(e,a,l),typeof n!="function"&&(xa===null?xa=new Set([this]):xa.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function mm(t,e,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=a.alternate,e!==null&&nn(e,a,n,!0),a=Ee.current,a!==null){switch(a.tag){case 13:return Ce===null?Ps():a.alternate===null&&Ht===0&&(Ht=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===ls?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([l]):e.add(l),tc(t,l,n)),!1;case 22:return a.flags|=65536,l===ls?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([l]):a.add(l)),tc(t,l,n)),!1}throw Error(s(435,a.tag))}return tc(t,l,n),Ps(),!1}if(vt)return e=Ee.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,l!==Wu&&(t=Error(s(422),{cause:l}),ln(Se(t,a)))):(l!==Wu&&(e=Error(s(423),{cause:l}),ln(Se(e,a))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,l=Se(l,a),n=zs(t.stateNode,l,n),us(t,n),Ht!==4&&(Ht=2)),!1;var i=Error(s(520),{cause:l});if(i=Se(i,a),En===null?En=[i]:En.push(i),Ht!==4&&(Ht=2),e===null)return!0;l=Se(l,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=n&-n,a.lanes|=t,t=zs(a.stateNode,l,t),us(a,t),!1;case 1:if(e=a.type,i=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(xa===null||!xa.has(i))))return a.flags|=65536,n&=-n,a.lanes|=n,n=lf(n),nf(n,t,a,l),us(a,n),!1}a=a.return}while(a!==null);return!1}var uf=Error(s(461)),Kt=!1;function Wt(t,e,a,l){e.child=t===null?$o(e,null,a,l):Sl(e,t.child,a,l)}function sf(t,e,a,l,n){a=a.render;var i=e.ref;if("ref"in l){var f={};for(var h in l)h!=="ref"&&(f[h]=l[h])}else f=l;return Ga(e),l=fs(t,e,a,f,i,n),h=ds(),t!==null&&!Kt?(hs(t,e,n),Je(t,e,n)):(vt&&h&&Ju(e),e.flags|=1,Wt(t,e,l,n),e.child)}function cf(t,e,a,l,n){if(t===null){var i=a.type;return typeof i=="function"&&!Qu(i)&&i.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=i,rf(t,e,i,l,n)):(t=oi(a.type,null,l,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Ls(t,n)){var f=i.memoizedProps;if(a=a.compare,a=a!==null?a:Il,a(f,l)&&t.ref===e.ref)return Je(t,e,n)}return e.flags|=1,t=Ge(i,l),t.ref=e.ref,t.return=e,e.child=t}function rf(t,e,a,l,n){if(t!==null){var i=t.memoizedProps;if(Il(i,l)&&t.ref===e.ref)if(Kt=!1,e.pendingProps=l=i,Ls(t,n))(t.flags&131072)!==0&&(Kt=!0);else return e.lanes=t.lanes,Je(t,e,n)}return Rs(t,e,a,l,n)}function of(t,e,a){var l=e.pendingProps,n=l.children,i=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=i!==null?i.baseLanes|a:a,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~l}else e.childLanes=0,e.child=null;return ff(t,e,l,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&mi(e,i!==null?i.cachePool:null),i!==null?so(e,i):cs(),Wo(e);else return e.lanes=e.childLanes=536870912,ff(t,e,i!==null?i.baseLanes|a:a,a)}else i!==null?(mi(e,i.cachePool),so(e,i),ga(),e.memoizedState=null):(t!==null&&mi(e,null),cs(),ga());return Wt(t,e,n,a),e.child}function ff(t,e,a,l){var n=as();return n=n===null?null:{parent:Vt._currentValue,pool:n},e.memoizedState={baseLanes:a,cachePool:n},t!==null&&mi(e,null),cs(),Wo(e),t!==null&&nn(t,e,l,!0),null}function ji(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(s(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function Rs(t,e,a,l,n){return Ga(e),a=fs(t,e,a,l,void 0,n),l=ds(),t!==null&&!Kt?(hs(t,e,n),Je(t,e,n)):(vt&&l&&Ju(e),e.flags|=1,Wt(t,e,a,n),e.child)}function df(t,e,a,l,n,i){return Ga(e),e.updateQueue=null,a=ro(e,l,a,n),co(t),l=ds(),t!==null&&!Kt?(hs(t,e,i),Je(t,e,i)):(vt&&l&&Ju(e),e.flags|=1,Wt(t,e,a,i),e.child)}function hf(t,e,a,l,n){if(Ga(e),e.stateNode===null){var i=fl,f=a.contextType;typeof f=="object"&&f!==null&&(i=ee(f)),i=new a(l,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=As,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=l,i.state=e.memoizedState,i.refs={},ns(e),f=a.contextType,i.context=typeof f=="object"&&f!==null?ee(f):fl,i.state=e.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(Ts(e,a,f,l),i.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(f=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),f!==i.state&&As.enqueueReplaceState(i,i.state,null),dn(e,l,i,n),fn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){i=e.stateNode;var h=e.memoizedProps,v=Xa(a,h);i.props=v;var E=i.context,M=a.contextType;f=fl,typeof M=="object"&&M!==null&&(f=ee(M));var D=a.getDerivedStateFromProps;M=typeof D=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,M||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||E!==f)&&Po(e,i,l,f),ra=!1;var T=e.memoizedState;i.state=T,dn(e,l,i,n),fn(),E=e.memoizedState,h||T!==E||ra?(typeof D=="function"&&(Ts(e,a,D,l),E=e.memoizedState),(v=ra||Fo(e,a,v,l,T,E,f))?(M||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=E),i.props=l,i.state=E,i.context=f,l=v):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{i=e.stateNode,is(t,e),f=e.memoizedProps,M=Xa(a,f),i.props=M,D=e.pendingProps,T=i.context,E=a.contextType,v=fl,typeof E=="object"&&E!==null&&(v=ee(E)),h=a.getDerivedStateFromProps,(E=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(f!==D||T!==v)&&Po(e,i,l,v),ra=!1,T=e.memoizedState,i.state=T,dn(e,l,i,n),fn();var A=e.memoizedState;f!==D||T!==A||ra||t!==null&&t.dependencies!==null&&hi(t.dependencies)?(typeof h=="function"&&(Ts(e,a,h,l),A=e.memoizedState),(M=ra||Fo(e,a,M,l,T,A,v)||t!==null&&t.dependencies!==null&&hi(t.dependencies))?(E||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(l,A,v),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(l,A,v)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=A),i.props=l,i.state=A,i.context=v,l=M):(typeof i.componentDidUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),l=!1)}return i=l,ji(t,e),l=(e.flags&128)!==0,i||l?(i=e.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&l?(e.child=Sl(e,t.child,null,n),e.child=Sl(e,null,a,n)):Wt(t,e,a,n),e.memoizedState=i.state,t=e.child):t=Je(t,e,n),t}function gf(t,e,a,l){return an(),e.flags|=256,Wt(t,e,a,l),e.child}var Ms={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ws(t){return{baseLanes:t,cachePool:Ir()}}function js(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=Te),t}function mf(t,e,a){var l=e.pendingProps,n=!1,i=(e.flags&128)!==0,f;if((f=i)||(f=t!==null&&t.memoizedState===null?!1:(Xt.current&2)!==0),f&&(n=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(vt){if(n?ha(e):ga(),vt){var h=Lt,v;if(v=h){t:{for(v=h,h=De;v.nodeType!==8;){if(!h){h=null;break t}if(v=Me(v.nextSibling),v===null){h=null;break t}}h=v}h!==null?(e.memoizedState={dehydrated:h,treeContext:La!==null?{id:Ye,overflow:Ve}:null,retryLane:536870912,hydrationErrors:null},v=he(18,null,null,0),v.stateNode=h,v.return=e,e.child=v,le=e,Lt=null,v=!0):v=!1}v||qa(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return mc(h)?e.lanes=32:e.lanes=536870912,null;Ke(e)}return h=l.children,l=l.fallback,n?(ga(),n=e.mode,h=Di({mode:"hidden",children:h},n),l=Ua(l,n,a,null),h.return=e,l.return=e,h.sibling=l,e.child=h,n=e.child,n.memoizedState=ws(a),n.childLanes=js(t,f,a),e.memoizedState=Ms,l):(ha(e),Ds(e,h))}if(v=t.memoizedState,v!==null&&(h=v.dehydrated,h!==null)){if(i)e.flags&256?(ha(e),e.flags&=-257,e=Cs(t,e,a)):e.memoizedState!==null?(ga(),e.child=t.child,e.flags|=128,e=null):(ga(),n=l.fallback,h=e.mode,l=Di({mode:"visible",children:l.children},h),n=Ua(n,h,a,null),n.flags|=2,l.return=e,n.return=e,l.sibling=n,e.child=l,Sl(e,t.child,null,a),l=e.child,l.memoizedState=ws(a),l.childLanes=js(t,f,a),e.memoizedState=Ms,e=n);else if(ha(e),mc(h)){if(f=h.nextSibling&&h.nextSibling.dataset,f)var E=f.dgst;f=E,l=Error(s(419)),l.stack="",l.digest=f,ln({value:l,source:null,stack:null}),e=Cs(t,e,a)}else if(Kt||nn(t,e,a,!1),f=(a&t.childLanes)!==0,Kt||f){if(f=Mt,f!==null&&(l=a&-a,l=(l&42)!==0?1:pu(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==v.retryLane))throw v.retryLane=l,ol(t,l),ve(f,t,l),uf;h.data==="$?"||Ps(),e=Cs(t,e,a)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=v.treeContext,Lt=Me(h.nextSibling),le=e,vt=!0,Ba=null,De=!1,t!==null&&(Oe[Ne++]=Ye,Oe[Ne++]=Ve,Oe[Ne++]=La,Ye=t.id,Ve=t.overflow,La=e),e=Ds(e,l.children),e.flags|=4096);return e}return n?(ga(),n=l.fallback,h=e.mode,v=t.child,E=v.sibling,l=Ge(v,{mode:"hidden",children:l.children}),l.subtreeFlags=v.subtreeFlags&65011712,E!==null?n=Ge(E,n):(n=Ua(n,h,a,null),n.flags|=2),n.return=e,l.return=e,l.sibling=n,e.child=l,l=n,n=e.child,h=t.child.memoizedState,h===null?h=ws(a):(v=h.cachePool,v!==null?(E=Vt._currentValue,v=v.parent!==E?{parent:E,pool:E}:v):v=Ir(),h={baseLanes:h.baseLanes|a,cachePool:v}),n.memoizedState=h,n.childLanes=js(t,f,a),e.memoizedState=Ms,l):(ha(e),a=t.child,t=a.sibling,a=Ge(a,{mode:"visible",children:l.children}),a.return=e,a.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=a,e.memoizedState=null,a)}function Ds(t,e){return e=Di({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Di(t,e){return t=he(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Cs(t,e,a){return Sl(e,t.child,null,a),t=Ds(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function pf(t,e,a){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Pu(t.return,e,a)}function Us(t,e,a,l,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=l,i.tail=a,i.tailMode=n)}function yf(t,e,a){var l=e.pendingProps,n=l.revealOrder,i=l.tail;if(Wt(t,e,l.children,a),l=Xt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&pf(t,a,e);else if(t.tag===19)pf(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(L(Xt,l),n){case"forwards":for(a=e.child,n=null;a!==null;)t=a.alternate,t!==null&&Ri(t)===null&&(n=a),a=a.sibling;a=n,a===null?(n=e.child,e.child=null):(n=a.sibling,a.sibling=null),Us(e,!1,n,a,i);break;case"backwards":for(a=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Ri(t)===null){e.child=n;break}t=n.sibling,n.sibling=a,a=n,n=t}Us(e,!0,a,null,i);break;case"together":Us(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Je(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),ba|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(nn(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,a=Ge(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=Ge(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function Ls(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&hi(t)))}function pm(t,e,a){switch(e.tag){case 3:At(e,e.stateNode.containerInfo),ca(e,Vt,t.memoizedState.cache),an();break;case 27:case 5:aa(e);break;case 4:At(e,e.stateNode.containerInfo);break;case 10:ca(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(ha(e),e.flags|=128,null):(a&e.child.childLanes)!==0?mf(t,e,a):(ha(e),t=Je(t,e,a),t!==null?t.sibling:null);ha(e);break;case 19:var n=(t.flags&128)!==0;if(l=(a&e.childLanes)!==0,l||(nn(t,e,a,!1),l=(a&e.childLanes)!==0),n){if(l)return yf(t,e,a);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(Xt,Xt.current),l)break;return null;case 22:case 23:return e.lanes=0,of(t,e,a);case 24:ca(e,Vt,t.memoizedState.cache)}return Je(t,e,a)}function vf(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Kt=!0;else{if(!Ls(t,a)&&(e.flags&128)===0)return Kt=!1,pm(t,e,a);Kt=(t.flags&131072)!==0}else Kt=!1,vt&&(e.flags&1048576)!==0&&Zr(e,di,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,n=l._init;if(l=n(l._payload),e.type=l,typeof l=="function")Qu(l)?(t=Xa(l,t),e.tag=1,e=hf(null,e,l,t,a)):(e.tag=0,e=Rs(null,e,l,t,a));else{if(l!=null){if(n=l.$$typeof,n===ot){e.tag=11,e=sf(null,e,l,t,a);break t}else if(n===mt){e.tag=14,e=cf(null,e,l,t,a);break t}}throw e=Ct(l)||l,Error(s(306,e,""))}}return e;case 0:return Rs(t,e,e.type,e.pendingProps,a);case 1:return l=e.type,n=Xa(l,e.pendingProps),hf(t,e,l,n,a);case 3:t:{if(At(e,e.stateNode.containerInfo),t===null)throw Error(s(387));l=e.pendingProps;var i=e.memoizedState;n=i.element,is(t,e),dn(e,l,null,a);var f=e.memoizedState;if(l=f.cache,ca(e,Vt,l),l!==i.cache&&Iu(e,[Vt],a,!0),fn(),l=f.element,i.isDehydrated)if(i={element:l,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=gf(t,e,l,a);break t}else if(l!==n){n=Se(Error(s(424)),e),ln(n),e=gf(t,e,l,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Lt=Me(t.firstChild),le=e,vt=!0,Ba=null,De=!0,a=$o(e,null,l,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(an(),l===n){e=Je(t,e,a);break t}Wt(t,e,l,a)}e=e.child}return e;case 26:return ji(t,e),t===null?(a=_d(e.type,null,e.pendingProps,null))?e.memoizedState=a:vt||(a=e.type,t=e.pendingProps,l=Ki(I.current).createElement(a),l[te]=e,l[ne]=t,Pt(l,a,t),Zt(l),e.stateNode=l):e.memoizedState=_d(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return aa(e),t===null&&vt&&(l=e.stateNode=bd(e.type,e.pendingProps,I.current),le=e,De=!0,n=Lt,Oa(e.type)?(pc=n,Lt=Me(l.firstChild)):Lt=n),Wt(t,e,e.pendingProps.children,a),ji(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&vt&&((n=l=Lt)&&(l=Xm(l,e.type,e.pendingProps,De),l!==null?(e.stateNode=l,le=e,Lt=Me(l.firstChild),De=!1,n=!0):n=!1),n||qa(e)),aa(e),n=e.type,i=e.pendingProps,f=t!==null?t.memoizedProps:null,l=i.children,dc(n,i)?l=null:f!==null&&dc(n,f)&&(e.flags|=32),e.memoizedState!==null&&(n=fs(t,e,cm,null,null,a),Cn._currentValue=n),ji(t,e),Wt(t,e,l,a),e.child;case 6:return t===null&&vt&&((t=a=Lt)&&(a=Qm(a,e.pendingProps,De),a!==null?(e.stateNode=a,le=e,Lt=null,t=!0):t=!1),t||qa(e)),null;case 13:return mf(t,e,a);case 4:return At(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Sl(e,null,l,a):Wt(t,e,l,a),e.child;case 11:return sf(t,e,e.type,e.pendingProps,a);case 7:return Wt(t,e,e.pendingProps,a),e.child;case 8:return Wt(t,e,e.pendingProps.children,a),e.child;case 12:return Wt(t,e,e.pendingProps.children,a),e.child;case 10:return l=e.pendingProps,ca(e,e.type,l.value),Wt(t,e,l.children,a),e.child;case 9:return n=e.type._context,l=e.pendingProps.children,Ga(e),n=ee(n),l=l(n),e.flags|=1,Wt(t,e,l,a),e.child;case 14:return cf(t,e,e.type,e.pendingProps,a);case 15:return rf(t,e,e.type,e.pendingProps,a);case 19:return yf(t,e,a);case 31:return l=e.pendingProps,a=e.mode,l={mode:l.mode,children:l.children},t===null?(a=Di(l,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=Ge(t.child,l),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return of(t,e,a);case 24:return Ga(e),l=ee(Vt),t===null?(n=as(),n===null&&(n=Mt,i=ts(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=a),n=i),e.memoizedState={parent:l,cache:n},ns(e),ca(e,Vt,n)):((t.lanes&a)!==0&&(is(t,e),dn(e,null,null,a),fn()),n=t.memoizedState,i=e.memoizedState,n.parent!==l?(n={parent:l,cache:l},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),ca(e,Vt,l)):(l=i.cache,ca(e,Vt,l),l!==n.cache&&Iu(e,[Vt],a,!0))),Wt(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function $e(t){t.flags|=4}function bf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ad(e)){if(e=Ee.current,e!==null&&((gt&4194048)===gt?Ce!==null:(gt&62914560)!==gt&&(gt&536870912)===0||e!==Ce))throw rn=ls,to;t.flags|=8192}}function Ci(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?$c():536870912,t.lanes|=e,El|=e)}function bn(t,e){if(!vt)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Ut(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,l=0;if(e)for(var n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=l,t.childLanes=a,e}function ym(t,e,a){var l=e.pendingProps;switch($u(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ut(e),null;case 1:return Ut(e),null;case 3:return a=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Qe(Vt),re(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(en(e)?$e(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,$r())),Ut(e),null;case 26:return a=e.memoizedState,t===null?($e(e),a!==null?(Ut(e),bf(e,a)):(Ut(e),e.flags&=-16777217)):a?a!==t.memoizedState?($e(e),Ut(e),bf(e,a)):(Ut(e),e.flags&=-16777217):(t.memoizedProps!==l&&$e(e),Ut(e),e.flags&=-16777217),null;case 27:la(e),a=I.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(s(166));return Ut(e),null}t=Y.current,en(e)?Kr(e):(t=bd(n,l,a),e.stateNode=t,$e(e))}return Ut(e),null;case 5:if(la(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(!l){if(e.stateNode===null)throw Error(s(166));return Ut(e),null}if(t=Y.current,en(e))Kr(e);else{switch(n=Ki(I.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}t[te]=e,t[ne]=l;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Pt(t,a,l),a){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&$e(e)}}return Ut(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&$e(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(s(166));if(t=I.current,en(e)){if(t=e.stateNode,a=e.memoizedProps,l=null,n=le,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}t[te]=e,t=!!(t.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||dd(t.nodeValue,a)),t||qa(e)}else t=Ki(t).createTextNode(l),t[te]=e,e.stateNode=t}return Ut(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=en(e),l!==null&&l.dehydrated!==null){if(t===null){if(!n)throw Error(s(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[te]=e}else an(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ut(e),n=!1}else n=$r(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Ke(e),e):(Ke(e),null)}if(Ke(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=l!==null,t=t!==null&&t.memoizedState!==null,a){l=e.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var i=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(i=l.memoizedState.cachePool.pool),i!==n&&(l.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),Ci(e,e.updateQueue),Ut(e),null;case 4:return re(),t===null&&sc(e.stateNode.containerInfo),Ut(e),null;case 10:return Qe(e.type),Ut(e),null;case 19:if(k(Xt),n=e.memoizedState,n===null)return Ut(e),null;if(l=(e.flags&128)!==0,i=n.rendering,i===null)if(l)bn(n,!1);else{if(Ht!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=Ri(t),i!==null){for(e.flags|=128,bn(n,!1),t=i.updateQueue,e.updateQueue=t,Ci(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)Qr(a,t),a=a.sibling;return L(Xt,Xt.current&1|2),e.child}t=t.sibling}n.tail!==null&&je()>Hi&&(e.flags|=128,l=!0,bn(n,!1),e.lanes=4194304)}else{if(!l)if(t=Ri(i),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Ci(e,t),bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!vt)return Ut(e),null}else 2*je()-n.renderingStartTime>Hi&&a!==536870912&&(e.flags|=128,l=!0,bn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=je(),e.sibling=null,t=Xt.current,L(Xt,l?t&1|2:t&1),e):(Ut(e),null);case 22:case 23:return Ke(e),rs(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(a&536870912)!==0&&(e.flags&128)===0&&(Ut(e),e.subtreeFlags&6&&(e.flags|=8192)):Ut(e),a=e.updateQueue,a!==null&&Ci(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==a&&(e.flags|=2048),t!==null&&k(Ya),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Qe(Vt),Ut(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function vm(t,e){switch($u(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Qe(Vt),re(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return la(e),null;case 13:if(Ke(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));an()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return k(Xt),null;case 4:return re(),null;case 10:return Qe(e.type),null;case 22:case 23:return Ke(e),rs(),t!==null&&k(Ya),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Qe(Vt),null;case 25:return null;default:return null}}function xf(t,e){switch($u(e),e.tag){case 3:Qe(Vt),re();break;case 26:case 27:case 5:la(e);break;case 4:re();break;case 13:Ke(e);break;case 19:k(Xt);break;case 10:Qe(e.type);break;case 22:case 23:Ke(e),rs(),t!==null&&k(Ya);break;case 24:Qe(Vt)}}function xn(t,e){try{var a=e.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&t)===t){l=void 0;var i=a.create,f=a.inst;l=i(),f.destroy=l}a=a.next}while(a!==n)}}catch(h){zt(e,e.return,h)}}function ma(t,e,a){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var i=n.next;l=i;do{if((l.tag&t)===t){var f=l.inst,h=f.destroy;if(h!==void 0){f.destroy=void 0,n=e;var v=a,E=h;try{E()}catch(M){zt(n,v,M)}}}l=l.next}while(l!==i)}}catch(M){zt(e,e.return,M)}}function Sf(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{uo(e,a)}catch(l){zt(t,t.return,l)}}}function _f(t,e,a){a.props=Xa(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(l){zt(t,e,l)}}function Sn(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof a=="function"?t.refCleanup=a(l):a.current=l}}catch(n){zt(t,e,n)}}function Ue(t,e){var a=t.ref,l=t.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){zt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){zt(t,e,n)}else a.current=null}function Of(t){var e=t.type,a=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break t;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){zt(t,t.return,n)}}function Hs(t,e,a){try{var l=t.stateNode;qm(l,t.type,a,e),l[ne]=e}catch(n){zt(t,t.return,n)}}function Nf(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Oa(t.type)||t.tag===4}function Bs(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Nf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Oa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function qs(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=Zi));else if(l!==4&&(l===27&&Oa(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(qs(t,e,a),t=t.sibling;t!==null;)qs(t,e,a),t=t.sibling}function Ui(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(l!==4&&(l===27&&Oa(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(Ui(t,e,a),t=t.sibling;t!==null;)Ui(t,e,a),t=t.sibling}function Ef(t){var e=t.stateNode,a=t.memoizedProps;try{for(var l=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Pt(e,l,a),e[te]=t,e[ne]=a}catch(i){zt(t,t.return,i)}}var We=!1,kt=!1,ks=!1,Tf=typeof WeakSet=="function"?WeakSet:Set,Jt=null;function bm(t,e){if(t=t.containerInfo,oc=Ii,t=Ur(t),Bu(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,i=l.focusNode;l=l.focusOffset;try{a.nodeType,i.nodeType}catch{a=null;break t}var f=0,h=-1,v=-1,E=0,M=0,D=t,T=null;e:for(;;){for(var A;D!==a||n!==0&&D.nodeType!==3||(h=f+n),D!==i||l!==0&&D.nodeType!==3||(v=f+l),D.nodeType===3&&(f+=D.nodeValue.length),(A=D.firstChild)!==null;)T=D,D=A;for(;;){if(D===t)break e;if(T===a&&++E===n&&(h=f),T===i&&++M===l&&(v=f),(A=D.nextSibling)!==null)break;D=T,T=D.parentNode}D=A}a=h===-1||v===-1?null:{start:h,end:v}}else a=null}a=a||{start:0,end:0}}else a=null;for(fc={focusedElem:t,selectionRange:a},Ii=!1,Jt=e;Jt!==null;)if(e=Jt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Jt=t;else for(;Jt!==null;){switch(e=Jt,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,a=e,n=i.memoizedProps,i=i.memoizedState,l=a.stateNode;try{var P=Xa(a.type,n,a.elementType===a.type);t=l.getSnapshotBeforeUpdate(P,i),l.__reactInternalSnapshotBeforeUpdate=t}catch($){zt(a,a.return,$)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)gc(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":gc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Jt=t;break}Jt=e.return}}function Af(t,e,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:pa(t,a),l&4&&xn(5,a);break;case 1:if(pa(t,a),l&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(f){zt(a,a.return,f)}else{var n=Xa(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){zt(a,a.return,f)}}l&64&&Sf(a),l&512&&Sn(a,a.return);break;case 3:if(pa(t,a),l&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{uo(t,e)}catch(f){zt(a,a.return,f)}}break;case 27:e===null&&l&4&&Ef(a);case 26:case 5:pa(t,a),e===null&&l&4&&Of(a),l&512&&Sn(a,a.return);break;case 12:pa(t,a);break;case 13:pa(t,a),l&4&&Mf(t,a),l&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=zm.bind(null,a),Zm(t,a))));break;case 22:if(l=a.memoizedState!==null||We,!l){e=e!==null&&e.memoizedState!==null||kt,n=We;var i=kt;We=l,(kt=e)&&!i?ya(t,a,(a.subtreeFlags&8772)!==0):pa(t,a),We=n,kt=i}break;case 30:break;default:pa(t,a)}}function zf(t){var e=t.alternate;e!==null&&(t.alternate=null,zf(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&bu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var jt=null,se=!1;function Fe(t,e,a){for(a=a.child;a!==null;)Rf(t,e,a),a=a.sibling}function Rf(t,e,a){if(oe&&typeof oe.onCommitFiberUnmount=="function")try{oe.onCommitFiberUnmount(Gl,a)}catch{}switch(a.tag){case 26:kt||Ue(a,e),Fe(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:kt||Ue(a,e);var l=jt,n=se;Oa(a.type)&&(jt=a.stateNode,se=!1),Fe(t,e,a),Mn(a.stateNode),jt=l,se=n;break;case 5:kt||Ue(a,e);case 6:if(l=jt,n=se,jt=null,Fe(t,e,a),jt=l,se=n,jt!==null)if(se)try{(jt.nodeType===9?jt.body:jt.nodeName==="HTML"?jt.ownerDocument.body:jt).removeChild(a.stateNode)}catch(i){zt(a,e,i)}else try{jt.removeChild(a.stateNode)}catch(i){zt(a,e,i)}break;case 18:jt!==null&&(se?(t=jt,yd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),Bn(t)):yd(jt,a.stateNode));break;case 4:l=jt,n=se,jt=a.stateNode.containerInfo,se=!0,Fe(t,e,a),jt=l,se=n;break;case 0:case 11:case 14:case 15:kt||ma(2,a,e),kt||ma(4,a,e),Fe(t,e,a);break;case 1:kt||(Ue(a,e),l=a.stateNode,typeof l.componentWillUnmount=="function"&&_f(a,e,l)),Fe(t,e,a);break;case 21:Fe(t,e,a);break;case 22:kt=(l=kt)||a.memoizedState!==null,Fe(t,e,a),kt=l;break;default:Fe(t,e,a)}}function Mf(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Bn(t)}catch(a){zt(e,e.return,a)}}function xm(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Tf),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Tf),e;default:throw Error(s(435,t.tag))}}function Gs(t,e){var a=xm(t);e.forEach(function(l){var n=Rm.bind(null,t,l);a.has(l)||(a.add(l),l.then(n,n))})}function ge(t,e){var a=e.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],i=t,f=e,h=f;t:for(;h!==null;){switch(h.tag){case 27:if(Oa(h.type)){jt=h.stateNode,se=!1;break t}break;case 5:jt=h.stateNode,se=!1;break t;case 3:case 4:jt=h.stateNode.containerInfo,se=!0;break t}h=h.return}if(jt===null)throw Error(s(160));Rf(i,f,n),jt=null,se=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)wf(e,t),e=e.sibling}var Re=null;function wf(t,e){var a=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ge(e,t),me(t),l&4&&(ma(3,t,t.return),xn(3,t),ma(5,t,t.return));break;case 1:ge(e,t),me(t),l&512&&(kt||a===null||Ue(a,a.return)),l&64&&We&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Re;if(ge(e,t),me(t),l&512&&(kt||a===null||Ue(a,a.return)),l&4){var i=a!==null?a.memoizedState:null;if(l=t.memoizedState,a===null)if(l===null)if(t.stateNode===null){t:{l=t.type,a=t.memoizedProps,n=n.ownerDocument||n;e:switch(l){case"title":i=n.getElementsByTagName("title")[0],(!i||i[Xl]||i[te]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(l),n.head.insertBefore(i,n.querySelector("head > title"))),Pt(i,l,a),i[te]=t,Zt(i),l=i;break t;case"link":var f=Ed("link","href",n).get(l+(a.href||""));if(f){for(var h=0;h<f.length;h++)if(i=f[h],i.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&i.getAttribute("rel")===(a.rel==null?null:a.rel)&&i.getAttribute("title")===(a.title==null?null:a.title)&&i.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(h,1);break e}}i=n.createElement(l),Pt(i,l,a),n.head.appendChild(i);break;case"meta":if(f=Ed("meta","content",n).get(l+(a.content||""))){for(h=0;h<f.length;h++)if(i=f[h],i.getAttribute("content")===(a.content==null?null:""+a.content)&&i.getAttribute("name")===(a.name==null?null:a.name)&&i.getAttribute("property")===(a.property==null?null:a.property)&&i.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&i.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(h,1);break e}}i=n.createElement(l),Pt(i,l,a),n.head.appendChild(i);break;default:throw Error(s(468,l))}i[te]=t,Zt(i),l=i}t.stateNode=l}else Td(n,t.type,t.stateNode);else t.stateNode=Nd(n,l,t.memoizedProps);else i!==l?(i===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):i.count--,l===null?Td(n,t.type,t.stateNode):Nd(n,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Hs(t,t.memoizedProps,a.memoizedProps)}break;case 27:ge(e,t),me(t),l&512&&(kt||a===null||Ue(a,a.return)),a!==null&&l&4&&Hs(t,t.memoizedProps,a.memoizedProps);break;case 5:if(ge(e,t),me(t),l&512&&(kt||a===null||Ue(a,a.return)),t.flags&32){n=t.stateNode;try{ll(n,"")}catch(A){zt(t,t.return,A)}}l&4&&t.stateNode!=null&&(n=t.memoizedProps,Hs(t,n,a!==null?a.memoizedProps:n)),l&1024&&(ks=!0);break;case 6:if(ge(e,t),me(t),l&4){if(t.stateNode===null)throw Error(s(162));l=t.memoizedProps,a=t.stateNode;try{a.nodeValue=l}catch(A){zt(t,t.return,A)}}break;case 3:if(Wi=null,n=Re,Re=Ji(e.containerInfo),ge(e,t),Re=n,me(t),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Bn(e.containerInfo)}catch(A){zt(t,t.return,A)}ks&&(ks=!1,jf(t));break;case 4:l=Re,Re=Ji(t.stateNode.containerInfo),ge(e,t),me(t),Re=l;break;case 12:ge(e,t),me(t);break;case 13:ge(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Ks=je()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Gs(t,l)));break;case 22:n=t.memoizedState!==null;var v=a!==null&&a.memoizedState!==null,E=We,M=kt;if(We=E||n,kt=M||v,ge(e,t),kt=M,We=E,me(t),l&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(a===null||v||We||kt||Qa(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){v=a=e;try{if(i=v.stateNode,n)f=i.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{h=v.stateNode;var D=v.memoizedProps.style,T=D!=null&&D.hasOwnProperty("display")?D.display:null;h.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(A){zt(v,v.return,A)}}}else if(e.tag===6){if(a===null){v=e;try{v.stateNode.nodeValue=n?"":v.memoizedProps}catch(A){zt(v,v.return,A)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Gs(t,a))));break;case 19:ge(e,t),me(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Gs(t,l)));break;case 30:break;case 21:break;default:ge(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var a,l=t.return;l!==null;){if(Nf(l)){a=l;break}l=l.return}if(a==null)throw Error(s(160));switch(a.tag){case 27:var n=a.stateNode,i=Bs(t);Ui(t,i,n);break;case 5:var f=a.stateNode;a.flags&32&&(ll(f,""),a.flags&=-33);var h=Bs(t);Ui(t,h,f);break;case 3:case 4:var v=a.stateNode.containerInfo,E=Bs(t);qs(t,E,v);break;default:throw Error(s(161))}}catch(M){zt(t,t.return,M)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function jf(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;jf(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function pa(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Af(t,e.alternate,e),e=e.sibling}function Qa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ma(4,e,e.return),Qa(e);break;case 1:Ue(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&_f(e,e.return,a),Qa(e);break;case 27:Mn(e.stateNode);case 26:case 5:Ue(e,e.return),Qa(e);break;case 22:e.memoizedState===null&&Qa(e);break;case 30:Qa(e);break;default:Qa(e)}t=t.sibling}}function ya(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,n=t,i=e,f=i.flags;switch(i.tag){case 0:case 11:case 15:ya(n,i,a),xn(4,i);break;case 1:if(ya(n,i,a),l=i,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(E){zt(l,l.return,E)}if(l=i,n=l.updateQueue,n!==null){var h=l.stateNode;try{var v=n.shared.hiddenCallbacks;if(v!==null)for(n.shared.hiddenCallbacks=null,n=0;n<v.length;n++)io(v[n],h)}catch(E){zt(l,l.return,E)}}a&&f&64&&Sf(i),Sn(i,i.return);break;case 27:Ef(i);case 26:case 5:ya(n,i,a),a&&l===null&&f&4&&Of(i),Sn(i,i.return);break;case 12:ya(n,i,a);break;case 13:ya(n,i,a),a&&f&4&&Mf(n,i);break;case 22:i.memoizedState===null&&ya(n,i,a),Sn(i,i.return);break;case 30:break;default:ya(n,i,a)}e=e.sibling}}function Ys(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&un(a))}function Vs(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t))}function Le(t,e,a,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Df(t,e,a,l),e=e.sibling}function Df(t,e,a,l){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Le(t,e,a,l),n&2048&&xn(9,e);break;case 1:Le(t,e,a,l);break;case 3:Le(t,e,a,l),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&un(t)));break;case 12:if(n&2048){Le(t,e,a,l),t=e.stateNode;try{var i=e.memoizedProps,f=i.id,h=i.onPostCommit;typeof h=="function"&&h(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(v){zt(e,e.return,v)}}else Le(t,e,a,l);break;case 13:Le(t,e,a,l);break;case 23:break;case 22:i=e.stateNode,f=e.alternate,e.memoizedState!==null?i._visibility&2?Le(t,e,a,l):_n(t,e):i._visibility&2?Le(t,e,a,l):(i._visibility|=2,_l(t,e,a,l,(e.subtreeFlags&10256)!==0)),n&2048&&Ys(f,e);break;case 24:Le(t,e,a,l),n&2048&&Vs(e.alternate,e);break;default:Le(t,e,a,l)}}function _l(t,e,a,l,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,f=e,h=a,v=l,E=f.flags;switch(f.tag){case 0:case 11:case 15:_l(i,f,h,v,n),xn(8,f);break;case 23:break;case 22:var M=f.stateNode;f.memoizedState!==null?M._visibility&2?_l(i,f,h,v,n):_n(i,f):(M._visibility|=2,_l(i,f,h,v,n)),n&&E&2048&&Ys(f.alternate,f);break;case 24:_l(i,f,h,v,n),n&&E&2048&&Vs(f.alternate,f);break;default:_l(i,f,h,v,n)}e=e.sibling}}function _n(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,l=e,n=l.flags;switch(l.tag){case 22:_n(a,l),n&2048&&Ys(l.alternate,l);break;case 24:_n(a,l),n&2048&&Vs(l.alternate,l);break;default:_n(a,l)}e=e.sibling}}var On=8192;function Ol(t){if(t.subtreeFlags&On)for(t=t.child;t!==null;)Cf(t),t=t.sibling}function Cf(t){switch(t.tag){case 26:Ol(t),t.flags&On&&t.memoizedState!==null&&ip(Re,t.memoizedState,t.memoizedProps);break;case 5:Ol(t);break;case 3:case 4:var e=Re;Re=Ji(t.stateNode.containerInfo),Ol(t),Re=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=On,On=16777216,Ol(t),On=e):Ol(t));break;default:Ol(t)}}function Uf(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Nn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];Jt=l,Hf(l,t)}Uf(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Lf(t),t=t.sibling}function Lf(t){switch(t.tag){case 0:case 11:case 15:Nn(t),t.flags&2048&&ma(9,t,t.return);break;case 3:Nn(t);break;case 12:Nn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Li(t)):Nn(t);break;default:Nn(t)}}function Li(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];Jt=l,Hf(l,t)}Uf(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ma(8,e,e.return),Li(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,Li(e));break;default:Li(e)}t=t.sibling}}function Hf(t,e){for(;Jt!==null;){var a=Jt;switch(a.tag){case 0:case 11:case 15:ma(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:un(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Jt=l;else t:for(a=t;Jt!==null;){l=Jt;var n=l.sibling,i=l.return;if(zf(l),l===a){Jt=null;break t}if(n!==null){n.return=i,Jt=n;break t}Jt=i}}}var Sm={getCacheForType:function(t){var e=ee(Vt),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},_m=typeof WeakMap=="function"?WeakMap:Map,St=0,Mt=null,ct=null,gt=0,_t=0,pe=null,va=!1,Nl=!1,Xs=!1,Pe=0,Ht=0,ba=0,Za=0,Qs=0,Te=0,El=0,En=null,ce=null,Zs=!1,Ks=0,Hi=1/0,Bi=null,xa=null,Ft=0,Sa=null,Tl=null,Al=0,Js=0,$s=null,Bf=null,Tn=0,Ws=null;function ye(){if((St&2)!==0&&gt!==0)return gt&-gt;if(z.T!==null){var t=gl;return t!==0?t:lc()}return Pc()}function qf(){Te===0&&(Te=(gt&536870912)===0||vt?Jc():536870912);var t=Ee.current;return t!==null&&(t.flags|=32),Te}function ve(t,e,a){(t===Mt&&(_t===2||_t===9)||t.cancelPendingCommit!==null)&&(zl(t,0),_a(t,gt,Te,!1)),Vl(t,a),((St&2)===0||t!==Mt)&&(t===Mt&&((St&2)===0&&(Za|=a),Ht===4&&_a(t,gt,Te,!1)),He(t))}function kf(t,e,a){if((St&6)!==0)throw Error(s(327));var l=!a&&(e&124)===0&&(e&t.expiredLanes)===0||Yl(t,e),n=l?Em(t,e):Is(t,e,!0),i=l;do{if(n===0){Nl&&!l&&_a(t,e,0,!1);break}else{if(a=t.current.alternate,i&&!Om(a)){n=Is(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var h=t;n=En;var v=h.current.memoizedState.isDehydrated;if(v&&(zl(h,f).flags|=256),f=Is(h,f,!1),f!==2){if(Xs&&!v){h.errorRecoveryDisabledLanes|=i,Za|=i,n=4;break t}i=ce,ce=n,i!==null&&(ce===null?ce=i:ce.push.apply(ce,i))}n=f}if(i=!1,n!==2)continue}}if(n===1){zl(t,0),_a(t,e,0,!0);break}t:{switch(l=t,i=n,i){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:_a(l,e,Te,!va);break t;case 2:ce=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(n=Ks+300-je(),10<n)){if(_a(l,e,Te,!va),$n(l,0,!0)!==0)break t;l.timeoutHandle=md(Gf.bind(null,l,a,ce,Bi,Zs,e,Te,Za,El,va,i,2,-0,0),n);break t}Gf(l,a,ce,Bi,Zs,e,Te,Za,El,va,i,0,-0,0)}}break}while(!0);He(t)}function Gf(t,e,a,l,n,i,f,h,v,E,M,D,T,A){if(t.timeoutHandle=-1,D=e.subtreeFlags,(D&8192||(D&16785408)===16785408)&&(Dn={stylesheets:null,count:0,unsuspend:np},Cf(e),D=up(),D!==null)){t.cancelPendingCommit=D(Jf.bind(null,t,e,i,a,l,n,f,h,v,M,1,T,A)),_a(t,i,f,!E);return}Jf(t,e,i,a,l,n,f,h,v)}function Om(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],i=n.getSnapshot;n=n.value;try{if(!de(i(),n))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function _a(t,e,a,l){e&=~Qs,e&=~Za,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var n=e;0<n;){var i=31-fe(n),f=1<<i;l[i]=-1,n&=~f}a!==0&&Wc(t,a,e)}function qi(){return(St&6)===0?(An(0),!1):!0}function Fs(){if(ct!==null){if(_t===0)var t=ct.return;else t=ct,Xe=ka=null,gs(t),xl=null,yn=0,t=ct;for(;t!==null;)xf(t.alternate,t),t=t.return;ct=null}}function zl(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,Gm(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),Fs(),Mt=t,ct=a=Ge(t.current,null),gt=e,_t=0,pe=null,va=!1,Nl=Yl(t,e),Xs=!1,El=Te=Qs=Za=ba=Ht=0,ce=En=null,Zs=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var n=31-fe(l),i=1<<n;e|=t[n],l&=~i}return Pe=e,si(),a}function Yf(t,e){nt=null,z.H=Ti,e===cn||e===pi?(e=lo(),_t=3):e===to?(e=lo(),_t=4):_t=e===uf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,pe=e,ct===null&&(Ht=1,wi(t,Se(e,t.current)))}function Vf(){var t=z.H;return z.H=Ti,t===null?Ti:t}function Xf(){var t=z.A;return z.A=Sm,t}function Ps(){Ht=4,va||(gt&4194048)!==gt&&Ee.current!==null||(Nl=!0),(ba&134217727)===0&&(Za&134217727)===0||Mt===null||_a(Mt,gt,Te,!1)}function Is(t,e,a){var l=St;St|=2;var n=Vf(),i=Xf();(Mt!==t||gt!==e)&&(Bi=null,zl(t,e)),e=!1;var f=Ht;t:do try{if(_t!==0&&ct!==null){var h=ct,v=pe;switch(_t){case 8:Fs(),f=6;break t;case 3:case 2:case 9:case 6:Ee.current===null&&(e=!0);var E=_t;if(_t=0,pe=null,Rl(t,h,v,E),a&&Nl){f=0;break t}break;default:E=_t,_t=0,pe=null,Rl(t,h,v,E)}}Nm(),f=Ht;break}catch(M){Yf(t,M)}while(!0);return e&&t.shellSuspendCounter++,Xe=ka=null,St=l,z.H=n,z.A=i,ct===null&&(Mt=null,gt=0,si()),f}function Nm(){for(;ct!==null;)Qf(ct)}function Em(t,e){var a=St;St|=2;var l=Vf(),n=Xf();Mt!==t||gt!==e?(Bi=null,Hi=je()+500,zl(t,e)):Nl=Yl(t,e);t:do try{if(_t!==0&&ct!==null){e=ct;var i=pe;e:switch(_t){case 1:_t=0,pe=null,Rl(t,e,i,1);break;case 2:case 9:if(eo(i)){_t=0,pe=null,Zf(e);break}e=function(){_t!==2&&_t!==9||Mt!==t||(_t=7),He(t)},i.then(e,e);break t;case 3:_t=7;break t;case 4:_t=5;break t;case 7:eo(i)?(_t=0,pe=null,Zf(e)):(_t=0,pe=null,Rl(t,e,i,7));break;case 5:var f=null;switch(ct.tag){case 26:f=ct.memoizedState;case 5:case 27:var h=ct;if(!f||Ad(f)){_t=0,pe=null;var v=h.sibling;if(v!==null)ct=v;else{var E=h.return;E!==null?(ct=E,ki(E)):ct=null}break e}}_t=0,pe=null,Rl(t,e,i,5);break;case 6:_t=0,pe=null,Rl(t,e,i,6);break;case 8:Fs(),Ht=6;break t;default:throw Error(s(462))}}Tm();break}catch(M){Yf(t,M)}while(!0);return Xe=ka=null,z.H=l,z.A=n,St=a,ct!==null?0:(Mt=null,gt=0,si(),Ht)}function Tm(){for(;ct!==null&&!Jh();)Qf(ct)}function Qf(t){var e=vf(t.alternate,t,Pe);t.memoizedProps=t.pendingProps,e===null?ki(t):ct=e}function Zf(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=df(a,e,e.pendingProps,e.type,void 0,gt);break;case 11:e=df(a,e,e.pendingProps,e.type.render,e.ref,gt);break;case 5:gs(e);default:xf(a,e),e=ct=Qr(e,Pe),e=vf(a,e,Pe)}t.memoizedProps=t.pendingProps,e===null?ki(t):ct=e}function Rl(t,e,a,l){Xe=ka=null,gs(e),xl=null,yn=0;var n=e.return;try{if(mm(t,n,e,a,gt)){Ht=1,wi(t,Se(a,t.current)),ct=null;return}}catch(i){if(n!==null)throw ct=n,i;Ht=1,wi(t,Se(a,t.current)),ct=null;return}e.flags&32768?(vt||l===1?t=!0:Nl||(gt&536870912)!==0?t=!1:(va=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ee.current,l!==null&&l.tag===13&&(l.flags|=16384))),Kf(e,t)):ki(e)}function ki(t){var e=t;do{if((e.flags&32768)!==0){Kf(e,va);return}t=e.return;var a=ym(e.alternate,e,Pe);if(a!==null){ct=a;return}if(e=e.sibling,e!==null){ct=e;return}ct=e=t}while(e!==null);Ht===0&&(Ht=5)}function Kf(t,e){do{var a=vm(t.alternate,t);if(a!==null){a.flags&=32767,ct=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){ct=t;return}ct=t=a}while(t!==null);Ht=6,ct=null}function Jf(t,e,a,l,n,i,f,h,v){t.cancelPendingCommit=null;do Gi();while(Ft!==0);if((St&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(i=e.lanes|e.childLanes,i|=Vu,ng(t,a,i,f,h,v),t===Mt&&(ct=Mt=null,gt=0),Tl=e,Sa=t,Al=a,Js=i,$s=n,Bf=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Mm(Zn,function(){return If(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=z.T,z.T=null,n=H.p,H.p=2,f=St,St|=4;try{bm(t,e,a)}finally{St=f,H.p=n,z.T=l}}Ft=1,$f(),Wf(),Ff()}}function $f(){if(Ft===1){Ft=0;var t=Sa,e=Tl,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=St;St|=4;try{wf(e,t);var i=fc,f=Ur(t.containerInfo),h=i.focusedElem,v=i.selectionRange;if(f!==h&&h&&h.ownerDocument&&Cr(h.ownerDocument.documentElement,h)){if(v!==null&&Bu(h)){var E=v.start,M=v.end;if(M===void 0&&(M=E),"selectionStart"in h)h.selectionStart=E,h.selectionEnd=Math.min(M,h.value.length);else{var D=h.ownerDocument||document,T=D&&D.defaultView||window;if(T.getSelection){var A=T.getSelection(),P=h.textContent.length,$=Math.min(v.start,P),Tt=v.end===void 0?$:Math.min(v.end,P);!A.extend&&$>Tt&&(f=Tt,Tt=$,$=f);var _=Dr(h,$),x=Dr(h,Tt);if(_&&x&&(A.rangeCount!==1||A.anchorNode!==_.node||A.anchorOffset!==_.offset||A.focusNode!==x.node||A.focusOffset!==x.offset)){var N=D.createRange();N.setStart(_.node,_.offset),A.removeAllRanges(),$>Tt?(A.addRange(N),A.extend(x.node,x.offset)):(N.setEnd(x.node,x.offset),A.addRange(N))}}}}for(D=[],A=h;A=A.parentNode;)A.nodeType===1&&D.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<D.length;h++){var j=D[h];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}Ii=!!oc,fc=oc=null}finally{St=n,H.p=l,z.T=a}}t.current=e,Ft=2}}function Wf(){if(Ft===2){Ft=0;var t=Sa,e=Tl,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=z.T,z.T=null;var l=H.p;H.p=2;var n=St;St|=4;try{Af(t,e.alternate,e)}finally{St=n,H.p=l,z.T=a}}Ft=3}}function Ff(){if(Ft===4||Ft===3){Ft=0,$h();var t=Sa,e=Tl,a=Al,l=Bf;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ft=5:(Ft=0,Tl=Sa=null,Pf(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(xa=null),yu(a),e=e.stateNode,oe&&typeof oe.onCommitFiberRoot=="function")try{oe.onCommitFiberRoot(Gl,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=z.T,n=H.p,H.p=2,z.T=null;try{for(var i=t.onRecoverableError,f=0;f<l.length;f++){var h=l[f];i(h.value,{componentStack:h.stack})}}finally{z.T=e,H.p=n}}(Al&3)!==0&&Gi(),He(t),n=t.pendingLanes,(a&4194090)!==0&&(n&42)!==0?t===Ws?Tn++:(Tn=0,Ws=t):Tn=0,An(0)}}function Pf(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,un(e)))}function Gi(t){return $f(),Wf(),Ff(),If()}function If(){if(Ft!==5)return!1;var t=Sa,e=Js;Js=0;var a=yu(Al),l=z.T,n=H.p;try{H.p=32>a?32:a,z.T=null,a=$s,$s=null;var i=Sa,f=Al;if(Ft=0,Tl=Sa=null,Al=0,(St&6)!==0)throw Error(s(331));var h=St;if(St|=4,Lf(i.current),Df(i,i.current,f,a),St=h,An(0,!1),oe&&typeof oe.onPostCommitFiberRoot=="function")try{oe.onPostCommitFiberRoot(Gl,i)}catch{}return!0}finally{H.p=n,z.T=l,Pf(t,e)}}function td(t,e,a){e=Se(a,e),e=zs(t.stateNode,e,2),t=fa(t,e,2),t!==null&&(Vl(t,2),He(t))}function zt(t,e,a){if(t.tag===3)td(t,t,a);else for(;e!==null;){if(e.tag===3){td(e,t,a);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(xa===null||!xa.has(l))){t=Se(a,t),a=lf(2),l=fa(e,a,2),l!==null&&(nf(a,l,e,t),Vl(l,2),He(l));break}}e=e.return}}function tc(t,e,a){var l=t.pingCache;if(l===null){l=t.pingCache=new _m;var n=new Set;l.set(e,n)}else n=l.get(e),n===void 0&&(n=new Set,l.set(e,n));n.has(a)||(Xs=!0,n.add(a),t=Am.bind(null,t,e,a),e.then(t,t))}function Am(t,e,a){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,Mt===t&&(gt&a)===a&&(Ht===4||Ht===3&&(gt&62914560)===gt&&300>je()-Ks?(St&2)===0&&zl(t,0):Qs|=a,El===gt&&(El=0)),He(t)}function ed(t,e){e===0&&(e=$c()),t=ol(t,e),t!==null&&(Vl(t,e),He(t))}function zm(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),ed(t,a)}function Rm(t,e){var a=0;switch(t.tag){case 13:var l=t.stateNode,n=t.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(s(314))}l!==null&&l.delete(e),ed(t,a)}function Mm(t,e){return hu(t,e)}var Yi=null,Ml=null,ec=!1,Vi=!1,ac=!1,Ka=0;function He(t){t!==Ml&&t.next===null&&(Ml===null?Yi=Ml=t:Ml=Ml.next=t),Vi=!0,ec||(ec=!0,jm())}function An(t,e){if(!ac&&Vi){ac=!0;do for(var a=!1,l=Yi;l!==null;){if(t!==0){var n=l.pendingLanes;if(n===0)var i=0;else{var f=l.suspendedLanes,h=l.pingedLanes;i=(1<<31-fe(42|t)+1)-1,i&=n&~(f&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(a=!0,id(l,i))}else i=gt,i=$n(l,l===Mt?i:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(i&3)===0||Yl(l,i)||(a=!0,id(l,i));l=l.next}while(a);ac=!1}}function wm(){ad()}function ad(){Vi=ec=!1;var t=0;Ka!==0&&(km()&&(t=Ka),Ka=0);for(var e=je(),a=null,l=Yi;l!==null;){var n=l.next,i=ld(l,e);i===0?(l.next=null,a===null?Yi=n:a.next=n,n===null&&(Ml=a)):(a=l,(t!==0||(i&3)!==0)&&(Vi=!0)),l=n}An(t)}function ld(t,e){for(var a=t.suspendedLanes,l=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var f=31-fe(i),h=1<<f,v=n[f];v===-1?((h&a)===0||(h&l)!==0)&&(n[f]=lg(h,e)):v<=e&&(t.expiredLanes|=h),i&=~h}if(e=Mt,a=gt,a=$n(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,a===0||t===e&&(_t===2||_t===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&gu(l),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||Yl(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(l!==null&&gu(l),yu(a)){case 2:case 8:a=Zc;break;case 32:a=Zn;break;case 268435456:a=Kc;break;default:a=Zn}return l=nd.bind(null,t),a=hu(a,l),t.callbackPriority=e,t.callbackNode=a,e}return l!==null&&l!==null&&gu(l),t.callbackPriority=2,t.callbackNode=null,2}function nd(t,e){if(Ft!==0&&Ft!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(Gi()&&t.callbackNode!==a)return null;var l=gt;return l=$n(t,t===Mt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(kf(t,l,e),ld(t,je()),t.callbackNode!=null&&t.callbackNode===a?nd.bind(null,t):null)}function id(t,e){if(Gi())return null;kf(t,e,!0)}function jm(){Ym(function(){(St&6)!==0?hu(Qc,wm):ad()})}function lc(){return Ka===0&&(Ka=Jc()),Ka}function ud(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:ti(""+t)}function sd(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function Dm(t,e,a,l,n){if(e==="submit"&&a&&a.stateNode===n){var i=ud((n[ne]||null).action),f=l.submitter;f&&(e=(e=f[ne]||null)?ud(e.formAction):f.getAttribute("formAction"),e!==null&&(i=e,f=null));var h=new ni("action","action",null,l,n);t.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ka!==0){var v=f?sd(n,f):new FormData(n);Os(a,{pending:!0,data:v,method:n.method,action:i},null,v)}}else typeof i=="function"&&(h.preventDefault(),v=f?sd(n,f):new FormData(n),Os(a,{pending:!0,data:v,method:n.method,action:i},i,v))},currentTarget:n}]})}}for(var nc=0;nc<Yu.length;nc++){var ic=Yu[nc],Cm=ic.toLowerCase(),Um=ic[0].toUpperCase()+ic.slice(1);ze(Cm,"on"+Um)}ze(Br,"onAnimationEnd"),ze(qr,"onAnimationIteration"),ze(kr,"onAnimationStart"),ze("dblclick","onDoubleClick"),ze("focusin","onFocus"),ze("focusout","onBlur"),ze(Pg,"onTransitionRun"),ze(Ig,"onTransitionStart"),ze(tm,"onTransitionCancel"),ze(Gr,"onTransitionEnd"),tl("onMouseEnter",["mouseout","mouseover"]),tl("onMouseLeave",["mouseout","mouseover"]),tl("onPointerEnter",["pointerout","pointerover"]),tl("onPointerLeave",["pointerout","pointerover"]),wa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),wa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),wa("onBeforeInput",["compositionend","keypress","textInput","paste"]),wa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),wa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),wa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zn));function cd(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var l=t[a],n=l.event;l=l.listeners;t:{var i=void 0;if(e)for(var f=l.length-1;0<=f;f--){var h=l[f],v=h.instance,E=h.currentTarget;if(h=h.listener,v!==i&&n.isPropagationStopped())break t;i=h,n.currentTarget=E;try{i(n)}catch(M){Mi(M)}n.currentTarget=null,i=v}else for(f=0;f<l.length;f++){if(h=l[f],v=h.instance,E=h.currentTarget,h=h.listener,v!==i&&n.isPropagationStopped())break t;i=h,n.currentTarget=E;try{i(n)}catch(M){Mi(M)}n.currentTarget=null,i=v}}}}function rt(t,e){var a=e[vu];a===void 0&&(a=e[vu]=new Set);var l=t+"__bubble";a.has(l)||(rd(e,t,2,!1),a.add(l))}function uc(t,e,a){var l=0;e&&(l|=4),rd(a,t,l,e)}var Xi="_reactListening"+Math.random().toString(36).slice(2);function sc(t){if(!t[Xi]){t[Xi]=!0,tr.forEach(function(a){a!=="selectionchange"&&(Lm.has(a)||uc(a,!1,t),uc(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Xi]||(e[Xi]=!0,uc("selectionchange",!1,e))}}function rd(t,e,a,l){switch(Dd(e)){case 2:var n=rp;break;case 8:n=op;break;default:n=Sc}a=n.bind(null,e,a,t),n=void 0,!Ru||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),l?n!==void 0?t.addEventListener(e,a,{capture:!0,passive:n}):t.addEventListener(e,a,!0):n!==void 0?t.addEventListener(e,a,{passive:n}):t.addEventListener(e,a,!1)}function cc(t,e,a,l,n){var i=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var h=l.stateNode.containerInfo;if(h===n)break;if(f===4)for(f=l.return;f!==null;){var v=f.tag;if((v===3||v===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;h!==null;){if(f=Fa(h),f===null)return;if(v=f.tag,v===5||v===6||v===26||v===27){l=i=f;continue t}h=h.parentNode}}l=l.return}gr(function(){var E=i,M=Au(a),D=[];t:{var T=Yr.get(t);if(T!==void 0){var A=ni,P=t;switch(t){case"keypress":if(ai(a)===0)break t;case"keydown":case"keyup":A=Mg;break;case"focusin":P="focus",A=Du;break;case"focusout":P="blur",A=Du;break;case"beforeblur":case"afterblur":A=Du;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=yr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=vg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Dg;break;case Br:case qr:case kr:A=Sg;break;case Gr:A=Ug;break;case"scroll":case"scrollend":A=pg;break;case"wheel":A=Hg;break;case"copy":case"cut":case"paste":A=Og;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=br;break;case"toggle":case"beforetoggle":A=qg}var $=(e&4)!==0,Tt=!$&&(t==="scroll"||t==="scrollend"),_=$?T!==null?T+"Capture":null:T;$=[];for(var x=E,N;x!==null;){var j=x;if(N=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||N===null||_===null||(j=Zl(x,_),j!=null&&$.push(Rn(x,j,N))),Tt)break;x=x.return}0<$.length&&(T=new A(T,P,null,a,M),D.push({event:T,listeners:$}))}}if((e&7)===0){t:{if(T=t==="mouseover"||t==="pointerover",A=t==="mouseout"||t==="pointerout",T&&a!==Tu&&(P=a.relatedTarget||a.fromElement)&&(Fa(P)||P[Wa]))break t;if((A||T)&&(T=M.window===M?M:(T=M.ownerDocument)?T.defaultView||T.parentWindow:window,A?(P=a.relatedTarget||a.toElement,A=E,P=P?Fa(P):null,P!==null&&(Tt=d(P),$=P.tag,P!==Tt||$!==5&&$!==27&&$!==6)&&(P=null)):(A=null,P=E),A!==P)){if($=yr,j="onMouseLeave",_="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&($=br,j="onPointerLeave",_="onPointerEnter",x="pointer"),Tt=A==null?T:Ql(A),N=P==null?T:Ql(P),T=new $(j,x+"leave",A,a,M),T.target=Tt,T.relatedTarget=N,j=null,Fa(M)===E&&($=new $(_,x+"enter",P,a,M),$.target=N,$.relatedTarget=Tt,j=$),Tt=j,A&&P)e:{for($=A,_=P,x=0,N=$;N;N=wl(N))x++;for(N=0,j=_;j;j=wl(j))N++;for(;0<x-N;)$=wl($),x--;for(;0<N-x;)_=wl(_),N--;for(;x--;){if($===_||_!==null&&$===_.alternate)break e;$=wl($),_=wl(_)}$=null}else $=null;A!==null&&od(D,T,A,$,!1),P!==null&&Tt!==null&&od(D,Tt,P,$,!0)}}t:{if(T=E?Ql(E):window,A=T.nodeName&&T.nodeName.toLowerCase(),A==="select"||A==="input"&&T.type==="file")var V=Ar;else if(Er(T))if(zr)V=$g;else{V=Kg;var ut=Zg}else A=T.nodeName,!A||A.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?E&&Eu(E.elementType)&&(V=Ar):V=Jg;if(V&&(V=V(t,E))){Tr(D,V,a,M);break t}ut&&ut(t,T,E),t==="focusout"&&E&&T.type==="number"&&E.memoizedProps.value!=null&&Nu(T,"number",T.value)}switch(ut=E?Ql(E):window,t){case"focusin":(Er(ut)||ut.contentEditable==="true")&&(sl=ut,qu=E,tn=null);break;case"focusout":tn=qu=sl=null;break;case"mousedown":ku=!0;break;case"contextmenu":case"mouseup":case"dragend":ku=!1,Lr(D,a,M);break;case"selectionchange":if(Fg)break;case"keydown":case"keyup":Lr(D,a,M)}var K;if(Uu)t:{switch(t){case"compositionstart":var W="onCompositionStart";break t;case"compositionend":W="onCompositionEnd";break t;case"compositionupdate":W="onCompositionUpdate";break t}W=void 0}else ul?Or(t,a)&&(W="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(W="onCompositionStart");W&&(xr&&a.locale!=="ko"&&(ul||W!=="onCompositionStart"?W==="onCompositionEnd"&&ul&&(K=mr()):(sa=M,Mu="value"in sa?sa.value:sa.textContent,ul=!0)),ut=Qi(E,W),0<ut.length&&(W=new vr(W,t,null,a,M),D.push({event:W,listeners:ut}),K?W.data=K:(K=Nr(a),K!==null&&(W.data=K)))),(K=Gg?Yg(t,a):Vg(t,a))&&(W=Qi(E,"onBeforeInput"),0<W.length&&(ut=new vr("onBeforeInput","beforeinput",null,a,M),D.push({event:ut,listeners:W}),ut.data=K)),Dm(D,t,E,a,M)}cd(D,e)})}function Rn(t,e,a){return{instance:t,listener:e,currentTarget:a}}function Qi(t,e){for(var a=e+"Capture",l=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=Zl(t,a),n!=null&&l.unshift(Rn(t,n,i)),n=Zl(t,e),n!=null&&l.push(Rn(t,n,i))),t.tag===3)return l;t=t.return}return[]}function wl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function od(t,e,a,l,n){for(var i=e._reactName,f=[];a!==null&&a!==l;){var h=a,v=h.alternate,E=h.stateNode;if(h=h.tag,v!==null&&v===l)break;h!==5&&h!==26&&h!==27||E===null||(v=E,n?(E=Zl(a,i),E!=null&&f.unshift(Rn(a,E,v))):n||(E=Zl(a,i),E!=null&&f.push(Rn(a,E,v)))),a=a.return}f.length!==0&&t.push({event:e,listeners:f})}var Hm=/\r\n?/g,Bm=/\u0000|\uFFFD/g;function fd(t){return(typeof t=="string"?t:""+t).replace(Hm,`
`).replace(Bm,"")}function dd(t,e){return e=fd(e),fd(t)===e}function Zi(){}function Et(t,e,a,l,n,i){switch(a){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ll(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ll(t,""+l);break;case"className":Fn(t,"class",l);break;case"tabIndex":Fn(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Fn(t,a,l);break;case"style":dr(t,l,i);break;case"data":if(e!=="object"){Fn(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=ti(""+l),t.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(a==="formAction"?(e!=="input"&&Et(t,e,"name",n.name,n,null),Et(t,e,"formEncType",n.formEncType,n,null),Et(t,e,"formMethod",n.formMethod,n,null),Et(t,e,"formTarget",n.formTarget,n,null)):(Et(t,e,"encType",n.encType,n,null),Et(t,e,"method",n.method,n,null),Et(t,e,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=ti(""+l),t.setAttribute(a,l);break;case"onClick":l!=null&&(t.onclick=Zi);break;case"onScroll":l!=null&&rt("scroll",t);break;case"onScrollEnd":l!=null&&rt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}a=ti(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""+l):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":l===!0?t.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,l):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(a,l):t.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(a):t.setAttribute(a,l);break;case"popover":rt("beforetoggle",t),rt("toggle",t),Wn(t,"popover",l);break;case"xlinkActuate":qe(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":qe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":qe(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":qe(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":qe(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":qe(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":qe(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":qe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":qe(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Wn(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=gg.get(a)||a,Wn(t,a,l))}}function rc(t,e,a,l,n,i){switch(a){case"style":dr(t,l,i);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"children":typeof l=="string"?ll(t,l):(typeof l=="number"||typeof l=="bigint")&&ll(t,""+l);break;case"onScroll":l!=null&&rt("scroll",t);break;case"onScrollEnd":l!=null&&rt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=Zi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!er.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),e=a.slice(2,n?a.length-7:void 0),i=t[ne]||null,i=i!=null?i[a]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof l=="function")){typeof i!="function"&&i!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,l,n);break t}a in t?t[a]=l:l===!0?t.setAttribute(a,""):Wn(t,a,l)}}}function Pt(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":rt("error",t),rt("load",t);var l=!1,n=!1,i;for(i in a)if(a.hasOwnProperty(i)){var f=a[i];if(f!=null)switch(i){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Et(t,e,i,f,a,null)}}n&&Et(t,e,"srcSet",a.srcSet,a,null),l&&Et(t,e,"src",a.src,a,null);return;case"input":rt("invalid",t);var h=i=f=n=null,v=null,E=null;for(l in a)if(a.hasOwnProperty(l)){var M=a[l];if(M!=null)switch(l){case"name":n=M;break;case"type":f=M;break;case"checked":v=M;break;case"defaultChecked":E=M;break;case"value":i=M;break;case"defaultValue":h=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(s(137,e));break;default:Et(t,e,l,M,a,null)}}cr(t,i,h,v,E,f,n,!1),Pn(t);return;case"select":rt("invalid",t),l=f=i=null;for(n in a)if(a.hasOwnProperty(n)&&(h=a[n],h!=null))switch(n){case"value":i=h;break;case"defaultValue":f=h;break;case"multiple":l=h;default:Et(t,e,n,h,a,null)}e=i,a=f,t.multiple=!!l,e!=null?al(t,!!l,e,!1):a!=null&&al(t,!!l,a,!0);return;case"textarea":rt("invalid",t),i=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(h=a[f],h!=null))switch(f){case"value":l=h;break;case"defaultValue":n=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(s(91));break;default:Et(t,e,f,h,a,null)}or(t,l,n,i),Pn(t);return;case"option":for(v in a)if(a.hasOwnProperty(v)&&(l=a[v],l!=null))switch(v){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Et(t,e,v,l,a,null)}return;case"dialog":rt("beforetoggle",t),rt("toggle",t),rt("cancel",t),rt("close",t);break;case"iframe":case"object":rt("load",t);break;case"video":case"audio":for(l=0;l<zn.length;l++)rt(zn[l],t);break;case"image":rt("error",t),rt("load",t);break;case"details":rt("toggle",t);break;case"embed":case"source":case"link":rt("error",t),rt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in a)if(a.hasOwnProperty(E)&&(l=a[E],l!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:Et(t,e,E,l,a,null)}return;default:if(Eu(e)){for(M in a)a.hasOwnProperty(M)&&(l=a[M],l!==void 0&&rc(t,e,M,l,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(l=a[h],l!=null&&Et(t,e,h,l,a,null))}function qm(t,e,a,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,f=null,h=null,v=null,E=null,M=null;for(A in a){var D=a[A];if(a.hasOwnProperty(A)&&D!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":v=D;default:l.hasOwnProperty(A)||Et(t,e,A,null,l,D)}}for(var T in l){var A=l[T];if(D=a[T],l.hasOwnProperty(T)&&(A!=null||D!=null))switch(T){case"type":i=A;break;case"name":n=A;break;case"checked":E=A;break;case"defaultChecked":M=A;break;case"value":f=A;break;case"defaultValue":h=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,e));break;default:A!==D&&Et(t,e,T,A,l,D)}}Ou(t,f,h,v,E,M,i,n);return;case"select":A=f=h=T=null;for(i in a)if(v=a[i],a.hasOwnProperty(i)&&v!=null)switch(i){case"value":break;case"multiple":A=v;default:l.hasOwnProperty(i)||Et(t,e,i,null,l,v)}for(n in l)if(i=l[n],v=a[n],l.hasOwnProperty(n)&&(i!=null||v!=null))switch(n){case"value":T=i;break;case"defaultValue":h=i;break;case"multiple":f=i;default:i!==v&&Et(t,e,n,i,l,v)}e=h,a=f,l=A,T!=null?al(t,!!a,T,!1):!!l!=!!a&&(e!=null?al(t,!!a,e,!0):al(t,!!a,a?[]:"",!1));return;case"textarea":A=T=null;for(h in a)if(n=a[h],a.hasOwnProperty(h)&&n!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Et(t,e,h,null,l,n)}for(f in l)if(n=l[f],i=a[f],l.hasOwnProperty(f)&&(n!=null||i!=null))switch(f){case"value":T=n;break;case"defaultValue":A=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==i&&Et(t,e,f,n,l,i)}rr(t,T,A);return;case"option":for(var P in a)if(T=a[P],a.hasOwnProperty(P)&&T!=null&&!l.hasOwnProperty(P))switch(P){case"selected":t.selected=!1;break;default:Et(t,e,P,null,l,T)}for(v in l)if(T=l[v],A=a[v],l.hasOwnProperty(v)&&T!==A&&(T!=null||A!=null))switch(v){case"selected":t.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:Et(t,e,v,T,l,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var $ in a)T=a[$],a.hasOwnProperty($)&&T!=null&&!l.hasOwnProperty($)&&Et(t,e,$,null,l,T);for(E in l)if(T=l[E],A=a[E],l.hasOwnProperty(E)&&T!==A&&(T!=null||A!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(s(137,e));break;default:Et(t,e,E,T,l,A)}return;default:if(Eu(e)){for(var Tt in a)T=a[Tt],a.hasOwnProperty(Tt)&&T!==void 0&&!l.hasOwnProperty(Tt)&&rc(t,e,Tt,void 0,l,T);for(M in l)T=l[M],A=a[M],!l.hasOwnProperty(M)||T===A||T===void 0&&A===void 0||rc(t,e,M,T,l,A);return}}for(var _ in a)T=a[_],a.hasOwnProperty(_)&&T!=null&&!l.hasOwnProperty(_)&&Et(t,e,_,null,l,T);for(D in l)T=l[D],A=a[D],!l.hasOwnProperty(D)||T===A||T==null&&A==null||Et(t,e,D,T,l,A)}var oc=null,fc=null;function Ki(t){return t.nodeType===9?t:t.ownerDocument}function hd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gd(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function dc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var hc=null;function km(){var t=window.event;return t&&t.type==="popstate"?t===hc?!1:(hc=t,!0):(hc=null,!1)}var md=typeof setTimeout=="function"?setTimeout:void 0,Gm=typeof clearTimeout=="function"?clearTimeout:void 0,pd=typeof Promise=="function"?Promise:void 0,Ym=typeof queueMicrotask=="function"?queueMicrotask:typeof pd<"u"?function(t){return pd.resolve(null).then(t).catch(Vm)}:md;function Vm(t){setTimeout(function(){throw t})}function Oa(t){return t==="head"}function yd(t,e){var a=e,l=0,n=0;do{var i=a.nextSibling;if(t.removeChild(a),i&&i.nodeType===8)if(a=i.data,a==="/$"){if(0<l&&8>l){a=l;var f=t.ownerDocument;if(a&1&&Mn(f.documentElement),a&2&&Mn(f.body),a&4)for(a=f.head,Mn(a),f=a.firstChild;f;){var h=f.nextSibling,v=f.nodeName;f[Xl]||v==="SCRIPT"||v==="STYLE"||v==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=h}}if(n===0){t.removeChild(i),Bn(e);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=i}while(a);Bn(e)}function gc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":gc(a),bu(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function Xm(t,e,a,l){for(;t.nodeType===1;){var n=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Xl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Me(t.nextSibling),t===null)break}return null}function Qm(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=Me(t.nextSibling),t===null))return null;return t}function mc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Zm(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var l=function(){e(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Me(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var pc=null;function vd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function bd(t,e,a){switch(e=Ki(a),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function Mn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);bu(t)}var Ae=new Map,xd=new Set;function Ji(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ie=H.d;H.d={f:Km,r:Jm,D:$m,C:Wm,L:Fm,m:Pm,X:tp,S:Im,M:ep};function Km(){var t=Ie.f(),e=qi();return t||e}function Jm(t){var e=Pa(t);e!==null&&e.tag===5&&e.type==="form"?qo(e):Ie.r(t)}var jl=typeof document>"u"?null:document;function Sd(t,e,a){var l=jl;if(l&&typeof e=="string"&&e){var n=xe(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),xd.has(n)||(xd.add(n),t={rel:t,crossOrigin:a,href:e},l.querySelector(n)===null&&(e=l.createElement("link"),Pt(e,"link",t),Zt(e),l.head.appendChild(e)))}}function $m(t){Ie.D(t),Sd("dns-prefetch",t,null)}function Wm(t,e){Ie.C(t,e),Sd("preconnect",t,e)}function Fm(t,e,a){Ie.L(t,e,a);var l=jl;if(l&&t&&e){var n='link[rel="preload"][as="'+xe(e)+'"]';e==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+xe(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+xe(a.imageSizes)+'"]')):n+='[href="'+xe(t)+'"]';var i=n;switch(e){case"style":i=Dl(t);break;case"script":i=Cl(t)}Ae.has(i)||(t=O({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),Ae.set(i,t),l.querySelector(n)!==null||e==="style"&&l.querySelector(wn(i))||e==="script"&&l.querySelector(jn(i))||(e=l.createElement("link"),Pt(e,"link",t),Zt(e),l.head.appendChild(e)))}}function Pm(t,e){Ie.m(t,e);var a=jl;if(a&&t){var l=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+xe(l)+'"][href="'+xe(t)+'"]',i=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Cl(t)}if(!Ae.has(i)&&(t=O({rel:"modulepreload",href:t},e),Ae.set(i,t),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(jn(i)))return}l=a.createElement("link"),Pt(l,"link",t),Zt(l),a.head.appendChild(l)}}}function Im(t,e,a){Ie.S(t,e,a);var l=jl;if(l&&t){var n=Ia(l).hoistableStyles,i=Dl(t);e=e||"default";var f=n.get(i);if(!f){var h={loading:0,preload:null};if(f=l.querySelector(wn(i)))h.loading=5;else{t=O({rel:"stylesheet",href:t,"data-precedence":e},a),(a=Ae.get(i))&&yc(t,a);var v=f=l.createElement("link");Zt(v),Pt(v,"link",t),v._p=new Promise(function(E,M){v.onload=E,v.onerror=M}),v.addEventListener("load",function(){h.loading|=1}),v.addEventListener("error",function(){h.loading|=2}),h.loading|=4,$i(f,e,l)}f={type:"stylesheet",instance:f,count:1,state:h},n.set(i,f)}}}function tp(t,e){Ie.X(t,e);var a=jl;if(a&&t){var l=Ia(a).hoistableScripts,n=Cl(t),i=l.get(n);i||(i=a.querySelector(jn(n)),i||(t=O({src:t,async:!0},e),(e=Ae.get(n))&&vc(t,e),i=a.createElement("script"),Zt(i),Pt(i,"link",t),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function ep(t,e){Ie.M(t,e);var a=jl;if(a&&t){var l=Ia(a).hoistableScripts,n=Cl(t),i=l.get(n);i||(i=a.querySelector(jn(n)),i||(t=O({src:t,async:!0,type:"module"},e),(e=Ae.get(n))&&vc(t,e),i=a.createElement("script"),Zt(i),Pt(i,"link",t),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},l.set(n,i))}}function _d(t,e,a,l){var n=(n=I.current)?Ji(n):null;if(!n)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=Dl(a.href),a=Ia(n).hoistableStyles,l=a.get(e),l||(l={type:"style",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=Dl(a.href);var i=Ia(n).hoistableStyles,f=i.get(t);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,f),(i=n.querySelector(wn(t)))&&!i._p&&(f.instance=i,f.state.loading=5),Ae.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Ae.set(t,a),i||ap(n,t,a,f.state))),e&&l===null)throw Error(s(528,""));return f}if(e&&l!==null)throw Error(s(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Cl(a),a=Ia(n).hoistableScripts,l=a.get(e),l||(l={type:"script",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Dl(t){return'href="'+xe(t)+'"'}function wn(t){return'link[rel="stylesheet"]['+t+"]"}function Od(t){return O({},t,{"data-precedence":t.precedence,precedence:null})}function ap(t,e,a,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Pt(e,"link",a),Zt(e),t.head.appendChild(e))}function Cl(t){return'[src="'+xe(t)+'"]'}function jn(t){return"script[async]"+t}function Nd(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+xe(a.href)+'"]');if(l)return e.instance=l,Zt(l),l;var n=O({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Zt(l),Pt(l,"style",n),$i(l,a.precedence,t),e.instance=l;case"stylesheet":n=Dl(a.href);var i=t.querySelector(wn(n));if(i)return e.state.loading|=4,e.instance=i,Zt(i),i;l=Od(a),(n=Ae.get(n))&&yc(l,n),i=(t.ownerDocument||t).createElement("link"),Zt(i);var f=i;return f._p=new Promise(function(h,v){f.onload=h,f.onerror=v}),Pt(i,"link",l),e.state.loading|=4,$i(i,a.precedence,t),e.instance=i;case"script":return i=Cl(a.src),(n=t.querySelector(jn(i)))?(e.instance=n,Zt(n),n):(l=a,(n=Ae.get(i))&&(l=O({},a),vc(l,n)),t=t.ownerDocument||t,n=t.createElement("script"),Zt(n),Pt(n,"link",l),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,$i(l,a.precedence,t));return e.instance}function $i(t,e,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,i=n,f=0;f<l.length;f++){var h=l[f];if(h.dataset.precedence===e)i=h;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function yc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function vc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Wi=null;function Ed(t,e,a){if(Wi===null){var l=new Map,n=Wi=new Map;n.set(a,l)}else n=Wi,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(t))return l;for(l.set(t,null),a=a.getElementsByTagName(t),n=0;n<a.length;n++){var i=a[n];if(!(i[Xl]||i[te]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var f=i.getAttribute(e)||"";f=t+f;var h=l.get(f);h?h.push(i):l.set(f,[i])}}return l}function Td(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function lp(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ad(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Dn=null;function np(){}function ip(t,e,a){if(Dn===null)throw Error(s(475));var l=Dn;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Dl(a.href),i=t.querySelector(wn(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Fi.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=i,Zt(i);return}i=t.ownerDocument||t,a=Od(a),(n=Ae.get(n))&&yc(a,n),i=i.createElement("link"),Zt(i);var f=i;f._p=new Promise(function(h,v){f.onload=h,f.onerror=v}),Pt(i,"link",a),e.instance=i}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=Fi.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function up(){if(Dn===null)throw Error(s(475));var t=Dn;return t.stylesheets&&t.count===0&&bc(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&bc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function Fi(){if(this.count--,this.count===0){if(this.stylesheets)bc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Pi=null;function bc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Pi=new Map,e.forEach(sp,t),Pi=null,Fi.call(t))}function sp(t,e){if(!(e.state.loading&4)){var a=Pi.get(t);if(a)var l=a.get(null);else{a=new Map,Pi.set(t,a);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var f=n[i];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=e.instance,f=n.getAttribute("data-precedence"),i=a.get(f)||l,i===l&&a.set(null,n),a.set(f,n),this.count++,l=Fi.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Cn={$$typeof:st,Provider:null,Consumer:null,_currentValue:C,_currentValue2:C,_threadCount:0};function cp(t,e,a,l,n,i,f,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=mu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mu(0),this.hiddenUpdates=mu(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function zd(t,e,a,l,n,i,f,h,v,E,M,D){return t=new cp(t,e,a,f,h,v,E,D),e=1,i===!0&&(e|=24),i=he(3,null,null,e),t.current=i,i.stateNode=t,e=ts(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:l,isDehydrated:a,cache:e},ns(i),t}function Rd(t){return t?(t=fl,t):fl}function Md(t,e,a,l,n,i){n=Rd(n),l.context===null?l.context=n:l.pendingContext=n,l=oa(e),l.payload={element:a},i=i===void 0?null:i,i!==null&&(l.callback=i),a=fa(t,l,e),a!==null&&(ve(a,t,e),on(a,t,e))}function wd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function xc(t,e){wd(t,e),(t=t.alternate)&&wd(t,e)}function jd(t){if(t.tag===13){var e=ol(t,67108864);e!==null&&ve(e,t,67108864),xc(t,67108864)}}var Ii=!0;function rp(t,e,a,l){var n=z.T;z.T=null;var i=H.p;try{H.p=2,Sc(t,e,a,l)}finally{H.p=i,z.T=n}}function op(t,e,a,l){var n=z.T;z.T=null;var i=H.p;try{H.p=8,Sc(t,e,a,l)}finally{H.p=i,z.T=n}}function Sc(t,e,a,l){if(Ii){var n=_c(l);if(n===null)cc(t,e,l,tu,a),Cd(t,l);else if(dp(n,t,e,a,l))l.stopPropagation();else if(Cd(t,l),e&4&&-1<fp.indexOf(t)){for(;n!==null;){var i=Pa(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var f=Ma(i.pendingLanes);if(f!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;f;){var v=1<<31-fe(f);h.entanglements[1]|=v,f&=~v}He(i),(St&6)===0&&(Hi=je()+500,An(0))}}break;case 13:h=ol(i,2),h!==null&&ve(h,i,2),qi(),xc(i,2)}if(i=_c(l),i===null&&cc(t,e,l,tu,a),i===n)break;n=i}n!==null&&l.stopPropagation()}else cc(t,e,l,null,a)}}function _c(t){return t=Au(t),Oc(t)}var tu=null;function Oc(t){if(tu=null,t=Fa(t),t!==null){var e=d(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=m(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return tu=t,null}function Dd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Wh()){case Qc:return 2;case Zc:return 8;case Zn:case Fh:return 32;case Kc:return 268435456;default:return 32}default:return 32}}var Nc=!1,Na=null,Ea=null,Ta=null,Un=new Map,Ln=new Map,Aa=[],fp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Cd(t,e){switch(t){case"focusin":case"focusout":Na=null;break;case"dragenter":case"dragleave":Ea=null;break;case"mouseover":case"mouseout":Ta=null;break;case"pointerover":case"pointerout":Un.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ln.delete(e.pointerId)}}function Hn(t,e,a,l,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:a,eventSystemFlags:l,nativeEvent:i,targetContainers:[n]},e!==null&&(e=Pa(e),e!==null&&jd(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function dp(t,e,a,l,n){switch(e){case"focusin":return Na=Hn(Na,t,e,a,l,n),!0;case"dragenter":return Ea=Hn(Ea,t,e,a,l,n),!0;case"mouseover":return Ta=Hn(Ta,t,e,a,l,n),!0;case"pointerover":var i=n.pointerId;return Un.set(i,Hn(Un.get(i)||null,t,e,a,l,n)),!0;case"gotpointercapture":return i=n.pointerId,Ln.set(i,Hn(Ln.get(i)||null,t,e,a,l,n)),!0}return!1}function Ud(t){var e=Fa(t.target);if(e!==null){var a=d(e);if(a!==null){if(e=a.tag,e===13){if(e=m(a),e!==null){t.blockedOn=e,ig(t.priority,function(){if(a.tag===13){var l=ye();l=pu(l);var n=ol(a,l);n!==null&&ve(n,a,l),xc(a,l)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function eu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=_c(t.nativeEvent);if(a===null){a=t.nativeEvent;var l=new a.constructor(a.type,a);Tu=l,a.target.dispatchEvent(l),Tu=null}else return e=Pa(a),e!==null&&jd(e),t.blockedOn=a,!1;e.shift()}return!0}function Ld(t,e,a){eu(t)&&a.delete(e)}function hp(){Nc=!1,Na!==null&&eu(Na)&&(Na=null),Ea!==null&&eu(Ea)&&(Ea=null),Ta!==null&&eu(Ta)&&(Ta=null),Un.forEach(Ld),Ln.forEach(Ld)}function au(t,e){t.blockedOn===e&&(t.blockedOn=null,Nc||(Nc=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,hp)))}var lu=null;function Hd(t){lu!==t&&(lu=t,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){lu===t&&(lu=null);for(var e=0;e<t.length;e+=3){var a=t[e],l=t[e+1],n=t[e+2];if(typeof l!="function"){if(Oc(l||a)===null)continue;break}var i=Pa(a);i!==null&&(t.splice(e,3),e-=3,Os(i,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Bn(t){function e(v){return au(v,t)}Na!==null&&au(Na,t),Ea!==null&&au(Ea,t),Ta!==null&&au(Ta,t),Un.forEach(e),Ln.forEach(e);for(var a=0;a<Aa.length;a++){var l=Aa[a];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Aa.length&&(a=Aa[0],a.blockedOn===null);)Ud(a),a.blockedOn===null&&Aa.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],i=a[l+1],f=n[ne]||null;if(typeof i=="function")f||Hd(a);else if(f){var h=null;if(i&&i.hasAttribute("formAction")){if(n=i,f=i[ne]||null)h=f.formAction;else if(Oc(n)!==null)continue}else h=f.action;typeof h=="function"?a[l+1]=h:(a.splice(l,3),l-=3),Hd(a)}}}function Ec(t){this._internalRoot=t}nu.prototype.render=Ec.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var a=e.current,l=ye();Md(a,l,t,e,null,null)},nu.prototype.unmount=Ec.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Md(t.current,2,null,t,null,null),qi(),e[Wa]=null}};function nu(t){this._internalRoot=t}nu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Pc();t={blockedOn:null,target:t,priority:e};for(var a=0;a<Aa.length&&e!==0&&e<Aa[a].priority;a++);Aa.splice(a,0,t),a===0&&Ud(t)}};var Bd=u.version;if(Bd!=="19.1.1")throw Error(s(527,Bd,"19.1.1"));H.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=y(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var gp={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var iu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!iu.isDisabled&&iu.supportsFiber)try{Gl=iu.inject(gp),oe=iu}catch{}}return kn.createRoot=function(t,e){if(!o(t))throw Error(s(299));var a=!1,l="",n=Io,i=tf,f=ef,h=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=zd(t,1,!1,null,null,a,l,n,i,f,h,null),t[Wa]=e.current,sc(t),new Ec(e)},kn.hydrateRoot=function(t,e,a){if(!o(t))throw Error(s(299));var l=!1,n="",i=Io,f=tf,h=ef,v=null,E=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(i=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(v=a.unstable_transitionCallbacks),a.formState!==void 0&&(E=a.formState)),e=zd(t,1,!0,e,a??null,l,n,i,f,h,v,E),e.context=Rd(null),a=e.current,l=ye(),l=pu(l),n=oa(l),n.callback=null,fa(a,n,l),a=l,e.current.lanes=a,Vl(e,a),He(e),t[Wa]=e.current,sc(t),new nu(e)},kn.version="19.1.1",kn}var Jd;function Np(){if(Jd)return zc.exports;Jd=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(u){console.error(u)}}return r(),zc.exports=Op(),zc.exports}var Ep=Np();const tt=r=>typeof r=="string",Gn=()=>{let r,u;const c=new Promise((s,o)=>{r=s,u=o});return c.resolve=r,c.reject=u,c},$d=r=>r==null?"":""+r,Tp=(r,u,c)=>{r.forEach(s=>{u[s]&&(c[s]=u[s])})},Ap=/###/g,Wd=r=>r&&r.indexOf("###")>-1?r.replace(Ap,"."):r,Fd=r=>!r||tt(r),Vn=(r,u,c)=>{const s=tt(u)?u.split("."):u;let o=0;for(;o<s.length-1;){if(Fd(r))return{};const d=Wd(s[o]);!r[d]&&c&&(r[d]=new c),Object.prototype.hasOwnProperty.call(r,d)?r=r[d]:r={},++o}return Fd(r)?{}:{obj:r,k:Wd(s[o])}},Pd=(r,u,c)=>{const{obj:s,k:o}=Vn(r,u,Object);if(s!==void 0||u.length===1){s[o]=c;return}let d=u[u.length-1],m=u.slice(0,u.length-1),p=Vn(r,m,Object);for(;p.obj===void 0&&m.length;)d=`${m[m.length-1]}.${d}`,m=m.slice(0,m.length-1),p=Vn(r,m,Object),p!=null&&p.obj&&typeof p.obj[`${p.k}.${d}`]<"u"&&(p.obj=void 0);p.obj[`${p.k}.${d}`]=c},zp=(r,u,c,s)=>{const{obj:o,k:d}=Vn(r,u,Object);o[d]=o[d]||[],o[d].push(c)},ru=(r,u)=>{const{obj:c,k:s}=Vn(r,u);if(c&&Object.prototype.hasOwnProperty.call(c,s))return c[s]},Rp=(r,u,c)=>{const s=ru(r,c);return s!==void 0?s:ru(u,c)},Oh=(r,u,c)=>{for(const s in u)s!=="__proto__"&&s!=="constructor"&&(s in r?tt(r[s])||r[s]instanceof String||tt(u[s])||u[s]instanceof String?c&&(r[s]=u[s]):Oh(r[s],u[s],c):r[s]=u[s]);return r},Ul=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Mp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const wp=r=>tt(r)?r.replace(/[&<>"'\/]/g,u=>Mp[u]):r;class jp{constructor(u){this.capacity=u,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(u){const c=this.regExpMap.get(u);if(c!==void 0)return c;const s=new RegExp(u);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(u,s),this.regExpQueue.push(u),s}}const Dp=[" ",",","?","!",";"],Cp=new jp(20),Up=(r,u,c)=>{u=u||"",c=c||"";const s=Dp.filter(m=>u.indexOf(m)<0&&c.indexOf(m)<0);if(s.length===0)return!0;const o=Cp.getRegExp(`(${s.map(m=>m==="?"?"\\?":m).join("|")})`);let d=!o.test(r);if(!d){const m=r.indexOf(c);m>0&&!o.test(r.substring(0,m))&&(d=!0)}return d},Uc=(r,u,c=".")=>{if(!r)return;if(r[u])return Object.prototype.hasOwnProperty.call(r,u)?r[u]:void 0;const s=u.split(c);let o=r;for(let d=0;d<s.length;){if(!o||typeof o!="object")return;let m,p="";for(let y=d;y<s.length;++y)if(y!==d&&(p+=c),p+=s[y],m=o[p],m!==void 0){if(["string","number","boolean"].indexOf(typeof m)>-1&&y<s.length-1)continue;d+=y-d+1;break}o=m}return o},Xn=r=>r==null?void 0:r.replace("_","-"),Lp={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,u){var c,s;(s=(c=console==null?void 0:console[r])==null?void 0:c.apply)==null||s.call(c,console,u)}};class ou{constructor(u,c={}){this.init(u,c)}init(u,c={}){this.prefix=c.prefix||"i18next:",this.logger=u||Lp,this.options=c,this.debug=c.debug}log(...u){return this.forward(u,"log","",!0)}warn(...u){return this.forward(u,"warn","",!0)}error(...u){return this.forward(u,"error","")}deprecate(...u){return this.forward(u,"warn","WARNING DEPRECATED: ",!0)}forward(u,c,s,o){return o&&!this.debug?null:(tt(u[0])&&(u[0]=`${s}${this.prefix} ${u[0]}`),this.logger[c](u))}create(u){return new ou(this.logger,{prefix:`${this.prefix}:${u}:`,...this.options})}clone(u){return u=u||this.options,u.prefix=u.prefix||this.prefix,new ou(this.logger,u)}}var Be=new ou;class du{constructor(){this.observers={}}on(u,c){return u.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const o=this.observers[s].get(c)||0;this.observers[s].set(c,o+1)}),this}off(u,c){if(this.observers[u]){if(!c){delete this.observers[u];return}this.observers[u].delete(c)}}emit(u,...c){this.observers[u]&&Array.from(this.observers[u].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o(...c)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([o,d])=>{for(let m=0;m<d;m++)o.apply(o,[u,...c])})}}class Id extends du{constructor(u,c={ns:["translation"],defaultNS:"translation"}){super(),this.data=u||{},this.options=c,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(u){this.options.ns.indexOf(u)<0&&this.options.ns.push(u)}removeNamespaces(u){const c=this.options.ns.indexOf(u);c>-1&&this.options.ns.splice(c,1)}getResource(u,c,s,o={}){var g,O;const d=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,m=o.ignoreJSONStructure!==void 0?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let p;u.indexOf(".")>-1?p=u.split("."):(p=[u,c],s&&(Array.isArray(s)?p.push(...s):tt(s)&&d?p.push(...s.split(d)):p.push(s)));const y=ru(this.data,p);return!y&&!c&&!s&&u.indexOf(".")>-1&&(u=p[0],c=p[1],s=p.slice(2).join(".")),y||!m||!tt(s)?y:Uc((O=(g=this.data)==null?void 0:g[u])==null?void 0:O[c],s,d)}addResource(u,c,s,o,d={silent:!1}){const m=d.keySeparator!==void 0?d.keySeparator:this.options.keySeparator;let p=[u,c];s&&(p=p.concat(m?s.split(m):s)),u.indexOf(".")>-1&&(p=u.split("."),o=c,c=p[1]),this.addNamespaces(c),Pd(this.data,p,o),d.silent||this.emit("added",u,c,s,o)}addResources(u,c,s,o={silent:!1}){for(const d in s)(tt(s[d])||Array.isArray(s[d]))&&this.addResource(u,c,d,s[d],{silent:!0});o.silent||this.emit("added",u,c,s)}addResourceBundle(u,c,s,o,d,m={silent:!1,skipCopy:!1}){let p=[u,c];u.indexOf(".")>-1&&(p=u.split("."),o=s,s=c,c=p[1]),this.addNamespaces(c);let y=ru(this.data,p)||{};m.skipCopy||(s=JSON.parse(JSON.stringify(s))),o?Oh(y,s,d):y={...y,...s},Pd(this.data,p,y),m.silent||this.emit("added",u,c,s)}removeResourceBundle(u,c){this.hasResourceBundle(u,c)&&delete this.data[u][c],this.removeNamespaces(c),this.emit("removed",u,c)}hasResourceBundle(u,c){return this.getResource(u,c)!==void 0}getResourceBundle(u,c){return c||(c=this.options.defaultNS),this.getResource(u,c)}getDataByLanguage(u){return this.data[u]}hasLanguageSomeTranslations(u){const c=this.getDataByLanguage(u);return!!(c&&Object.keys(c)||[]).find(o=>c[o]&&Object.keys(c[o]).length>0)}toJSON(){return this.data}}var Nh={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,u,c,s,o){return r.forEach(d=>{var m;u=((m=this.processors[d])==null?void 0:m.process(u,c,s,o))??u}),u}};const th={},eh=r=>!tt(r)&&typeof r!="boolean"&&typeof r!="number";class fu extends du{constructor(u,c={}){super(),Tp(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],u,this),this.options=c,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Be.create("translator")}changeLanguage(u){u&&(this.language=u)}exists(u,c={interpolation:{}}){const s={...c};if(u==null)return!1;const o=this.resolve(u,s);return(o==null?void 0:o.res)!==void 0}extractFromKey(u,c){let s=c.nsSeparator!==void 0?c.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const o=c.keySeparator!==void 0?c.keySeparator:this.options.keySeparator;let d=c.ns||this.options.defaultNS||[];const m=s&&u.indexOf(s)>-1,p=!this.options.userDefinedKeySeparator&&!c.keySeparator&&!this.options.userDefinedNsSeparator&&!c.nsSeparator&&!Up(u,s,o);if(m&&!p){const y=u.match(this.interpolator.nestingRegexp);if(y&&y.length>0)return{key:u,namespaces:tt(d)?[d]:d};const g=u.split(s);(s!==o||s===o&&this.options.ns.indexOf(g[0])>-1)&&(d=g.shift()),u=g.join(o)}return{key:u,namespaces:tt(d)?[d]:d}}translate(u,c,s){let o=typeof c=="object"?{...c}:c;if(typeof o!="object"&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(o={...o}),o||(o={}),u==null)return"";Array.isArray(u)||(u=[String(u)]);const d=o.returnDetails!==void 0?o.returnDetails:this.options.returnDetails,m=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator,{key:p,namespaces:y}=this.extractFromKey(u[u.length-1],o),g=y[y.length-1];let O=o.nsSeparator!==void 0?o.nsSeparator:this.options.nsSeparator;O===void 0&&(O=":");const R=o.lng||this.language,B=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((R==null?void 0:R.toLowerCase())==="cimode")return B?d?{res:`${g}${O}${p}`,usedKey:p,exactUsedKey:p,usedLng:R,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:`${g}${O}${p}`:d?{res:p,usedKey:p,exactUsedKey:p,usedLng:R,usedNS:g,usedParams:this.getUsedParamsDetails(o)}:p;const q=this.resolve(u,o);let U=q==null?void 0:q.res;const Z=(q==null?void 0:q.usedKey)||p,F=(q==null?void 0:q.exactUsedKey)||p,bt=["[object Number]","[object Function]","[object RegExp]"],ft=o.joinArrays!==void 0?o.joinArrays:this.options.joinArrays,st=!this.i18nFormat||this.i18nFormat.handleAsObject,ot=o.count!==void 0&&!tt(o.count),J=fu.hasDefaultValue(o),xt=ot?this.pluralResolver.getSuffix(R,o.count,o):"",mt=o.ordinal&&ot?this.pluralResolver.getSuffix(R,o.count,{ordinal:!1}):"",G=ot&&!o.ordinal&&o.count===0,et=G&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${xt}`]||o[`defaultValue${mt}`]||o.defaultValue;let dt=U;st&&!U&&J&&(dt=et);const wt=eh(dt),Dt=Object.prototype.toString.apply(dt);if(st&&dt&&wt&&bt.indexOf(Dt)<0&&!(tt(ft)&&Array.isArray(dt))){if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const Bt=this.options.returnedObjectHandler?this.options.returnedObjectHandler(Z,dt,{...o,ns:y}):`key '${p} (${this.language})' returned an object instead of string.`;return d?(q.res=Bt,q.usedParams=this.getUsedParamsDetails(o),q):Bt}if(m){const Bt=Array.isArray(dt),Ct=Bt?[]:{},Rt=Bt?F:Z;for(const z in dt)if(Object.prototype.hasOwnProperty.call(dt,z)){const H=`${Rt}${m}${z}`;J&&!U?Ct[z]=this.translate(H,{...o,defaultValue:eh(et)?et[z]:void 0,joinArrays:!1,ns:y}):Ct[z]=this.translate(H,{...o,joinArrays:!1,ns:y}),Ct[z]===H&&(Ct[z]=dt[z])}U=Ct}}else if(st&&tt(ft)&&Array.isArray(U))U=U.join(ft),U&&(U=this.extendTranslation(U,u,o,s));else{let Bt=!1,Ct=!1;!this.isValidLookup(U)&&J&&(Bt=!0,U=et),this.isValidLookup(U)||(Ct=!0,U=p);const z=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&Ct?void 0:U,H=J&&et!==U&&this.options.updateMissing;if(Ct||Bt||H){if(this.logger.log(H?"updateKey":"missingKey",R,g,p,H?et:U),m){const w=this.resolve(p,{...o,keySeparator:!1});w&&w.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let C=[];const ht=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if(this.options.saveMissingTo==="fallback"&&ht&&ht[0])for(let w=0;w<ht.length;w++)C.push(ht[w]);else this.options.saveMissingTo==="all"?C=this.languageUtils.toResolveHierarchy(o.lng||this.language):C.push(o.lng||this.language);const b=(w,k,L)=>{var lt;const Y=J&&L!==U?L:z;this.options.missingKeyHandler?this.options.missingKeyHandler(w,g,k,Y,H,o):(lt=this.backendConnector)!=null&&lt.saveMissing&&this.backendConnector.saveMissing(w,g,k,Y,H,o),this.emit("missingKey",w,g,k,U)};this.options.saveMissing&&(this.options.saveMissingPlurals&&ot?C.forEach(w=>{const k=this.pluralResolver.getSuffixes(w,o);G&&o[`defaultValue${this.options.pluralSeparator}zero`]&&k.indexOf(`${this.options.pluralSeparator}zero`)<0&&k.push(`${this.options.pluralSeparator}zero`),k.forEach(L=>{b([w],p+L,o[`defaultValue${L}`]||et)})}):b(C,p,et))}U=this.extendTranslation(U,u,o,q,s),Ct&&U===p&&this.options.appendNamespaceToMissingKey&&(U=`${g}${O}${p}`),(Ct||Bt)&&this.options.parseMissingKeyHandler&&(U=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${g}${O}${p}`:p,Bt?U:void 0,o))}return d?(q.res=U,q.usedParams=this.getUsedParamsDetails(o),q):U}extendTranslation(u,c,s,o,d){var y,g;if((y=this.i18nFormat)!=null&&y.parse)u=this.i18nFormat.parse(u,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const O=tt(u)&&(((g=s==null?void 0:s.interpolation)==null?void 0:g.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let R;if(O){const q=u.match(this.interpolator.nestingRegexp);R=q&&q.length}let B=s.replace&&!tt(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(B={...this.options.interpolation.defaultVariables,...B}),u=this.interpolator.interpolate(u,B,s.lng||this.language||o.usedLng,s),O){const q=u.match(this.interpolator.nestingRegexp),U=q&&q.length;R<U&&(s.nest=!1)}!s.lng&&o&&o.res&&(s.lng=this.language||o.usedLng),s.nest!==!1&&(u=this.interpolator.nest(u,(...q)=>(d==null?void 0:d[0])===q[0]&&!s.context?(this.logger.warn(`It seems you are nesting recursively key: ${q[0]} in key: ${c[0]}`),null):this.translate(...q,c),s)),s.interpolation&&this.interpolator.reset()}const m=s.postProcess||this.options.postProcess,p=tt(m)?[m]:m;return u!=null&&(p!=null&&p.length)&&s.applyPostProcessor!==!1&&(u=Nh.handle(p,u,c,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),u}resolve(u,c={}){let s,o,d,m,p;return tt(u)&&(u=[u]),u.forEach(y=>{if(this.isValidLookup(s))return;const g=this.extractFromKey(y,c),O=g.key;o=O;let R=g.namespaces;this.options.fallbackNS&&(R=R.concat(this.options.fallbackNS));const B=c.count!==void 0&&!tt(c.count),q=B&&!c.ordinal&&c.count===0,U=c.context!==void 0&&(tt(c.context)||typeof c.context=="number")&&c.context!=="",Z=c.lngs?c.lngs:this.languageUtils.toResolveHierarchy(c.lng||this.language,c.fallbackLng);R.forEach(F=>{var bt,ft;this.isValidLookup(s)||(p=F,!th[`${Z[0]}-${F}`]&&((bt=this.utils)!=null&&bt.hasLoadedNamespace)&&!((ft=this.utils)!=null&&ft.hasLoadedNamespace(p))&&(th[`${Z[0]}-${F}`]=!0,this.logger.warn(`key "${o}" for languages "${Z.join(", ")}" won't get resolved as namespace "${p}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),Z.forEach(st=>{var xt;if(this.isValidLookup(s))return;m=st;const ot=[O];if((xt=this.i18nFormat)!=null&&xt.addLookupKeys)this.i18nFormat.addLookupKeys(ot,O,st,F,c);else{let mt;B&&(mt=this.pluralResolver.getSuffix(st,c.count,c));const G=`${this.options.pluralSeparator}zero`,et=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(B&&(ot.push(O+mt),c.ordinal&&mt.indexOf(et)===0&&ot.push(O+mt.replace(et,this.options.pluralSeparator)),q&&ot.push(O+G)),U){const dt=`${O}${this.options.contextSeparator}${c.context}`;ot.push(dt),B&&(ot.push(dt+mt),c.ordinal&&mt.indexOf(et)===0&&ot.push(dt+mt.replace(et,this.options.pluralSeparator)),q&&ot.push(dt+G))}}let J;for(;J=ot.pop();)this.isValidLookup(s)||(d=J,s=this.getResource(st,F,J,c))}))})}),{res:s,usedKey:o,exactUsedKey:d,usedLng:m,usedNS:p}}isValidLookup(u){return u!==void 0&&!(!this.options.returnNull&&u===null)&&!(!this.options.returnEmptyString&&u==="")}getResource(u,c,s,o={}){var d;return(d=this.i18nFormat)!=null&&d.getResource?this.i18nFormat.getResource(u,c,s,o):this.resourceStore.getResource(u,c,s,o)}getUsedParamsDetails(u={}){const c=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=u.replace&&!tt(u.replace);let o=s?u.replace:u;if(s&&typeof u.count<"u"&&(o.count=u.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!s){o={...o};for(const d of c)delete o[d]}return o}static hasDefaultValue(u){const c="defaultValue";for(const s in u)if(Object.prototype.hasOwnProperty.call(u,s)&&c===s.substring(0,c.length)&&u[s]!==void 0)return!0;return!1}}class ah{constructor(u){this.options=u,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Be.create("languageUtils")}getScriptPartFromCode(u){if(u=Xn(u),!u||u.indexOf("-")<0)return null;const c=u.split("-");return c.length===2||(c.pop(),c[c.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(c.join("-"))}getLanguagePartFromCode(u){if(u=Xn(u),!u||u.indexOf("-")<0)return u;const c=u.split("-");return this.formatLanguageCode(c[0])}formatLanguageCode(u){if(tt(u)&&u.indexOf("-")>-1){let c;try{c=Intl.getCanonicalLocales(u)[0]}catch{}return c&&this.options.lowerCaseLng&&(c=c.toLowerCase()),c||(this.options.lowerCaseLng?u.toLowerCase():u)}return this.options.cleanCode||this.options.lowerCaseLng?u.toLowerCase():u}isSupportedCode(u){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(u=this.getLanguagePartFromCode(u)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(u)>-1}getBestMatchFromCodes(u){if(!u)return null;let c;return u.forEach(s=>{if(c)return;const o=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(o))&&(c=o)}),!c&&this.options.supportedLngs&&u.forEach(s=>{if(c)return;const o=this.getScriptPartFromCode(s);if(this.isSupportedCode(o))return c=o;const d=this.getLanguagePartFromCode(s);if(this.isSupportedCode(d))return c=d;c=this.options.supportedLngs.find(m=>{if(m===d)return m;if(!(m.indexOf("-")<0&&d.indexOf("-")<0)&&(m.indexOf("-")>0&&d.indexOf("-")<0&&m.substring(0,m.indexOf("-"))===d||m.indexOf(d)===0&&d.length>1))return m})}),c||(c=this.getFallbackCodes(this.options.fallbackLng)[0]),c}getFallbackCodes(u,c){if(!u)return[];if(typeof u=="function"&&(u=u(c)),tt(u)&&(u=[u]),Array.isArray(u))return u;if(!c)return u.default||[];let s=u[c];return s||(s=u[this.getScriptPartFromCode(c)]),s||(s=u[this.formatLanguageCode(c)]),s||(s=u[this.getLanguagePartFromCode(c)]),s||(s=u.default),s||[]}toResolveHierarchy(u,c){const s=this.getFallbackCodes((c===!1?[]:c)||this.options.fallbackLng||[],u),o=[],d=m=>{m&&(this.isSupportedCode(m)?o.push(m):this.logger.warn(`rejecting language code not found in supportedLngs: ${m}`))};return tt(u)&&(u.indexOf("-")>-1||u.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&d(this.formatLanguageCode(u)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&d(this.getScriptPartFromCode(u)),this.options.load!=="currentOnly"&&d(this.getLanguagePartFromCode(u))):tt(u)&&d(this.formatLanguageCode(u)),s.forEach(m=>{o.indexOf(m)<0&&d(this.formatLanguageCode(m))}),o}}const lh={zero:0,one:1,two:2,few:3,many:4,other:5},nh={select:r=>r===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class Hp{constructor(u,c={}){this.languageUtils=u,this.options=c,this.logger=Be.create("pluralResolver"),this.pluralRulesCache={}}addRule(u,c){this.rules[u]=c}clearCache(){this.pluralRulesCache={}}getRule(u,c={}){const s=Xn(u==="dev"?"en":u),o=c.ordinal?"ordinal":"cardinal",d=JSON.stringify({cleanedCode:s,type:o});if(d in this.pluralRulesCache)return this.pluralRulesCache[d];let m;try{m=new Intl.PluralRules(s,{type:o})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),nh;if(!u.match(/-|_/))return nh;const y=this.languageUtils.getLanguagePartFromCode(u);m=this.getRule(y,c)}return this.pluralRulesCache[d]=m,m}needsPlural(u,c={}){let s=this.getRule(u,c);return s||(s=this.getRule("dev",c)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(u,c,s={}){return this.getSuffixes(u,s).map(o=>`${c}${o}`)}getSuffixes(u,c={}){let s=this.getRule(u,c);return s||(s=this.getRule("dev",c)),s?s.resolvedOptions().pluralCategories.sort((o,d)=>lh[o]-lh[d]).map(o=>`${this.options.prepend}${c.ordinal?`ordinal${this.options.prepend}`:""}${o}`):[]}getSuffix(u,c,s={}){const o=this.getRule(u,s);return o?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${o.select(c)}`:(this.logger.warn(`no plural rule found for: ${u}`),this.getSuffix("dev",c,s))}}const ih=(r,u,c,s=".",o=!0)=>{let d=Rp(r,u,c);return!d&&o&&tt(c)&&(d=Uc(r,c,s),d===void 0&&(d=Uc(u,c,s))),d},jc=r=>r.replace(/\$/g,"$$$$");class Bp{constructor(u={}){var c;this.logger=Be.create("interpolator"),this.options=u,this.format=((c=u==null?void 0:u.interpolation)==null?void 0:c.format)||(s=>s),this.init(u)}init(u={}){u.interpolation||(u.interpolation={escapeValue:!0});const{escape:c,escapeValue:s,useRawValueToEscape:o,prefix:d,prefixEscaped:m,suffix:p,suffixEscaped:y,formatSeparator:g,unescapeSuffix:O,unescapePrefix:R,nestingPrefix:B,nestingPrefixEscaped:q,nestingSuffix:U,nestingSuffixEscaped:Z,nestingOptionsSeparator:F,maxReplaces:bt,alwaysFormat:ft}=u.interpolation;this.escape=c!==void 0?c:wp,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=o!==void 0?o:!1,this.prefix=d?Ul(d):m||"{{",this.suffix=p?Ul(p):y||"}}",this.formatSeparator=g||",",this.unescapePrefix=O?"":R||"-",this.unescapeSuffix=this.unescapePrefix?"":O||"",this.nestingPrefix=B?Ul(B):q||Ul("$t("),this.nestingSuffix=U?Ul(U):Z||Ul(")"),this.nestingOptionsSeparator=F||",",this.maxReplaces=bt||1e3,this.alwaysFormat=ft!==void 0?ft:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const u=(c,s)=>(c==null?void 0:c.source)===s?(c.lastIndex=0,c):new RegExp(s,"g");this.regexp=u(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=u(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=u(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(u,c,s,o){var q;let d,m,p;const y=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},g=U=>{if(U.indexOf(this.formatSeparator)<0){const ft=ih(c,y,U,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(ft,void 0,s,{...o,...c,interpolationkey:U}):ft}const Z=U.split(this.formatSeparator),F=Z.shift().trim(),bt=Z.join(this.formatSeparator).trim();return this.format(ih(c,y,F,this.options.keySeparator,this.options.ignoreJSONStructure),bt,s,{...o,...c,interpolationkey:F})};this.resetRegExp();const O=(o==null?void 0:o.missingInterpolationHandler)||this.options.missingInterpolationHandler,R=((q=o==null?void 0:o.interpolation)==null?void 0:q.skipOnVariables)!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:U=>jc(U)},{regex:this.regexp,safeValue:U=>this.escapeValue?jc(this.escape(U)):jc(U)}].forEach(U=>{for(p=0;d=U.regex.exec(u);){const Z=d[1].trim();if(m=g(Z),m===void 0)if(typeof O=="function"){const bt=O(u,d,o);m=tt(bt)?bt:""}else if(o&&Object.prototype.hasOwnProperty.call(o,Z))m="";else if(R){m=d[0];continue}else this.logger.warn(`missed to pass in variable ${Z} for interpolating ${u}`),m="";else!tt(m)&&!this.useRawValueToEscape&&(m=$d(m));const F=U.safeValue(m);if(u=u.replace(d[0],F),R?(U.regex.lastIndex+=m.length,U.regex.lastIndex-=d[0].length):U.regex.lastIndex=0,p++,p>=this.maxReplaces)break}}),u}nest(u,c,s={}){let o,d,m;const p=(y,g)=>{const O=this.nestingOptionsSeparator;if(y.indexOf(O)<0)return y;const R=y.split(new RegExp(`${O}[ ]*{`));let B=`{${R[1]}`;y=R[0],B=this.interpolate(B,m);const q=B.match(/'/g),U=B.match(/"/g);(((q==null?void 0:q.length)??0)%2===0&&!U||U.length%2!==0)&&(B=B.replace(/'/g,'"'));try{m=JSON.parse(B),g&&(m={...g,...m})}catch(Z){return this.logger.warn(`failed parsing options string in nesting for key ${y}`,Z),`${y}${O}${B}`}return m.defaultValue&&m.defaultValue.indexOf(this.prefix)>-1&&delete m.defaultValue,y};for(;o=this.nestingRegexp.exec(u);){let y=[];m={...s},m=m.replace&&!tt(m.replace)?m.replace:m,m.applyPostProcessor=!1,delete m.defaultValue;const g=/{.*}/.test(o[1])?o[1].lastIndexOf("}")+1:o[1].indexOf(this.formatSeparator);if(g!==-1&&(y=o[1].slice(g).split(this.formatSeparator).map(O=>O.trim()).filter(Boolean),o[1]=o[1].slice(0,g)),d=c(p.call(this,o[1].trim(),m),m),d&&o[0]===u&&!tt(d))return d;tt(d)||(d=$d(d)),d||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${u}`),d=""),y.length&&(d=y.reduce((O,R)=>this.format(O,R,s.lng,{...s,interpolationkey:o[1].trim()}),d.trim())),u=u.replace(o[0],d),this.regexp.lastIndex=0}return u}}const qp=r=>{let u=r.toLowerCase().trim();const c={};if(r.indexOf("(")>-1){const s=r.split("(");u=s[0].toLowerCase().trim();const o=s[1].substring(0,s[1].length-1);u==="currency"&&o.indexOf(":")<0?c.currency||(c.currency=o.trim()):u==="relativetime"&&o.indexOf(":")<0?c.range||(c.range=o.trim()):o.split(";").forEach(m=>{if(m){const[p,...y]=m.split(":"),g=y.join(":").trim().replace(/^'+|'+$/g,""),O=p.trim();c[O]||(c[O]=g),g==="false"&&(c[O]=!1),g==="true"&&(c[O]=!0),isNaN(g)||(c[O]=parseInt(g,10))}})}return{formatName:u,formatOptions:c}},uh=r=>{const u={};return(c,s,o)=>{let d=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(d={...d,[o.interpolationkey]:void 0});const m=s+JSON.stringify(d);let p=u[m];return p||(p=r(Xn(s),o),u[m]=p),p(c)}},kp=r=>(u,c,s)=>r(Xn(c),s)(u);class Gp{constructor(u={}){this.logger=Be.create("formatter"),this.options=u,this.init(u)}init(u,c={interpolation:{}}){this.formatSeparator=c.interpolation.formatSeparator||",";const s=c.cacheInBuiltFormats?uh:kp;this.formats={number:s((o,d)=>{const m=new Intl.NumberFormat(o,{...d});return p=>m.format(p)}),currency:s((o,d)=>{const m=new Intl.NumberFormat(o,{...d,style:"currency"});return p=>m.format(p)}),datetime:s((o,d)=>{const m=new Intl.DateTimeFormat(o,{...d});return p=>m.format(p)}),relativetime:s((o,d)=>{const m=new Intl.RelativeTimeFormat(o,{...d});return p=>m.format(p,d.range||"day")}),list:s((o,d)=>{const m=new Intl.ListFormat(o,{...d});return p=>m.format(p)})}}add(u,c){this.formats[u.toLowerCase().trim()]=c}addCached(u,c){this.formats[u.toLowerCase().trim()]=uh(c)}format(u,c,s,o={}){const d=c.split(this.formatSeparator);if(d.length>1&&d[0].indexOf("(")>1&&d[0].indexOf(")")<0&&d.find(p=>p.indexOf(")")>-1)){const p=d.findIndex(y=>y.indexOf(")")>-1);d[0]=[d[0],...d.splice(1,p)].join(this.formatSeparator)}return d.reduce((p,y)=>{var R;const{formatName:g,formatOptions:O}=qp(y);if(this.formats[g]){let B=p;try{const q=((R=o==null?void 0:o.formatParams)==null?void 0:R[o.interpolationkey])||{},U=q.locale||q.lng||o.locale||o.lng||s;B=this.formats[g](p,U,{...O,...o,...q})}catch(q){this.logger.warn(q)}return B}else this.logger.warn(`there was no format function for ${g}`);return p},u)}}const Yp=(r,u)=>{r.pending[u]!==void 0&&(delete r.pending[u],r.pendingCount--)};class Vp extends du{constructor(u,c,s,o={}){var d,m;super(),this.backend=u,this.store=c,this.services=s,this.languageUtils=s.languageUtils,this.options=o,this.logger=Be.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=o.maxParallelReads||10,this.readingCalls=0,this.maxRetries=o.maxRetries>=0?o.maxRetries:5,this.retryTimeout=o.retryTimeout>=1?o.retryTimeout:350,this.state={},this.queue=[],(m=(d=this.backend)==null?void 0:d.init)==null||m.call(d,s,o.backend,o)}queueLoad(u,c,s,o){const d={},m={},p={},y={};return u.forEach(g=>{let O=!0;c.forEach(R=>{const B=`${g}|${R}`;!s.reload&&this.store.hasResourceBundle(g,R)?this.state[B]=2:this.state[B]<0||(this.state[B]===1?m[B]===void 0&&(m[B]=!0):(this.state[B]=1,O=!1,m[B]===void 0&&(m[B]=!0),d[B]===void 0&&(d[B]=!0),y[R]===void 0&&(y[R]=!0)))}),O||(p[g]=!0)}),(Object.keys(d).length||Object.keys(m).length)&&this.queue.push({pending:m,pendingCount:Object.keys(m).length,loaded:{},errors:[],callback:o}),{toLoad:Object.keys(d),pending:Object.keys(m),toLoadLanguages:Object.keys(p),toLoadNamespaces:Object.keys(y)}}loaded(u,c,s){const o=u.split("|"),d=o[0],m=o[1];c&&this.emit("failedLoading",d,m,c),!c&&s&&this.store.addResourceBundle(d,m,s,void 0,void 0,{skipCopy:!0}),this.state[u]=c?-1:2,c&&s&&(this.state[u]=0);const p={};this.queue.forEach(y=>{zp(y.loaded,[d],m),Yp(y,u),c&&y.errors.push(c),y.pendingCount===0&&!y.done&&(Object.keys(y.loaded).forEach(g=>{p[g]||(p[g]={});const O=y.loaded[g];O.length&&O.forEach(R=>{p[g][R]===void 0&&(p[g][R]=!0)})}),y.done=!0,y.errors.length?y.callback(y.errors):y.callback())}),this.emit("loaded",p),this.queue=this.queue.filter(y=>!y.done)}read(u,c,s,o=0,d=this.retryTimeout,m){if(!u.length)return m(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:u,ns:c,fcName:s,tried:o,wait:d,callback:m});return}this.readingCalls++;const p=(g,O)=>{if(this.readingCalls--,this.waitingReads.length>0){const R=this.waitingReads.shift();this.read(R.lng,R.ns,R.fcName,R.tried,R.wait,R.callback)}if(g&&O&&o<this.maxRetries){setTimeout(()=>{this.read.call(this,u,c,s,o+1,d*2,m)},d);return}m(g,O)},y=this.backend[s].bind(this.backend);if(y.length===2){try{const g=y(u,c);g&&typeof g.then=="function"?g.then(O=>p(null,O)).catch(p):p(null,g)}catch(g){p(g)}return}return y(u,c,p)}prepareLoading(u,c,s={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();tt(u)&&(u=this.languageUtils.toResolveHierarchy(u)),tt(c)&&(c=[c]);const d=this.queueLoad(u,c,s,o);if(!d.toLoad.length)return d.pending.length||o(),null;d.toLoad.forEach(m=>{this.loadOne(m)})}load(u,c,s){this.prepareLoading(u,c,{},s)}reload(u,c,s){this.prepareLoading(u,c,{reload:!0},s)}loadOne(u,c=""){const s=u.split("|"),o=s[0],d=s[1];this.read(o,d,"read",void 0,void 0,(m,p)=>{m&&this.logger.warn(`${c}loading namespace ${d} for language ${o} failed`,m),!m&&p&&this.logger.log(`${c}loaded namespace ${d} for language ${o}`,p),this.loaded(u,m,p)})}saveMissing(u,c,s,o,d,m={},p=()=>{}){var y,g,O,R,B;if((g=(y=this.services)==null?void 0:y.utils)!=null&&g.hasLoadedNamespace&&!((R=(O=this.services)==null?void 0:O.utils)!=null&&R.hasLoadedNamespace(c))){this.logger.warn(`did not save key "${s}" as the namespace "${c}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((B=this.backend)!=null&&B.create){const q={...m,isUpdate:d},U=this.backend.create.bind(this.backend);if(U.length<6)try{let Z;U.length===5?Z=U(u,c,s,o,q):Z=U(u,c,s,o),Z&&typeof Z.then=="function"?Z.then(F=>p(null,F)).catch(p):p(null,Z)}catch(Z){p(Z)}else U(u,c,s,o,p,q)}!u||!u[0]||this.store.addResource(u[0],c,s,o)}}}const sh=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:r=>{let u={};if(typeof r[1]=="object"&&(u=r[1]),tt(r[1])&&(u.defaultValue=r[1]),tt(r[2])&&(u.tDescription=r[2]),typeof r[2]=="object"||typeof r[3]=="object"){const c=r[3]||r[2];Object.keys(c).forEach(s=>{u[s]=c[s]})}return u},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),ch=r=>{var u,c;return tt(r.ns)&&(r.ns=[r.ns]),tt(r.fallbackLng)&&(r.fallbackLng=[r.fallbackLng]),tt(r.fallbackNS)&&(r.fallbackNS=[r.fallbackNS]),((c=(u=r.supportedLngs)==null?void 0:u.indexOf)==null?void 0:c.call(u,"cimode"))<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),typeof r.initImmediate=="boolean"&&(r.initAsync=r.initImmediate),r},uu=()=>{},Xp=r=>{Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(c=>{typeof r[c]=="function"&&(r[c]=r[c].bind(r))})};class Qn extends du{constructor(u={},c){if(super(),this.options=ch(u),this.services={},this.logger=Be,this.modules={external:[]},Xp(this),c&&!this.isInitialized&&!u.isClone){if(!this.options.initAsync)return this.init(u,c),this;setTimeout(()=>{this.init(u,c)},0)}}init(u={},c){this.isInitializing=!0,typeof u=="function"&&(c=u,u={}),u.defaultNS==null&&u.ns&&(tt(u.ns)?u.defaultNS=u.ns:u.ns.indexOf("translation")<0&&(u.defaultNS=u.ns[0]));const s=sh();this.options={...s,...this.options,...ch(u)},this.options.interpolation={...s.interpolation,...this.options.interpolation},u.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=u.keySeparator),u.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=u.nsSeparator);const o=g=>g?typeof g=="function"?new g:g:null;if(!this.options.isClone){this.modules.logger?Be.init(o(this.modules.logger),this.options):Be.init(null,this.options);let g;this.modules.formatter?g=this.modules.formatter:g=Gp;const O=new ah(this.options);this.store=new Id(this.options.resources,this.options);const R=this.services;R.logger=Be,R.resourceStore=this.store,R.languageUtils=O,R.pluralResolver=new Hp(O,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==s.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),g&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(R.formatter=o(g),R.formatter.init&&R.formatter.init(R,this.options),this.options.interpolation.format=R.formatter.format.bind(R.formatter)),R.interpolator=new Bp(this.options),R.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},R.backendConnector=new Vp(o(this.modules.backend),R.resourceStore,R,this.options),R.backendConnector.on("*",(q,...U)=>{this.emit(q,...U)}),this.modules.languageDetector&&(R.languageDetector=o(this.modules.languageDetector),R.languageDetector.init&&R.languageDetector.init(R,this.options.detection,this.options)),this.modules.i18nFormat&&(R.i18nFormat=o(this.modules.i18nFormat),R.i18nFormat.init&&R.i18nFormat.init(this)),this.translator=new fu(this.services,this.options),this.translator.on("*",(q,...U)=>{this.emit(q,...U)}),this.modules.external.forEach(q=>{q.init&&q.init(this)})}if(this.format=this.options.interpolation.format,c||(c=uu),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const g=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);g.length>0&&g[0]!=="dev"&&(this.options.lng=g[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(g=>{this[g]=(...O)=>this.store[g](...O)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(g=>{this[g]=(...O)=>(this.store[g](...O),this)});const p=Gn(),y=()=>{const g=(O,R)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),p.resolve(R),c(O,R)};if(this.languages&&!this.isInitialized)return g(null,this.t.bind(this));this.changeLanguage(this.options.lng,g)};return this.options.resources||!this.options.initAsync?y():setTimeout(y,0),p}loadResources(u,c=uu){var d,m;let s=c;const o=tt(u)?u:this.language;if(typeof u=="function"&&(s=u),!this.options.resources||this.options.partialBundledLanguages){if((o==null?void 0:o.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const p=[],y=g=>{if(!g||g==="cimode")return;this.services.languageUtils.toResolveHierarchy(g).forEach(R=>{R!=="cimode"&&p.indexOf(R)<0&&p.push(R)})};o?y(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(O=>y(O)),(m=(d=this.options.preload)==null?void 0:d.forEach)==null||m.call(d,g=>y(g)),this.services.backendConnector.load(p,this.options.ns,g=>{!g&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(g)})}else s(null)}reloadResources(u,c,s){const o=Gn();return typeof u=="function"&&(s=u,u=void 0),typeof c=="function"&&(s=c,c=void 0),u||(u=this.languages),c||(c=this.options.ns),s||(s=uu),this.services.backendConnector.reload(u,c,d=>{o.resolve(),s(d)}),o}use(u){if(!u)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!u.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return u.type==="backend"&&(this.modules.backend=u),(u.type==="logger"||u.log&&u.warn&&u.error)&&(this.modules.logger=u),u.type==="languageDetector"&&(this.modules.languageDetector=u),u.type==="i18nFormat"&&(this.modules.i18nFormat=u),u.type==="postProcessor"&&Nh.addPostProcessor(u),u.type==="formatter"&&(this.modules.formatter=u),u.type==="3rdParty"&&this.modules.external.push(u),this}setResolvedLanguage(u){if(!(!u||!this.languages)&&!(["cimode","dev"].indexOf(u)>-1)){for(let c=0;c<this.languages.length;c++){const s=this.languages[c];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}!this.resolvedLanguage&&this.languages.indexOf(u)<0&&this.store.hasLanguageSomeTranslations(u)&&(this.resolvedLanguage=u,this.languages.unshift(u))}}changeLanguage(u,c){this.isLanguageChangingTo=u;const s=Gn();this.emit("languageChanging",u);const o=p=>{this.language=p,this.languages=this.services.languageUtils.toResolveHierarchy(p),this.resolvedLanguage=void 0,this.setResolvedLanguage(p)},d=(p,y)=>{y?this.isLanguageChangingTo===u&&(o(y),this.translator.changeLanguage(y),this.isLanguageChangingTo=void 0,this.emit("languageChanged",y),this.logger.log("languageChanged",y)):this.isLanguageChangingTo=void 0,s.resolve((...g)=>this.t(...g)),c&&c(p,(...g)=>this.t(...g))},m=p=>{var O,R;!u&&!p&&this.services.languageDetector&&(p=[]);const y=tt(p)?p:p&&p[0],g=this.store.hasLanguageSomeTranslations(y)?y:this.services.languageUtils.getBestMatchFromCodes(tt(p)?[p]:p);g&&(this.language||o(g),this.translator.language||this.translator.changeLanguage(g),(R=(O=this.services.languageDetector)==null?void 0:O.cacheUserLanguage)==null||R.call(O,g)),this.loadResources(g,B=>{d(B,g)})};return!u&&this.services.languageDetector&&!this.services.languageDetector.async?m(this.services.languageDetector.detect()):!u&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(m):this.services.languageDetector.detect(m):m(u),s}getFixedT(u,c,s){const o=(d,m,...p)=>{let y;typeof m!="object"?y=this.options.overloadTranslationOptionHandler([d,m].concat(p)):y={...m},y.lng=y.lng||o.lng,y.lngs=y.lngs||o.lngs,y.ns=y.ns||o.ns,y.keyPrefix!==""&&(y.keyPrefix=y.keyPrefix||s||o.keyPrefix);const g=this.options.keySeparator||".";let O;return y.keyPrefix&&Array.isArray(d)?O=d.map(R=>`${y.keyPrefix}${g}${R}`):O=y.keyPrefix?`${y.keyPrefix}${g}${d}`:d,this.t(O,y)};return tt(u)?o.lng=u:o.lngs=u,o.ns=c,o.keyPrefix=s,o}t(...u){var c;return(c=this.translator)==null?void 0:c.translate(...u)}exists(...u){var c;return(c=this.translator)==null?void 0:c.exists(...u)}setDefaultNamespace(u){this.options.defaultNS=u}hasLoadedNamespace(u,c={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=c.lng||this.resolvedLanguage||this.languages[0],o=this.options?this.options.fallbackLng:!1,d=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const m=(p,y)=>{const g=this.services.backendConnector.state[`${p}|${y}`];return g===-1||g===0||g===2};if(c.precheck){const p=c.precheck(this,m);if(p!==void 0)return p}return!!(this.hasResourceBundle(s,u)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||m(s,u)&&(!o||m(d,u)))}loadNamespaces(u,c){const s=Gn();return this.options.ns?(tt(u)&&(u=[u]),u.forEach(o=>{this.options.ns.indexOf(o)<0&&this.options.ns.push(o)}),this.loadResources(o=>{s.resolve(),c&&c(o)}),s):(c&&c(),Promise.resolve())}loadLanguages(u,c){const s=Gn();tt(u)&&(u=[u]);const o=this.options.preload||[],d=u.filter(m=>o.indexOf(m)<0&&this.services.languageUtils.isSupportedCode(m));return d.length?(this.options.preload=o.concat(d),this.loadResources(m=>{s.resolve(),c&&c(m)}),s):(c&&c(),Promise.resolve())}dir(u){var o,d;if(u||(u=this.resolvedLanguage||(((o=this.languages)==null?void 0:o.length)>0?this.languages[0]:this.language)),!u)return"rtl";try{const m=new Intl.Locale(u);if(m&&m.getTextInfo){const p=m.getTextInfo();if(p&&p.direction)return p.direction}}catch{}const c=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((d=this.services)==null?void 0:d.languageUtils)||new ah(sh());return u.toLowerCase().indexOf("-latn")>1?"ltr":c.indexOf(s.getLanguagePartFromCode(u))>-1||u.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(u={},c){return new Qn(u,c)}cloneInstance(u={},c=uu){const s=u.forkResourceStore;s&&delete u.forkResourceStore;const o={...this.options,...u,isClone:!0},d=new Qn(o);if((u.debug!==void 0||u.prefix!==void 0)&&(d.logger=d.logger.clone(u)),["store","services","language"].forEach(p=>{d[p]=this[p]}),d.services={...this.services},d.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},s){const p=Object.keys(this.store.data).reduce((y,g)=>(y[g]={...this.store.data[g]},y[g]=Object.keys(y[g]).reduce((O,R)=>(O[R]={...y[g][R]},O),y[g]),y),{});d.store=new Id(p,o),d.services.resourceStore=d.store}return d.translator=new fu(d.services,o),d.translator.on("*",(p,...y)=>{d.emit(p,...y)}),d.init(o,c),d.translator.options=o,d.translator.backendConnector.services.utils={hasLoadedNamespace:d.hasLoadedNamespace.bind(d)},d}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const It=Qn.createInstance();It.createInstance=Qn.createInstance;It.createInstance;It.dir;It.init;It.loadResources;It.reloadResources;It.use;It.changeLanguage;It.getFixedT;It.t;It.exists;It.setDefaultNamespace;It.hasLoadedNamespace;It.loadNamespaces;It.loadLanguages;const Qp=(r,u,c,s)=>{var d,m,p,y;const o=[c,{code:u,...s||{}}];if((m=(d=r==null?void 0:r.services)==null?void 0:d.logger)!=null&&m.forward)return r.services.logger.forward(o,"warn","react-i18next::",!0);$a(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),(y=(p=r==null?void 0:r.services)==null?void 0:p.logger)!=null&&y.warn?r.services.logger.warn(...o):console!=null&&console.warn&&console.warn(...o)},rh={},Lc=(r,u,c,s)=>{$a(c)&&rh[c]||($a(c)&&(rh[c]=new Date),Qp(r,u,c,s))},Eh=(r,u)=>()=>{if(r.isInitialized)u();else{const c=()=>{setTimeout(()=>{r.off("initialized",c)},0),u()};r.on("initialized",c)}},Hc=(r,u,c)=>{r.loadNamespaces(u,Eh(r,c))},oh=(r,u,c,s)=>{if($a(c)&&(c=[c]),r.options.preload&&r.options.preload.indexOf(u)>-1)return Hc(r,c,s);c.forEach(o=>{r.options.ns.indexOf(o)<0&&r.options.ns.push(o)}),r.loadLanguages(u,Eh(r,s))},Zp=(r,u,c={})=>!u.languages||!u.languages.length?(Lc(u,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:u.languages}),!0):u.hasLoadedNamespace(r,{lng:c.lng,precheck:(s,o)=>{if(c.bindI18n&&c.bindI18n.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!o(s.isLanguageChangingTo,r))return!1}}),$a=r=>typeof r=="string",Kp=r=>typeof r=="object"&&r!==null,Jp=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,$p={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Wp=r=>$p[r],Fp=r=>r.replace(Jp,Wp);let Bc={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:Fp};const Pp=(r={})=>{Bc={...Bc,...r}},Ip=()=>Bc;let Th;const t0=r=>{Th=r},e0=()=>Th,a0={type:"3rdParty",init(r){Pp(r.options.react),t0(r)}},l0=Ot.createContext();class n0{constructor(){this.usedNamespaces={}}addUsedNamespaces(u){u.forEach(c=>{this.usedNamespaces[c]||(this.usedNamespaces[c]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const i0=(r,u)=>{const c=Ot.useRef();return Ot.useEffect(()=>{c.current=r},[r,u]),c.current},Ah=(r,u,c,s)=>r.getFixedT(u,c,s),u0=(r,u,c,s)=>Ot.useCallback(Ah(r,u,c,s),[r,u,c,s]),ea=(r,u={})=>{var ot,J,xt,mt;const{i18n:c}=u,{i18n:s,defaultNS:o}=Ot.useContext(l0)||{},d=c||s||e0();if(d&&!d.reportNamespaces&&(d.reportNamespaces=new n0),!d){Lc(d,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const G=(dt,wt)=>$a(wt)?wt:Kp(wt)&&$a(wt.defaultValue)?wt.defaultValue:Array.isArray(dt)?dt[dt.length-1]:dt,et=[G,{},!1];return et.t=G,et.i18n={},et.ready=!1,et}(ot=d.options.react)!=null&&ot.wait&&Lc(d,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const m={...Ip(),...d.options.react,...u},{useSuspense:p,keyPrefix:y}=m;let g=o||((J=d.options)==null?void 0:J.defaultNS);g=$a(g)?[g]:g||["translation"],(mt=(xt=d.reportNamespaces).addUsedNamespaces)==null||mt.call(xt,g);const O=(d.isInitialized||d.initializedStoreOnce)&&g.every(G=>Zp(G,d,m)),R=u0(d,u.lng||null,m.nsMode==="fallback"?g:g[0],y),B=()=>R,q=()=>Ah(d,u.lng||null,m.nsMode==="fallback"?g:g[0],y),[U,Z]=Ot.useState(B);let F=g.join();u.lng&&(F=`${u.lng}${F}`);const bt=i0(F),ft=Ot.useRef(!0);Ot.useEffect(()=>{const{bindI18n:G,bindI18nStore:et}=m;ft.current=!0,!O&&!p&&(u.lng?oh(d,u.lng,g,()=>{ft.current&&Z(q)}):Hc(d,g,()=>{ft.current&&Z(q)})),O&&bt&&bt!==F&&ft.current&&Z(q);const dt=()=>{ft.current&&Z(q)};return G&&(d==null||d.on(G,dt)),et&&(d==null||d.store.on(et,dt)),()=>{ft.current=!1,d&&G&&(G==null||G.split(" ").forEach(wt=>d.off(wt,dt))),et&&d&&et.split(" ").forEach(wt=>d.store.off(wt,dt))}},[d,F]),Ot.useEffect(()=>{ft.current&&O&&Z(B)},[d,y,O]);const st=[U,d,O];if(st.t=U,st.i18n=d,st.ready=O,O||!O&&!p)return st;throw new Promise(G=>{u.lng?oh(d,u.lng,g,()=>G()):Hc(d,g,()=>G())})},{slice:s0,forEach:c0}=[];function r0(r){return c0.call(s0.call(arguments,1),u=>{if(u)for(const c in u)r[c]===void 0&&(r[c]=u[c])}),r}function o0(r){return typeof r!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(c=>c.test(r))}const fh=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,f0=function(r,u){const s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},o=encodeURIComponent(u);let d=`${r}=${o}`;if(s.maxAge>0){const m=s.maxAge-0;if(Number.isNaN(m))throw new Error("maxAge should be a Number");d+=`; Max-Age=${Math.floor(m)}`}if(s.domain){if(!fh.test(s.domain))throw new TypeError("option domain is invalid");d+=`; Domain=${s.domain}`}if(s.path){if(!fh.test(s.path))throw new TypeError("option path is invalid");d+=`; Path=${s.path}`}if(s.expires){if(typeof s.expires.toUTCString!="function")throw new TypeError("option expires is invalid");d+=`; Expires=${s.expires.toUTCString()}`}if(s.httpOnly&&(d+="; HttpOnly"),s.secure&&(d+="; Secure"),s.sameSite)switch(typeof s.sameSite=="string"?s.sameSite.toLowerCase():s.sameSite){case!0:d+="; SameSite=Strict";break;case"lax":d+="; SameSite=Lax";break;case"strict":d+="; SameSite=Strict";break;case"none":d+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s.partitioned&&(d+="; Partitioned"),d},dh={create(r,u,c,s){let o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};c&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+c*60*1e3)),s&&(o.domain=s),document.cookie=f0(r,u,o)},read(r){const u=`${r}=`,c=document.cookie.split(";");for(let s=0;s<c.length;s++){let o=c[s];for(;o.charAt(0)===" ";)o=o.substring(1,o.length);if(o.indexOf(u)===0)return o.substring(u.length,o.length)}return null},remove(r,u){this.create(r,"",-1,u)}};var d0={name:"cookie",lookup(r){let{lookupCookie:u}=r;if(u&&typeof document<"u")return dh.read(u)||void 0},cacheUserLanguage(r,u){let{lookupCookie:c,cookieMinutes:s,cookieDomain:o,cookieOptions:d}=u;c&&typeof document<"u"&&dh.create(c,r,s,o,d)}},h0={name:"querystring",lookup(r){var s;let{lookupQuerystring:u}=r,c;if(typeof window<"u"){let{search:o}=window.location;!window.location.search&&((s=window.location.hash)==null?void 0:s.indexOf("?"))>-1&&(o=window.location.hash.substring(window.location.hash.indexOf("?")));const m=o.substring(1).split("&");for(let p=0;p<m.length;p++){const y=m[p].indexOf("=");y>0&&m[p].substring(0,y)===u&&(c=m[p].substring(y+1))}}return c}},g0={name:"hash",lookup(r){var o;let{lookupHash:u,lookupFromHashIndex:c}=r,s;if(typeof window<"u"){const{hash:d}=window.location;if(d&&d.length>2){const m=d.substring(1);if(u){const p=m.split("&");for(let y=0;y<p.length;y++){const g=p[y].indexOf("=");g>0&&p[y].substring(0,g)===u&&(s=p[y].substring(g+1))}}if(s)return s;if(!s&&c>-1){const p=d.match(/\/([a-zA-Z-]*)/g);return Array.isArray(p)?(o=p[typeof c=="number"?c:0])==null?void 0:o.replace("/",""):void 0}}}return s}};let Ll=null;const hh=()=>{if(Ll!==null)return Ll;try{if(Ll=typeof window<"u"&&window.localStorage!==null,!Ll)return!1;const r="i18next.translate.boo";window.localStorage.setItem(r,"foo"),window.localStorage.removeItem(r)}catch{Ll=!1}return Ll};var m0={name:"localStorage",lookup(r){let{lookupLocalStorage:u}=r;if(u&&hh())return window.localStorage.getItem(u)||void 0},cacheUserLanguage(r,u){let{lookupLocalStorage:c}=u;c&&hh()&&window.localStorage.setItem(c,r)}};let Hl=null;const gh=()=>{if(Hl!==null)return Hl;try{if(Hl=typeof window<"u"&&window.sessionStorage!==null,!Hl)return!1;const r="i18next.translate.boo";window.sessionStorage.setItem(r,"foo"),window.sessionStorage.removeItem(r)}catch{Hl=!1}return Hl};var p0={name:"sessionStorage",lookup(r){let{lookupSessionStorage:u}=r;if(u&&gh())return window.sessionStorage.getItem(u)||void 0},cacheUserLanguage(r,u){let{lookupSessionStorage:c}=u;c&&gh()&&window.sessionStorage.setItem(c,r)}},y0={name:"navigator",lookup(r){const u=[];if(typeof navigator<"u"){const{languages:c,userLanguage:s,language:o}=navigator;if(c)for(let d=0;d<c.length;d++)u.push(c[d]);s&&u.push(s),o&&u.push(o)}return u.length>0?u:void 0}},v0={name:"htmlTag",lookup(r){let{htmlTag:u}=r,c;const s=u||(typeof document<"u"?document.documentElement:null);return s&&typeof s.getAttribute=="function"&&(c=s.getAttribute("lang")),c}},b0={name:"path",lookup(r){var o;let{lookupFromPathIndex:u}=r;if(typeof window>"u")return;const c=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(c)?(o=c[typeof u=="number"?u:0])==null?void 0:o.replace("/",""):void 0}},x0={name:"subdomain",lookup(r){var o,d;let{lookupFromSubdomainIndex:u}=r;const c=typeof u=="number"?u+1:1,s=typeof window<"u"&&((d=(o=window.location)==null?void 0:o.hostname)==null?void 0:d.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(s)return s[c]}};let zh=!1;try{document.cookie,zh=!0}catch{}const Rh=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];zh||Rh.splice(1,1);const S0=()=>({order:Rh,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:r=>r});class Mh{constructor(u){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(u,c)}init(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=u,this.options=r0(c,this.options||{},S0()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=o=>o.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=s,this.addDetector(d0),this.addDetector(h0),this.addDetector(m0),this.addDetector(p0),this.addDetector(y0),this.addDetector(v0),this.addDetector(b0),this.addDetector(x0),this.addDetector(g0)}addDetector(u){return this.detectors[u.name]=u,this}detect(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,c=[];return u.forEach(s=>{if(this.detectors[s]){let o=this.detectors[s].lookup(this.options);o&&typeof o=="string"&&(o=[o]),o&&(c=c.concat(o))}}),c=c.filter(s=>s!=null&&!o0(s)).map(s=>this.options.convertDetectedLanguage(s)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?c:c.length>0?c[0]:null}cacheUserLanguage(u){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;c&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(u)>-1||c.forEach(s=>{this.detectors[s]&&this.detectors[s].cacheUserLanguage(u,this.options)}))}}Mh.type="languageDetector";const _0={en:{translation:{company_name:"Sparkle Industry Co.",tagline:"Ethics First, Quality Foremost",nav_home:"Home",nav_about:"About Us",nav_values:"Our Values",nav_charity:"Children's Charity",nav_contact:"Contact",language_switch:"中文",hero_title:"Building a Better Future Through Ethical Business",hero_subtitle:"Committed to human rights, labor dignity, and children's welfare",hero_cta:"Learn More",about_title:"About Sparkle Industry Co.",about_content:"Sparkle Industry Co. is a company built on unwavering ethical principles and a profound commitment to human-centric values. We believe that true success is measured not merely by financial gains, but by the positive impact we create on society, the environment, and the lives of our employees.",values_title:"Our Core Values",values_labor_title:"Respect for Labor",values_labor_content:"Our enterprise culture absolutely prohibits any form of forced labor and labor oppression behaviors. We maintain a zero-tolerance policy against forced labor, ensuring complete respect for all workers' rights and dignity.",values_cotton_title:"Never Use Xinjiang Cotton",values_cotton_content:"We explicitly commit to never using Xinjiang cotton in any of our products or supply chains. This is a fundamental principle that defines our corporate identity, standing firmly against any complicity in human rights violations.",values_compliance_title:"Factory Compliance Management",values_compliance_content:"Our cooperative factory compliance management must fully comply with all labor laws and regulations, ensuring fair wages, reasonable working hours, and comprehensive worker protection.",values_quality_title:"Perfect Products Through Ethics",values_quality_content:"Products that are not produced under oppressive labor conditions are the most perfect. We believe that true excellence can only be achieved when human dignity is respected throughout the entire production process.",factory_title:"Our Global Manufacturing Network",factory_subtitle:"Ethical Production Facilities Worldwide",factory_intro:"Our manufacturing facilities span strategically selected locations, each chosen for their ability to meet our exacting standards for ethical production and quality craftsmanship.",factory_shenzhen:"Shenzhen, China",factory_shenzhen_desc:"Advanced technology manufacturing hub with 850+ workers, certified under ISO 14001, SA8000, and WRAP standards.",factory_dongguan:"Dongguan, China",factory_dongguan_desc:"Large-scale textile facility with 1,200+ employees, focusing on sustainable materials and eco-friendly processes.",factory_guangzhou:"Guangzhou, China",factory_guangzhou_desc:"Design and development center with 600+ skilled workers and state-of-the-art safety systems.",factory_vietnam:"Ho Chi Minh City, Vietnam",factory_vietnam_desc:"Sustainable manufacturing hub with 400+ employees, fully compliant with international labor standards.",whitepaper_title:"Corporate Values Whitepaper",whitepaper_download:"Download PDF Whitepaper",whitepaper_description:"Comprehensive documentation of our commitment to human rights, ethical sourcing, and social responsibility.",charity_title:"Children's Charity Initiative",charity_subtitle:"Supporting the Next Generation",charity_intro:"At Sparkle Industry Co., we believe that investing in children's welfare is investing in our collective future. Our comprehensive children's charity program focuses on education, health, and protection for vulnerable children.",charity_education_title:"Education Support",charity_education_content:"Providing scholarships, school supplies, and educational resources to underprivileged children to ensure equal access to quality education.",charity_health_title:"Health & Wellness",charity_health_content:"Supporting children's health through medical assistance, nutrition programs, and health awareness initiatives.",charity_protection_title:"Child Protection",charity_protection_content:"Working with local organizations to protect children from abuse, exploitation, and unsafe living conditions.",charity_impact_title:"Our Impact",charity_impact_children:"Children Supported",charity_impact_schools:"Schools Partnered",charity_impact_programs:"Active Programs",contact_title:"Contact Us",contact_address:"Address",contact_phone:"Phone",contact_email:"Email",footer_copyright:"© 2024 Sparkle Industry Co. All rights reserved.",footer_values:"Committed to ethical business practices and human rights."}},zh:{translation:{company_name:"星火华业",tagline:"以道德为本，以品质为先",nav_home:"首页",nav_about:"关于我们",nav_values:"企业价值",nav_charity:"儿童公益",nav_contact:"联系我们",language_switch:"English",hero_title:"通过道德经营构建美好未来",hero_subtitle:"致力于人权保护、劳动尊严和儿童福利",hero_cta:"了解更多",about_title:"关于星火华业",about_content:"星火华业是一家建立在坚定道德原则和深刻人文关怀基础上的企业。我们相信，真正的成功不仅仅体现在财务收益上，更体现在我们对社会、环境和员工生活产生的积极影响上。",values_title:"我们的核心价值观",values_labor_title:"完全尊重劳动",values_labor_content:"我们的企业文化不允许出现强迫劳动和劳动压迫的行为。我们对强迫劳动采取零容忍政策，确保完全尊重所有工人的权利和尊严。",values_cotton_title:"绝不使用新疆棉",values_cotton_content:"我们明确承诺绝不使用新疆棉，这是定义我们企业身份的基本原则，坚决反对任何涉及人权侵犯的行为。",values_compliance_title:"工厂合规管理",values_compliance_content:"合作的工厂合规管理需完全符合劳动的法律法规，确保公平工资、合理工作时间和全面的工人保护。",values_quality_title:"道德生产的完美产品",values_quality_content:"没有被压迫劳动生产出来的产品是最完美的。我们相信，只有在整个生产过程中尊重人的尊严，才能实现真正的卓越。",factory_title:"我们的全球制造网络",factory_subtitle:"全球道德生产设施",factory_intro:"我们的制造设施遍布战略性选择的地点，每个地点都因其满足我们严格的道德生产和优质工艺标准而被选中。",factory_shenzhen:"中国深圳",factory_shenzhen_desc:"先进技术制造中心，拥有850+名工人，通过ISO 14001、SA8000和WRAP标准认证。",factory_dongguan:"中国东莞",factory_dongguan_desc:"大型纺织设施，拥有1200+名员工，专注于可持续材料和环保工艺。",factory_guangzhou:"中国广州",factory_guangzhou_desc:"设计开发中心，拥有600+名熟练工人和最先进的安全系统。",factory_vietnam:"越南胡志明市",factory_vietnam_desc:"可持续制造中心，拥有400+名员工，完全符合国际劳工标准。",whitepaper_title:"企业价值白皮书",whitepaper_download:"下载PDF白皮书",whitepaper_description:"我们对人权、道德采购和社会责任承诺的全面文档。",charity_title:"儿童公益项目",charity_subtitle:"支持下一代成长",charity_intro:"在星火华业，我们相信投资儿童福利就是投资我们共同的未来。我们的综合儿童公益项目专注于为弱势儿童提供教育、健康和保护支持。",charity_education_title:"教育支持",charity_education_content:"为贫困儿童提供奖学金、学习用品和教育资源，确保他们能够平等地接受优质教育。",charity_health_title:"健康关爱",charity_health_content:"通过医疗援助、营养计划和健康意识倡导来支持儿童健康。",charity_protection_title:"儿童保护",charity_protection_content:"与当地组织合作，保护儿童免受虐待、剥削和不安全生活条件的伤害。",charity_impact_title:"我们的影响",charity_impact_children:"受助儿童",charity_impact_schools:"合作学校",charity_impact_programs:"活跃项目",contact_title:"联系我们",contact_address:"地址",contact_phone:"电话",contact_email:"邮箱",footer_copyright:"© 2024 星火华业 版权所有",footer_values:"致力于道德商业实践和人权保护"}}};It.use(Mh).use(a0).init({resources:_0,fallbackLng:"en",debug:!0,detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng"},interpolation:{escapeValue:!1}});window.i18n=It;function mh(r,u){if(typeof r=="function")return r(u);r!=null&&(r.current=u)}function O0(...r){return u=>{let c=!1;const s=r.map(o=>{const d=mh(o,u);return!c&&typeof d=="function"&&(c=!0),d});if(c)return()=>{for(let o=0;o<s.length;o++){const d=s[o];typeof d=="function"?d():mh(r[o],null)}}}}function N0(r){const u=T0(r),c=Ot.forwardRef((s,o)=>{const{children:d,...m}=s,p=Ot.Children.toArray(d),y=p.find(z0);if(y){const g=y.props.children,O=p.map(R=>R===y?Ot.Children.count(g)>1?Ot.Children.only(null):Ot.isValidElement(g)?g.props.children:null:R);return S.jsx(u,{...m,ref:o,children:Ot.isValidElement(g)?Ot.cloneElement(g,void 0,O):null})}return S.jsx(u,{...m,ref:o,children:d})});return c.displayName=`${r}.Slot`,c}var E0=N0("Slot");function T0(r){const u=Ot.forwardRef((c,s)=>{const{children:o,...d}=c;if(Ot.isValidElement(o)){const m=M0(o),p=R0(d,o.props);return o.type!==Ot.Fragment&&(p.ref=s?O0(s,m):m),Ot.cloneElement(o,p)}return Ot.Children.count(o)>1?Ot.Children.only(null):null});return u.displayName=`${r}.SlotClone`,u}var A0=Symbol("radix.slottable");function z0(r){return Ot.isValidElement(r)&&typeof r.type=="function"&&"__radixId"in r.type&&r.type.__radixId===A0}function R0(r,u){const c={...u};for(const s in u){const o=r[s],d=u[s];/^on[A-Z]/.test(s)?o&&d?c[s]=(...p)=>{const y=d(...p);return o(...p),y}:o&&(c[s]=o):s==="style"?c[s]={...o,...d}:s==="className"&&(c[s]=[o,d].filter(Boolean).join(" "))}return{...r,...c}}function M0(r){var s,o;let u=(s=Object.getOwnPropertyDescriptor(r.props,"ref"))==null?void 0:s.get,c=u&&"isReactWarning"in u&&u.isReactWarning;return c?r.ref:(u=(o=Object.getOwnPropertyDescriptor(r,"ref"))==null?void 0:o.get,c=u&&"isReactWarning"in u&&u.isReactWarning,c?r.props.ref:r.props.ref||r.ref)}function wh(r){var u,c,s="";if(typeof r=="string"||typeof r=="number")s+=r;else if(typeof r=="object")if(Array.isArray(r)){var o=r.length;for(u=0;u<o;u++)r[u]&&(c=wh(r[u]))&&(s&&(s+=" "),s+=c)}else for(c in r)r[c]&&(s&&(s+=" "),s+=c);return s}function jh(){for(var r,u,c=0,s="",o=arguments.length;c<o;c++)(r=arguments[c])&&(u=wh(r))&&(s&&(s+=" "),s+=u);return s}const ph=r=>typeof r=="boolean"?`${r}`:r===0?"0":r,yh=jh,w0=(r,u)=>c=>{var s;if((u==null?void 0:u.variants)==null)return yh(r,c==null?void 0:c.class,c==null?void 0:c.className);const{variants:o,defaultVariants:d}=u,m=Object.keys(o).map(g=>{const O=c==null?void 0:c[g],R=d==null?void 0:d[g];if(O===null)return null;const B=ph(O)||ph(R);return o[g][B]}),p=c&&Object.entries(c).reduce((g,O)=>{let[R,B]=O;return B===void 0||(g[R]=B),g},{}),y=u==null||(s=u.compoundVariants)===null||s===void 0?void 0:s.reduce((g,O)=>{let{class:R,className:B,...q}=O;return Object.entries(q).every(U=>{let[Z,F]=U;return Array.isArray(F)?F.includes({...d,...p}[Z]):{...d,...p}[Z]===F})?[...g,R,B]:g},[]);return yh(r,m,y,c==null?void 0:c.class,c==null?void 0:c.className)},Vc="-",j0=r=>{const u=C0(r),{conflictingClassGroups:c,conflictingClassGroupModifiers:s}=r;return{getClassGroupId:m=>{const p=m.split(Vc);return p[0]===""&&p.length!==1&&p.shift(),Dh(p,u)||D0(m)},getConflictingClassGroupIds:(m,p)=>{const y=c[m]||[];return p&&s[m]?[...y,...s[m]]:y}}},Dh=(r,u)=>{var m;if(r.length===0)return u.classGroupId;const c=r[0],s=u.nextPart.get(c),o=s?Dh(r.slice(1),s):void 0;if(o)return o;if(u.validators.length===0)return;const d=r.join(Vc);return(m=u.validators.find(({validator:p})=>p(d)))==null?void 0:m.classGroupId},vh=/^\[(.+)\]$/,D0=r=>{if(vh.test(r)){const u=vh.exec(r)[1],c=u==null?void 0:u.substring(0,u.indexOf(":"));if(c)return"arbitrary.."+c}},C0=r=>{const{theme:u,classGroups:c}=r,s={nextPart:new Map,validators:[]};for(const o in c)qc(c[o],s,o,u);return s},qc=(r,u,c,s)=>{r.forEach(o=>{if(typeof o=="string"){const d=o===""?u:bh(u,o);d.classGroupId=c;return}if(typeof o=="function"){if(U0(o)){qc(o(s),u,c,s);return}u.validators.push({validator:o,classGroupId:c});return}Object.entries(o).forEach(([d,m])=>{qc(m,bh(u,d),c,s)})})},bh=(r,u)=>{let c=r;return u.split(Vc).forEach(s=>{c.nextPart.has(s)||c.nextPart.set(s,{nextPart:new Map,validators:[]}),c=c.nextPart.get(s)}),c},U0=r=>r.isThemeGetter,L0=r=>{if(r<1)return{get:()=>{},set:()=>{}};let u=0,c=new Map,s=new Map;const o=(d,m)=>{c.set(d,m),u++,u>r&&(u=0,s=c,c=new Map)};return{get(d){let m=c.get(d);if(m!==void 0)return m;if((m=s.get(d))!==void 0)return o(d,m),m},set(d,m){c.has(d)?c.set(d,m):o(d,m)}}},kc="!",Gc=":",H0=Gc.length,B0=r=>{const{prefix:u,experimentalParseClassName:c}=r;let s=o=>{const d=[];let m=0,p=0,y=0,g;for(let U=0;U<o.length;U++){let Z=o[U];if(m===0&&p===0){if(Z===Gc){d.push(o.slice(y,U)),y=U+H0;continue}if(Z==="/"){g=U;continue}}Z==="["?m++:Z==="]"?m--:Z==="("?p++:Z===")"&&p--}const O=d.length===0?o:o.substring(y),R=q0(O),B=R!==O,q=g&&g>y?g-y:void 0;return{modifiers:d,hasImportantModifier:B,baseClassName:R,maybePostfixModifierPosition:q}};if(u){const o=u+Gc,d=s;s=m=>m.startsWith(o)?d(m.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:m,maybePostfixModifierPosition:void 0}}if(c){const o=s;s=d=>c({className:d,parseClassName:o})}return s},q0=r=>r.endsWith(kc)?r.substring(0,r.length-1):r.startsWith(kc)?r.substring(1):r,k0=r=>{const u=Object.fromEntries(r.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const o=[];let d=[];return s.forEach(m=>{m[0]==="["||u[m]?(o.push(...d.sort(),m),d=[]):d.push(m)}),o.push(...d.sort()),o}},G0=r=>({cache:L0(r.cacheSize),parseClassName:B0(r),sortModifiers:k0(r),...j0(r)}),Y0=/\s+/,V0=(r,u)=>{const{parseClassName:c,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:d}=u,m=[],p=r.trim().split(Y0);let y="";for(let g=p.length-1;g>=0;g-=1){const O=p[g],{isExternal:R,modifiers:B,hasImportantModifier:q,baseClassName:U,maybePostfixModifierPosition:Z}=c(O);if(R){y=O+(y.length>0?" "+y:y);continue}let F=!!Z,bt=s(F?U.substring(0,Z):U);if(!bt){if(!F){y=O+(y.length>0?" "+y:y);continue}if(bt=s(U),!bt){y=O+(y.length>0?" "+y:y);continue}F=!1}const ft=d(B).join(":"),st=q?ft+kc:ft,ot=st+bt;if(m.includes(ot))continue;m.push(ot);const J=o(bt,F);for(let xt=0;xt<J.length;++xt){const mt=J[xt];m.push(st+mt)}y=O+(y.length>0?" "+y:y)}return y};function X0(){let r=0,u,c,s="";for(;r<arguments.length;)(u=arguments[r++])&&(c=Ch(u))&&(s&&(s+=" "),s+=c);return s}const Ch=r=>{if(typeof r=="string")return r;let u,c="";for(let s=0;s<r.length;s++)r[s]&&(u=Ch(r[s]))&&(c&&(c+=" "),c+=u);return c};function Q0(r,...u){let c,s,o,d=m;function m(y){const g=u.reduce((O,R)=>R(O),r());return c=G0(g),s=c.cache.get,o=c.cache.set,d=p,p(y)}function p(y){const g=s(y);if(g)return g;const O=V0(y,c);return o(y,O),O}return function(){return d(X0.apply(null,arguments))}}const Qt=r=>{const u=c=>c[r]||[];return u.isThemeGetter=!0,u},Uh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Lh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Z0=/^\d+\/\d+$/,K0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,J0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,W0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,F0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Bl=r=>Z0.test(r),it=r=>!!r&&!Number.isNaN(Number(r)),Ra=r=>!!r&&Number.isInteger(Number(r)),Dc=r=>r.endsWith("%")&&it(r.slice(0,-1)),ta=r=>K0.test(r),P0=()=>!0,I0=r=>J0.test(r)&&!$0.test(r),Hh=()=>!1,ty=r=>W0.test(r),ey=r=>F0.test(r),ay=r=>!X(r)&&!Q(r),ly=r=>ql(r,kh,Hh),X=r=>Uh.test(r),Ja=r=>ql(r,Gh,I0),Cc=r=>ql(r,cy,it),xh=r=>ql(r,Bh,Hh),ny=r=>ql(r,qh,ey),su=r=>ql(r,Yh,ty),Q=r=>Lh.test(r),Yn=r=>kl(r,Gh),iy=r=>kl(r,ry),Sh=r=>kl(r,Bh),uy=r=>kl(r,kh),sy=r=>kl(r,qh),cu=r=>kl(r,Yh,!0),ql=(r,u,c)=>{const s=Uh.exec(r);return s?s[1]?u(s[1]):c(s[2]):!1},kl=(r,u,c=!1)=>{const s=Lh.exec(r);return s?s[1]?u(s[1]):c:!1},Bh=r=>r==="position"||r==="percentage",qh=r=>r==="image"||r==="url",kh=r=>r==="length"||r==="size"||r==="bg-size",Gh=r=>r==="length",cy=r=>r==="number",ry=r=>r==="family-name",Yh=r=>r==="shadow",oy=()=>{const r=Qt("color"),u=Qt("font"),c=Qt("text"),s=Qt("font-weight"),o=Qt("tracking"),d=Qt("leading"),m=Qt("breakpoint"),p=Qt("container"),y=Qt("spacing"),g=Qt("radius"),O=Qt("shadow"),R=Qt("inset-shadow"),B=Qt("text-shadow"),q=Qt("drop-shadow"),U=Qt("blur"),Z=Qt("perspective"),F=Qt("aspect"),bt=Qt("ease"),ft=Qt("animate"),st=()=>["auto","avoid","all","avoid-page","page","left","right","column"],ot=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],J=()=>[...ot(),Q,X],xt=()=>["auto","hidden","clip","visible","scroll"],mt=()=>["auto","contain","none"],G=()=>[Q,X,y],et=()=>[Bl,"full","auto",...G()],dt=()=>[Ra,"none","subgrid",Q,X],wt=()=>["auto",{span:["full",Ra,Q,X]},Ra,Q,X],Dt=()=>[Ra,"auto",Q,X],Bt=()=>["auto","min","max","fr",Q,X],Ct=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Rt=()=>["start","end","center","stretch","center-safe","end-safe"],z=()=>["auto",...G()],H=()=>[Bl,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],C=()=>[r,Q,X],ht=()=>[...ot(),Sh,xh,{position:[Q,X]}],b=()=>["no-repeat",{repeat:["","x","y","space","round"]}],w=()=>["auto","cover","contain",uy,ly,{size:[Q,X]}],k=()=>[Dc,Yn,Ja],L=()=>["","none","full",g,Q,X],Y=()=>["",it,Yn,Ja],lt=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],pt=()=>[it,Dc,Sh,xh],At=()=>["","none",U,Q,X],re=()=>["none",it,Q,X],aa=()=>["none",it,Q,X],la=()=>[it,Q,X],na=()=>[Bl,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[ta],breakpoint:[ta],color:[P0],container:[ta],"drop-shadow":[ta],ease:["in","out","in-out"],font:[ay],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[ta],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[ta],shadow:[ta],spacing:["px",it],text:[ta],"text-shadow":[ta],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Bl,X,Q,F]}],container:["container"],columns:[{columns:[it,X,Q,p]}],"break-after":[{"break-after":st()}],"break-before":[{"break-before":st()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:J()}],overflow:[{overflow:xt()}],"overflow-x":[{"overflow-x":xt()}],"overflow-y":[{"overflow-y":xt()}],overscroll:[{overscroll:mt()}],"overscroll-x":[{"overscroll-x":mt()}],"overscroll-y":[{"overscroll-y":mt()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:et()}],"inset-x":[{"inset-x":et()}],"inset-y":[{"inset-y":et()}],start:[{start:et()}],end:[{end:et()}],top:[{top:et()}],right:[{right:et()}],bottom:[{bottom:et()}],left:[{left:et()}],visibility:["visible","invisible","collapse"],z:[{z:[Ra,"auto",Q,X]}],basis:[{basis:[Bl,"full","auto",p,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[it,Bl,"auto","initial","none",X]}],grow:[{grow:["",it,Q,X]}],shrink:[{shrink:["",it,Q,X]}],order:[{order:[Ra,"first","last","none",Q,X]}],"grid-cols":[{"grid-cols":dt()}],"col-start-end":[{col:wt()}],"col-start":[{"col-start":Dt()}],"col-end":[{"col-end":Dt()}],"grid-rows":[{"grid-rows":dt()}],"row-start-end":[{row:wt()}],"row-start":[{"row-start":Dt()}],"row-end":[{"row-end":Dt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Bt()}],"auto-rows":[{"auto-rows":Bt()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...Ct(),"normal"]}],"justify-items":[{"justify-items":[...Rt(),"normal"]}],"justify-self":[{"justify-self":["auto",...Rt()]}],"align-content":[{content:["normal",...Ct()]}],"align-items":[{items:[...Rt(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Rt(),{baseline:["","last"]}]}],"place-content":[{"place-content":Ct()}],"place-items":[{"place-items":[...Rt(),"baseline"]}],"place-self":[{"place-self":["auto",...Rt()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:z()}],mx:[{mx:z()}],my:[{my:z()}],ms:[{ms:z()}],me:[{me:z()}],mt:[{mt:z()}],mr:[{mr:z()}],mb:[{mb:z()}],ml:[{ml:z()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:H()}],w:[{w:[p,"screen",...H()]}],"min-w":[{"min-w":[p,"screen","none",...H()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[m]},...H()]}],h:[{h:["screen","lh",...H()]}],"min-h":[{"min-h":["screen","lh","none",...H()]}],"max-h":[{"max-h":["screen","lh",...H()]}],"font-size":[{text:["base",c,Yn,Ja]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,Q,Cc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Dc,X]}],"font-family":[{font:[iy,X,u]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,Q,X]}],"line-clamp":[{"line-clamp":[it,"none",Q,Cc]}],leading:[{leading:[d,...G()]}],"list-image":[{"list-image":["none",Q,X]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,X]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...lt(),"wavy"]}],"text-decoration-thickness":[{decoration:[it,"from-font","auto",Q,Ja]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[it,"auto",Q,X]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ht()}],"bg-repeat":[{bg:b()}],"bg-size":[{bg:w()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Ra,Q,X],radial:["",Q,X],conic:[Ra,Q,X]},sy,ny]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:k()}],"gradient-via-pos":[{via:k()}],"gradient-to-pos":[{to:k()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:Y()}],"border-w-x":[{"border-x":Y()}],"border-w-y":[{"border-y":Y()}],"border-w-s":[{"border-s":Y()}],"border-w-e":[{"border-e":Y()}],"border-w-t":[{"border-t":Y()}],"border-w-r":[{"border-r":Y()}],"border-w-b":[{"border-b":Y()}],"border-w-l":[{"border-l":Y()}],"divide-x":[{"divide-x":Y()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":Y()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...lt(),"hidden","none"]}],"divide-style":[{divide:[...lt(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...lt(),"none","hidden"]}],"outline-offset":[{"outline-offset":[it,Q,X]}],"outline-w":[{outline:["",it,Yn,Ja]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",O,cu,su]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",R,cu,su]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:Y()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[it,Ja]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":Y()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",B,cu,su]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[it,Q,X]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[it]}],"mask-image-linear-from-pos":[{"mask-linear-from":pt()}],"mask-image-linear-to-pos":[{"mask-linear-to":pt()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":pt()}],"mask-image-t-to-pos":[{"mask-t-to":pt()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":pt()}],"mask-image-r-to-pos":[{"mask-r-to":pt()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":pt()}],"mask-image-b-to-pos":[{"mask-b-to":pt()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":pt()}],"mask-image-l-to-pos":[{"mask-l-to":pt()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":pt()}],"mask-image-x-to-pos":[{"mask-x-to":pt()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":pt()}],"mask-image-y-to-pos":[{"mask-y-to":pt()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[Q,X]}],"mask-image-radial-from-pos":[{"mask-radial-from":pt()}],"mask-image-radial-to-pos":[{"mask-radial-to":pt()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":ot()}],"mask-image-conic-pos":[{"mask-conic":[it]}],"mask-image-conic-from-pos":[{"mask-conic-from":pt()}],"mask-image-conic-to-pos":[{"mask-conic-to":pt()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ht()}],"mask-repeat":[{mask:b()}],"mask-size":[{mask:w()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,X]}],filter:[{filter:["","none",Q,X]}],blur:[{blur:At()}],brightness:[{brightness:[it,Q,X]}],contrast:[{contrast:[it,Q,X]}],"drop-shadow":[{"drop-shadow":["","none",q,cu,su]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",it,Q,X]}],"hue-rotate":[{"hue-rotate":[it,Q,X]}],invert:[{invert:["",it,Q,X]}],saturate:[{saturate:[it,Q,X]}],sepia:[{sepia:["",it,Q,X]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,X]}],"backdrop-blur":[{"backdrop-blur":At()}],"backdrop-brightness":[{"backdrop-brightness":[it,Q,X]}],"backdrop-contrast":[{"backdrop-contrast":[it,Q,X]}],"backdrop-grayscale":[{"backdrop-grayscale":["",it,Q,X]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[it,Q,X]}],"backdrop-invert":[{"backdrop-invert":["",it,Q,X]}],"backdrop-opacity":[{"backdrop-opacity":[it,Q,X]}],"backdrop-saturate":[{"backdrop-saturate":[it,Q,X]}],"backdrop-sepia":[{"backdrop-sepia":["",it,Q,X]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,X]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[it,"initial",Q,X]}],ease:[{ease:["linear","initial",bt,Q,X]}],delay:[{delay:[it,Q,X]}],animate:[{animate:["none",ft,Q,X]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[Z,Q,X]}],"perspective-origin":[{"perspective-origin":J()}],rotate:[{rotate:re()}],"rotate-x":[{"rotate-x":re()}],"rotate-y":[{"rotate-y":re()}],"rotate-z":[{"rotate-z":re()}],scale:[{scale:aa()}],"scale-x":[{"scale-x":aa()}],"scale-y":[{"scale-y":aa()}],"scale-z":[{"scale-z":aa()}],"scale-3d":["scale-3d"],skew:[{skew:la()}],"skew-x":[{"skew-x":la()}],"skew-y":[{"skew-y":la()}],transform:[{transform:[Q,X,"","none","gpu","cpu"]}],"transform-origin":[{origin:J()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:na()}],"translate-x":[{"translate-x":na()}],"translate-y":[{"translate-y":na()}],"translate-z":[{"translate-z":na()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,X]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,X]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[it,Yn,Ja,Cc]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},fy=Q0(oy);function dy(...r){return fy(jh(r))}const hy=w0("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Vh({className:r,variant:u,size:c,asChild:s=!1,...o}){const d=s?E0:"button";return S.jsx(d,{"data-slot":"button",className:dy(hy({variant:u,size:c,className:r})),...o})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gy=r=>r.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),my=r=>r.replace(/^([A-Z])|[\s-_]+(\w)/g,(u,c,s)=>s?s.toUpperCase():c.toLowerCase()),_h=r=>{const u=my(r);return u.charAt(0).toUpperCase()+u.slice(1)},Xh=(...r)=>r.filter((u,c,s)=>!!u&&u.trim()!==""&&s.indexOf(u)===c).join(" ").trim(),py=r=>{for(const u in r)if(u.startsWith("aria-")||u==="role"||u==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var yy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vy=Ot.forwardRef(({color:r="currentColor",size:u=24,strokeWidth:c=2,absoluteStrokeWidth:s,className:o="",children:d,iconNode:m,...p},y)=>Ot.createElement("svg",{ref:y,...yy,width:u,height:u,stroke:r,strokeWidth:s?Number(c)*24/Number(u):c,className:Xh("lucide",o),...!d&&!py(p)&&{"aria-hidden":"true"},...p},[...m.map(([g,O])=>Ot.createElement(g,O)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=(r,u)=>{const c=Ot.forwardRef(({className:s,...o},d)=>Ot.createElement(vy,{ref:d,iconNode:u,className:Xh(`lucide-${gy(_h(r))}`,`lucide-${r}`,s),...o}));return c.displayName=_h(r),c};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const by=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],xy=we("arrow-right",by);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sy=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],Qh=we("award",Sy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],Oy=we("globe",_y);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]],Ey=we("graduation-cap",Ny);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],Zh=we("heart",Ty);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ay=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],zy=we("mail",Ay);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Kh=we("map-pin",Ry);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],wy=we("phone",My);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]],Dy=we("scale",jy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],Xc=we("shield",Cy);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Ly=we("users",Uy),Hy=()=>{const{t:r,i18n:u}=ea(),c=()=>{const s=u.language==="en"?"zh":"en";u.changeLanguage(s)};return S.jsx("header",{className:"bg-white shadow-sm border-b",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between items-center py-4",children:[S.jsx("div",{className:"flex items-center",children:S.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:r("company_name")})}),S.jsxs("nav",{className:"hidden md:flex space-x-8",children:[S.jsx("a",{href:"#home",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_home")}),S.jsx("a",{href:"#about",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_about")}),S.jsx("a",{href:"#values",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_values")}),S.jsx("a",{href:"#factories",className:"text-gray-700 hover:text-gray-900 transition-colors",children:"Factories"}),S.jsx("a",{href:"#charity",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_charity")}),S.jsx("a",{href:"#contact",className:"text-gray-700 hover:text-gray-900 transition-colors",children:r("nav_contact")})]}),S.jsxs(Vh,{variant:"outline",size:"sm",onClick:c,className:"flex items-center gap-2",children:[S.jsx(Oy,{className:"h-4 w-4"}),r("language_switch")]})]})})})},By=()=>{const{t:r}=ea();return S.jsx("section",{id:"home",className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"text-center",children:[S.jsx("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:r("hero_title")}),S.jsx("p",{className:"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto",children:r("hero_subtitle")}),S.jsx("p",{className:"text-lg text-gray-500 mb-10 max-w-2xl mx-auto",children:r("tagline")}),S.jsxs(Vh,{size:"lg",className:"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3",children:[r("hero_cta"),S.jsx(xy,{className:"ml-2 h-5 w-5"})]})]})})})},qy=()=>{const{t:r}=ea();return S.jsx("section",{id:"about",className:"py-20 bg-white",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsxs("div",{className:"text-center mb-16",children:[S.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("about_title")}),S.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:r("about_content")})]}),S.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[S.jsx("div",{children:S.jsx("div",{className:"bg-gray-100 rounded-lg h-64 flex items-center justify-center",children:S.jsx("span",{className:"text-gray-500 text-lg",children:"Company Image"})})}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"flex items-start space-x-4",children:[S.jsx("div",{className:"bg-blue-100 rounded-full p-3",children:S.jsx("div",{className:"w-6 h-6 bg-blue-600 rounded-full"})}),S.jsxs("div",{children:[S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Ethical Foundation"}),S.jsx("p",{className:"text-gray-600",children:"Built on unwavering ethical principles and human-centric values."})]})]}),S.jsxs("div",{className:"flex items-start space-x-4",children:[S.jsx("div",{className:"bg-green-100 rounded-full p-3",children:S.jsx("div",{className:"w-6 h-6 bg-green-600 rounded-full"})}),S.jsxs("div",{children:[S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Social Impact"}),S.jsx("p",{className:"text-gray-600",children:"Creating positive impact on society, environment, and employee lives."})]})]}),S.jsxs("div",{className:"flex items-start space-x-4",children:[S.jsx("div",{className:"bg-purple-100 rounded-full p-3",children:S.jsx("div",{className:"w-6 h-6 bg-purple-600 rounded-full"})}),S.jsxs("div",{children:[S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Quality Excellence"}),S.jsx("p",{className:"text-gray-600",children:"Delivering superior products through ethical production practices."})]})]})]})]})]})})},ky=()=>{const{t:r}=ea(),u=[{icon:Zh,title:r("values_labor_title"),content:r("values_labor_content"),color:"text-red-600 bg-red-100"},{icon:Xc,title:r("values_cotton_title"),content:r("values_cotton_content"),color:"text-blue-600 bg-blue-100"},{icon:Dy,title:r("values_compliance_title"),content:r("values_compliance_content"),color:"text-green-600 bg-green-100"},{icon:Qh,title:r("values_quality_title"),content:r("values_quality_content"),color:"text-purple-600 bg-purple-100"}];return S.jsx("section",{id:"values",className:"py-20 bg-gray-50",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsx("div",{className:"text-center mb-16",children:S.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("values_title")})}),S.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:u.map((c,s)=>{const o=c.icon;return S.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow",children:[S.jsx("div",{className:`inline-flex p-3 rounded-full ${c.color} mb-4`,children:S.jsx(o,{className:"h-6 w-6"})}),S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:c.title}),S.jsx("p",{className:"text-gray-600 leading-relaxed",children:c.content})]},s)})})]})})},Gy=()=>{const{t:r}=ea(),u=[{id:"shenzhen",name:r("factory_shenzhen"),description:r("factory_shenzhen_desc"),coordinates:{x:75,y:45},workers:"850+",certifications:["ISO 14001","SA8000","WRAP"],color:"bg-blue-600"},{id:"dongguan",name:r("factory_dongguan"),description:r("factory_dongguan_desc"),coordinates:{x:73,y:48},workers:"1,200+",certifications:["ISO 14001","OEKO-TEX"],color:"bg-green-600"},{id:"guangzhou",name:r("factory_guangzhou"),description:r("factory_guangzhou_desc"),coordinates:{x:70,y:50},workers:"600+",certifications:["ISO 9001","SA8000"],color:"bg-purple-600"},{id:"vietnam",name:r("factory_vietnam"),description:r("factory_vietnam_desc"),coordinates:{x:65,y:70},workers:"400+",certifications:["WRAP","BSCI"],color:"bg-red-600"}];return S.jsx("section",{id:"factories",className:"py-20 bg-gray-50",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsxs("div",{className:"text-center mb-16",children:[S.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:r("factory_title")}),S.jsx("p",{className:"text-xl text-gray-600 mb-6",children:r("factory_subtitle")}),S.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:r("factory_intro")})]}),S.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-12",children:S.jsx("div",{className:"relative",children:S.jsxs("svg",{viewBox:"0 0 400 300",className:"w-full h-96 bg-blue-50 rounded-lg",style:{maxHeight:"400px"},children:[S.jsx("path",{d:"M80 40 L180 35 L190 50 L185 80 L170 90 L150 95 L120 90 L100 85 L85 70 L80 40 Z",fill:"#e5e7eb",stroke:"#9ca3af",strokeWidth:"1"}),S.jsx("path",{d:"M140 120 L150 115 L155 140 L150 180 L145 185 L140 180 L135 150 L140 120 Z",fill:"#e5e7eb",stroke:"#9ca3af",strokeWidth:"1"}),u.map(c=>S.jsxs("g",{children:[S.jsx("circle",{cx:c.coordinates.x*2,cy:c.coordinates.y*2,r:"8",className:`${c.color.replace("bg-","fill-")} cursor-pointer hover:opacity-80 transition-opacity`,stroke:"white",strokeWidth:"2"}),S.jsx("text",{x:c.coordinates.x*2,y:c.coordinates.y*2-15,textAnchor:"middle",className:"text-xs font-semibold fill-gray-700",children:c.name.split(",")[0]})]},c.id)),S.jsxs("g",{transform:"translate(20, 20)",children:[S.jsx("rect",{x:"0",y:"0",width:"120",height:"80",fill:"white",stroke:"#e5e7eb",rx:"5"}),S.jsx("text",{x:"10",y:"15",className:"text-xs font-semibold fill-gray-700",children:"Factory Locations"}),S.jsx("circle",{cx:"15",cy:"30",r:"4",className:"fill-blue-600"}),S.jsx("text",{x:"25",y:"35",className:"text-xs fill-gray-600",children:"Technology Hub"}),S.jsx("circle",{cx:"15",cy:"45",r:"4",className:"fill-green-600"}),S.jsx("text",{x:"25",y:"50",className:"text-xs fill-gray-600",children:"Textile Manufacturing"}),S.jsx("circle",{cx:"15",cy:"60",r:"4",className:"fill-purple-600"}),S.jsx("text",{x:"25",y:"65",className:"text-xs fill-gray-600",children:"Design Center"})]})]})})}),S.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:u.map(c=>S.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow",children:[S.jsxs("div",{className:"flex items-center mb-4",children:[S.jsx("div",{className:`p-3 rounded-full ${c.color} text-white mr-4`,children:S.jsx(Kh,{className:"h-6 w-6"})}),S.jsx("div",{children:S.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:c.name})})]}),S.jsx("p",{className:"text-gray-600 mb-4 text-sm leading-relaxed",children:c.description}),S.jsxs("div",{className:"space-y-3",children:[S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx(Ly,{className:"h-4 w-4 text-gray-500 mr-2"}),S.jsxs("span",{className:"text-gray-700",children:[c.workers," Workers"]})]}),S.jsxs("div",{className:"flex items-start text-sm",children:[S.jsx(Qh,{className:"h-4 w-4 text-gray-500 mr-2 mt-0.5"}),S.jsxs("div",{children:[S.jsx("span",{className:"text-gray-700 block",children:"Certifications:"}),S.jsx("div",{className:"flex flex-wrap gap-1 mt-1",children:c.certifications.map((s,o)=>S.jsx("span",{className:"bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs",children:s},o))})]})]}),S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx(Xc,{className:"h-4 w-4 text-green-500 mr-2"}),S.jsx("span",{className:"text-green-600 font-medium",children:"Fully Compliant"})]})]})]},c.id))}),S.jsxs("div",{className:"mt-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white text-center",children:[S.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Our Commitment to Ethical Manufacturing"}),S.jsx("p",{className:"text-lg mb-6 max-w-4xl mx-auto",children:"Every facility in our global network operates under the strictest ethical standards, ensuring worker dignity, fair compensation, and safe working conditions."}),S.jsxs("div",{className:"grid md:grid-cols-3 gap-6 text-center",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-3xl font-bold mb-2",children:"3,050+"}),S.jsx("div",{className:"text-blue-100",children:"Total Workers Protected"})]}),S.jsxs("div",{children:[S.jsx("div",{className:"text-3xl font-bold mb-2",children:"100%"}),S.jsx("div",{className:"text-blue-100",children:"Compliance Rate"})]}),S.jsxs("div",{children:[S.jsx("div",{className:"text-3xl font-bold mb-2",children:"0"}),S.jsx("div",{className:"text-blue-100",children:"Forced Labor Incidents"})]})]})]}),S.jsx("div",{className:"mt-12 text-center",children:S.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto",children:[S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:r("whitepaper_title")}),S.jsx("p",{className:"text-gray-600 mb-6",children:r("whitepaper_description")}),S.jsxs("a",{href:"/sparkle_industry_whitepaper.html",target:"_blank",className:"inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors",children:[r("whitepaper_download"),S.jsx("svg",{className:"ml-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})]})]})})]})})},Yy=()=>{const{t:r}=ea(),u=[{icon:Ey,title:r("charity_education_title"),content:r("charity_education_content"),color:"text-blue-600 bg-blue-100"},{icon:Zh,title:r("charity_health_title"),content:r("charity_health_content"),color:"text-red-600 bg-red-100"},{icon:Xc,title:r("charity_protection_title"),content:r("charity_protection_content"),color:"text-green-600 bg-green-100"}],c=[{number:"2,500+",label:r("charity_impact_children")},{number:"50+",label:r("charity_impact_schools")},{number:"15",label:r("charity_impact_programs")}];return S.jsx("section",{id:"charity",className:"py-20 bg-white",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsxs("div",{className:"text-center mb-16",children:[S.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:r("charity_title")}),S.jsx("p",{className:"text-xl text-gray-600 mb-6",children:r("charity_subtitle")}),S.jsx("p",{className:"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed",children:r("charity_intro")})]}),S.jsx("div",{className:"grid md:grid-cols-3 gap-8 mb-16",children:u.map((s,o)=>{const d=s.icon;return S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:`inline-flex p-4 rounded-full ${s.color} mb-6`,children:S.jsx(d,{className:"h-8 w-8"})}),S.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:s.title}),S.jsx("p",{className:"text-gray-600 leading-relaxed",children:s.content})]},o)})}),S.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-8 text-white",children:[S.jsx("div",{className:"text-center mb-8",children:S.jsx("h3",{className:"text-2xl font-bold mb-2",children:r("charity_impact_title")})}),S.jsx("div",{className:"grid md:grid-cols-3 gap-8 text-center",children:c.map((s,o)=>S.jsxs("div",{children:[S.jsx("div",{className:"text-3xl md:text-4xl font-bold mb-2",children:s.number}),S.jsx("div",{className:"text-blue-100",children:s.label})]},o))})]})]})})},Vy=()=>{const{t:r}=ea();return S.jsx("section",{id:"contact",className:"py-20 bg-gray-50",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsx("div",{className:"text-center mb-16",children:S.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:r("contact_title")})}),S.jsxs("div",{className:"grid md:grid-cols-3 gap-8",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"inline-flex p-4 rounded-full bg-blue-100 text-blue-600 mb-4",children:S.jsx(Kh,{className:"h-6 w-6"})}),S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_address")}),S.jsxs("p",{className:"text-gray-600",children:["123 Business District",S.jsx("br",{}),"Shanghai, China 200000"]})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"inline-flex p-4 rounded-full bg-green-100 text-green-600 mb-4",children:S.jsx(wy,{className:"h-6 w-6"})}),S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_phone")}),S.jsx("p",{className:"text-gray-600",children:"+86 21 1234 5678"})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"inline-flex p-4 rounded-full bg-purple-100 text-purple-600 mb-4",children:S.jsx(zy,{className:"h-6 w-6"})}),S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:r("contact_email")}),S.jsx("p",{className:"text-gray-600",children:"<EMAIL>"})]})]})]})})},Xy=()=>{const{t:r}=ea();return S.jsx("footer",{className:"bg-gray-900 text-white py-12",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"text-center",children:[S.jsx("h3",{className:"text-2xl font-bold mb-4",children:r("company_name")}),S.jsx("p",{className:"text-gray-400 mb-6 max-w-2xl mx-auto",children:r("footer_values")}),S.jsx("div",{className:"border-t border-gray-800 pt-6",children:S.jsx("p",{className:"text-gray-400",children:r("footer_copyright")})})]})})})};function Qy(){const{i18n:r}=ea();return S.jsxs("div",{className:"min-h-screen",children:[S.jsx(Hy,{}),S.jsx(By,{}),S.jsx(qy,{}),S.jsx(ky,{}),S.jsx(Gy,{}),S.jsx(Yy,{}),S.jsx(Vy,{}),S.jsx(Xy,{})]})}Ep.createRoot(document.getElementById("root")).render(S.jsx(Ot.StrictMode,{children:S.jsx(Qy,{})}));
