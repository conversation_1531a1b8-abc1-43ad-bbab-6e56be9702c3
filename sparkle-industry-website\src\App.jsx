import { useTranslation } from 'react-i18next';
import Header from './components/Header';
import Hero from './components/Hero';
import About from './components/About';
import Values from './components/Values';
import FactoryMap from './components/FactoryMap';
import Partners from './components/Partners';
import Charity from './components/Charity';
import Contact from './components/Contact';
import Footer from './components/Footer';
import './App.css';

function App() {
  const { i18n } = useTranslation();

  return (
    <div className="min-h-screen">
      <Header />
      <Hero />
      <About />
      <Values />
      <FactoryMap />
      <Partners />
      <Charity />
      <Contact />
      <Footer />
    </div>
  );
}

export default App;

